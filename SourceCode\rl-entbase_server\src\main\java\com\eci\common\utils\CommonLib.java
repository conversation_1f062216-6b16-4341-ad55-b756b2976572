package com.eci.common.utils;

import com.eci.common.ZsrDateUtils;

import javax.sql.rowset.CachedRowSet;
import java.sql.ResultSet;
import java.util.Date;
import java.util.List;

/**
 * 通用工具库
 */
public class CommonLib {
    private static volatile CommonLib instance;
    private static final Object lock = new Object();

    /**
     * 获取单例实例
     */
    public static CommonLib getInstance() {
        if (instance == null) {
            synchronized (lock) {
                if (instance == null) {
                    instance = new CommonLib();
                }
            }
        }
        return instance;
    }

    private CommonLib() {
    }

    /**
     * SQL字符串转义
     */
    public String SQLQ(String key) {
        if (key == null) {
            return "NULL";
        }
        return "'" + key.replace("'", "''") + "'";
    }

    /**
     * SQL字符串转义
     */
    public String SQLQ(Date key) {
        if (key == null) {
            return "NULL";
        }

        return "'" + ZsrDateUtils.dateToString(key) + "'";
    }

    public String SQLQ(int key) {
        return String.valueOf(key);
    }

    public String SQLQL(String key) {
        return SQLQ("%" + key + "%");
    }

    /**
     * SQL字符串转义并添加逗号
     */
    public String sqlQC(String key) {
        return SQLQ(key) + ",";
    }


    public String sqlQC(int key) {
        return SQLQ(key) + ",";
    }


    /**
     * 模糊查询 - 包含
     */
    public String sqlQL(String key) {
        return SQLQ("%" + key + "%");
    }

    public String sqlQL(int key) {
        return SQLQ("%" + key + "%");
    }

    public String sqlQL(float key) {
        return SQLQ("%" + key + "%");
    }

    public String sqlQL(double key) {
        return SQLQ("%" + key + "%");
    }

    public String sqlQL(Date key) {
        return SQLQ("%" + key.toString() + "%");
    }

    /**
     * 模糊查询 - 以指定内容结束
     */
    public String sqlQE(String key) {
        return SQLQ("%" + key);
    }

    public String sqlQE(int key) {
        return SQLQ("%" + key);
    }

    public String sqlQE(float key) {
        return SQLQ("%" + key);
    }

    public String sqlQE(double key) {
        return SQLQ("%" + key);
    }

    public String sqlQE(Date key) {
        return SQLQ("%" + key.toString());
    }

    /**
     * 模糊查询 - 以指定内容开始
     */
    public String sqlQS(String key) {
        return SQLQ(key + "%");
    }

    public String sqlQS(int key) {
        return SQLQ(key + "%");
    }

    public String sqlQS(float key) {
        return SQLQ(key + "%");
    }

    public String sqlQS(double key) {
        return SQLQ(key + "%");
    }

    public String sqlQS(Date key) {
        return SQLQ(key.toString() + "%");
    }

    /**
     * 检查数据集是否为空
     */
    public boolean checkEOF(ResultSet rs) {
        try {
            return rs != null && !rs.isBeforeFirst() && rs.next();
        } catch (Exception e) {
            return false;
        }
    }

    public boolean checkEOF(CachedRowSet crs) {
        try {
            return crs != null && !crs.isBeforeFirst() && crs.next();
        } catch (Exception e) {
            return false;
        }
    }

    public boolean checkEOF(List<?> list) {
        return list != null && !list.isEmpty();
    }

    /**
     * 检查数据集是否为空
     */
    public boolean isEmpty(ResultSet rs) {
        return !checkEOF(rs);
    }

    public boolean isEmpty(CachedRowSet crs) {
        return !checkEOF(crs);
    }

    public boolean isEmpty(List<?> list) {
        return !checkEOF(list);
    }


}

