package com.eci.common.utils.ExportUtils;

import com.eci.crud.entity.EciExcelExportAo;
import com.eci.excel.config.ExportConfig;
import io.swagger.annotations.ApiModelProperty;

import java.lang.reflect.Field;
import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * <Description>TODO <br>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/3/17$
 */
public class ExcelProcess {
    public static ExportConfig BuildConfig(String fileName,Class<?> clazz){
        List<EciExcelExportAo> eciExcelExportAos=BuildAos(clazz);
        ExportConfig exportConfig=new ExportConfig(fileName,eciExcelExportAos);
        return exportConfig;
    }

    private static List<EciExcelExportAo> BuildAos(Class<?> clazz){
        List<EciExcelExportAo> list=new ArrayList<>();
        Field[] fields = clazz.getDeclaredFields();
        Map<Integer,EciExcelExportAo> maps=new TreeMap<>();
        for (Field field : fields) {
            EciExcelExportAo exportAo=new EciExcelExportAo();
            Excel annotation = field.getAnnotation(Excel.class);
            if (annotation != null) {
                exportAo.setColumnCode(field.getName());
                exportAo.setColumnName(annotation.value());
                maps.put(annotation.order(),exportAo);
            }
        }
        list= maps.values().stream().collect(Collectors.toList());
        return list;
    }
}
