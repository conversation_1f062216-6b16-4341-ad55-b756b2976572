package com.eci.common;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class Log {
    private static final Logger logger = LoggerFactory.getLogger(Log.class);

    public static void Info(String message, String tag) {
        logger.info("[" + tag + "] " + message);
    }

    public static void info(String message, String tag) {
        logger.info("[" + tag + "] " + message);
    }

    public static void Write(String message, String tag) {
        logger.info("[" + tag + "] " + message);
    }

    public static void write(String message, String tag) {
        logger.info("[" + tag + "] " + message);
    }

    public static void Error(String message, Throwable throwable) {
        logger.error(message, throwable);
    }

    public static void error(String message, Throwable throwable) {
        logger.error(message, throwable);
    }
}