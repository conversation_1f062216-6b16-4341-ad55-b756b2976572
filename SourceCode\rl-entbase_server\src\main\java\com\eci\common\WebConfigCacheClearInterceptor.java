package com.eci.common;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * 拦截器配置
 */
@Configuration
public class WebConfigCacheClearInterceptor implements WebMvcConfigurer {

    @Autowired
    private DictCacheClearInterceptor cacheClearInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(cacheClearInterceptor)
                .addPathPatterns("/**"); // 可以指定拦截所有路径或具体路径
    }
}