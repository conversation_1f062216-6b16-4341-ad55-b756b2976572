SET search_path = lhxtd;
CREATE TABLE SYS_OPER_LOG (
  ID bigint NOT NULL,
  TITLE varchar(100),
  BUSINESS_TYPE smallint,
  OPER_URL varchar(255),
  METHOD varchar(255),
  REQUEST_METHOD varchar(10),
  OPER_IP varchar(15),
  OPER_LOCATION varchar(100),
  OPER_STATUS smallint,
  ERROR_MSG text,
  OPER_TIME timestamp without time zone,
  OPER_PARAM text,
  JSON_RESULT text,
  USER_ID varchar(64),
  LOGIN_NAME varchar(64),
  OPER_OS varchar(20),
  BROWSER varchar(50),
  SERVER varchar(20)
)
WITH (ORIENTATION = ROW, COMPRESSION = NO, FILLFACTOR = 80);
ALTER TABLE SYS_OPER_LOG ADD CONSTRAINT SYS_OPER_LOG_PKEY PRIMARY KEY (ID);

SET search_path TO lhxtd;

CREATE TABLE SYS_DATA_HELP (
    ID bigint NOT NULL,
    QUERY_KEY varchar(200),
    QUERY_TYPE character(1),
    SQL_COMMAND varchar(2000),
    BASE_COMMENT varchar(800),
    SYS varchar(30),
    CREATE_TIME timestamp without time zone,
    UPDATE_TIME timestamp without time zone,
    USE_CACHE character(1) DEFAULT 'N'::bpchar,
    LANGUAGE_TYPE varchar(10),
    DATA_CHANGE_CODE varchar(50),
    DATA_CHANGE_NAME varchar(50),
    TG_SORT_FIELD varchar(50),
    CREATE_USER varchar(50),
    UPDATE_USER varchar(50),
    CODE_MEMO varchar(50),
    NAME_MEMO varchar(50),
    COMPARE_TYPE varchar(20),
    CHOOSE_SHOW varchar(2000),
    TG_QUERY_FIELD varchar(100),
    AUTO_UPPER character(1),
    QUERY_MODE varchar(20),
    STATUS varchar(2),
    ECI_LOCK character(1),
    CONVERT_SQL varchar(2000),
    PL_VERSION varchar(200),
    NEED_DOWNLOAD character(1),
    MEMO_DETAIL varchar(800),
    ADMIN_LOCK character(1),
    ASSEMBLY_NAME varchar(400),
    FILTER character(1),
    LAYOUT varchar(200),
    EDIT_URL varchar(255),
    CONFIG varchar(255),
    WIDTH varchar(20),
    HEIGHT varchar(20),
    QUERY_DATA varchar(255),
    TG_PAGE_SIZE varchar(20)
)
WITH (orientation=row, compression=no, fillfactor=80);
ALTER TABLE SYS_DATA_HELP ADD CONSTRAINT UK_SYS_DATA_HELP1 UNIQUE (QUERY_KEY, SYS, LANGUAGE_TYPE);
ALTER TABLE SYS_DATA_HELP ADD CONSTRAINT SYS_DATA_HELP_PKEY PRIMARY KEY (ID);

CREATE TABLE SYS_USER_INFO (
   ID bigint NOT NULL,
   USER_ID varchar(50) NOT NULL,
   LOGIN_NAME varchar(30) NOT NULL,
   TRUE_NAME varchar(30) DEFAULT NULL,
   COMPANY_CODE varchar(20) NOT NULL,
   COMPANY_NAME varchar(255) NOT NULL,
   CUST_CODE varchar(4) DEFAULT NULL,
   CUST_NAME varchar(20) DEFAULT NULL,
   USER_NICKNAME varchar(30) DEFAULT NULL,
   USER_IMG varchar(255) DEFAULT NULL,
   USER_SEX varchar(1) DEFAULT '0',
   LOGIN_COUNT integer DEFAULT 0,
   LOGIN_TIME timestamp without time zone,
   LOGIN_LAST_TIME timestamp without time zone,
   CLIENT_IP varchar(30) DEFAULT NULL,
   CLIENT_PROVINCE varchar(30) DEFAULT NULL,
   CLIENT_CITY varchar(30) DEFAULT NULL,
   CLIENT_BROWSER varchar(30) DEFAULT NULL,
   CLIENT_OS varchar(30) DEFAULT NULL,
   USER_TOKEN varchar(255) DEFAULT NULL,
   BIND_PHONE_NO varchar(20) DEFAULT NULL,
   WEB_SIDE_TYPE varchar(1) DEFAULT '2',
   WEB_HEAD_TYPE varchar(1) DEFAULT '3',
   WEB_THEME varchar(10) DEFAULT '#409eff',
   WEB_LAYOUT varchar(1) DEFAULT '3',
   WEB_SIDE_IS_ICON varchar(1) DEFAULT '1',
   WEB_SIDE_IS_OPEN varchar(1) DEFAULT '1',
   WEB_IS_TAB varchar(1) DEFAULT '1',
   WEB_TAB_TYPE varchar(1) DEFAULT '1',
   MAIL_ADDRESS varchar(255) DEFAULT NULL,
   PWD_LAST_UPDATE_TIME timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
   DEV varchar(1) DEFAULT '0'
)
WITH (orientation=row, compression=no, fillfactor=80);
ALTER TABLE SYS_USER_INFO ADD CONSTRAINT SYS_USER_INFO_LOGIN_NAME_KEY UNIQUE (LOGIN_NAME);
ALTER TABLE SYS_USER_INFO ADD CONSTRAINT SYS_USER_INFO_USER_ID_KEY UNIQUE (USER_ID);
ALTER TABLE SYS_USER_INFO ADD CONSTRAINT SYS_USER_INFO_PKEY PRIMARY KEY (ID);


CREATE TABLE SYS_USER_LOGIN_LOG (
    ID bigint NOT NULL ,
    USER_ID varchar(50) DEFAULT NULL,
    LOGIN_NAME varchar(30) DEFAULT NULL,
    SYS_CODE varchar(30) DEFAULT NULL,
    CLIENT_IP varchar(30) DEFAULT NULL,
    CLIENT_PROVINCE varchar(30) DEFAULT NULL,
    CLIENT_CITY varchar(30) DEFAULT NULL,
    CLIENT_BROWSER varchar(30) DEFAULT NULL,
    CLIENT_OS varchar(30) DEFAULT NULL,
    LOGIN_TIME timestamp DEFAULT NULL,
    LOGIN_STATUS int DEFAULT NULL,
    LOGIN_DESCRIPTION varchar(255) DEFAULT NULL,
    ACTION_PARAMS text,
    PRIMARY KEY (ID)
);

CREATE TABLE SYS_ENTERPRISE_INFO (
 ID bigint NOT NULL ,
 COMPANY_CODE varchar(20) NOT NULL,
 COMPANY_NAME varchar(255) NOT NULL,
 EP_SCCD varchar(18) DEFAULT NULL,
 CUST_CODE varchar(4) DEFAULT NULL,
 CUST_NAME varchar(20) DEFAULT NULL,
 CONTACTS varchar(100) DEFAULT NULL,
 CONTACTS_TEL varchar(100) DEFAULT NULL,
 MAIL_ADDRESS varchar(255) DEFAULT NULL,
 EP_ADDRESS varchar(255) DEFAULT NULL,
 EP_LOGO_URL varchar(255) DEFAULT NULL,
 EP_SYS_NAME varchar(255) DEFAULT NULL,
 REMARK varchar(255) DEFAULT NULL,
 CREATE_TIME timestamp DEFAULT CURRENT_TIMESTAMP,
 UPDATE_TIME timestamp DEFAULT CURRENT_TIMESTAMP,

 PRIMARY KEY (ID),
 UNIQUE (COMPANY_CODE),
 UNIQUE (EP_SCCD)
);

CREATE TABLE SYS_TABLE_SETTING (
   ID bigint NOT NULL,
   USER_ID varchar(36) DEFAULT NULL,
   SETTING_TYPE varchar(1) DEFAULT NULL,
   TABLE_CODE varchar(255) DEFAULT NULL,
   JSON_DETAIL text,
   CREATE_TIME timestamp,

   PRIMARY KEY (ID),
   UNIQUE (USER_ID, SETTING_TYPE, TABLE_CODE)
);
COMMENT ON TABLE SYS_TABLE_SETTING IS '页面列表设置信息';
COMMENT ON COLUMN SYS_TABLE_SETTING.ID IS '主键';
COMMENT ON COLUMN SYS_TABLE_SETTING.USER_ID IS '用户id';
COMMENT ON COLUMN SYS_TABLE_SETTING.SETTING_TYPE IS '设置类型|1筛选项2表单项';
COMMENT ON COLUMN SYS_TABLE_SETTING.TABLE_CODE IS '表名';
COMMENT ON COLUMN SYS_TABLE_SETTING.JSON_DETAIL IS 'json明细';
COMMENT ON COLUMN SYS_TABLE_SETTING.CREATE_TIME IS '创建时间';

CREATE TABLE SYS_QUICK_MY_COLLECTION (
 ID bigint NOT NULL,
 USER_ID varchar(50),
 LOGIN_NAME varchar(30),
 TRUE_NAME varchar(50),
 MENU_ID varchar(50),
 MENU_NAME varchar(100),
 MENU_ROUTE varchar(100),
 REMARK varchar(255),
 CREATE_USER varchar(50),
 CREATE_TIME timestamp DEFAULT CURRENT_TIMESTAMP,
 UPDATE_USER varchar(50),
 UPDATE_TIME timestamp DEFAULT CURRENT_TIMESTAMP,

 PRIMARY KEY (ID)
);
COMMENT ON TABLE SYS_QUICK_MY_COLLECTION IS '';
COMMENT ON COLUMN SYS_QUICK_MY_COLLECTION.ID IS '自增主键';
COMMENT ON COLUMN SYS_QUICK_MY_COLLECTION.USER_ID IS '权限平台用户ID';
COMMENT ON COLUMN SYS_QUICK_MY_COLLECTION.LOGIN_NAME IS '用户名(权限平台登录用户名)';
COMMENT ON COLUMN SYS_QUICK_MY_COLLECTION.TRUE_NAME IS '真实姓名';
COMMENT ON COLUMN SYS_QUICK_MY_COLLECTION.MENU_ID IS '菜单编号';
COMMENT ON COLUMN SYS_QUICK_MY_COLLECTION.MENU_NAME IS '菜单名称';
COMMENT ON COLUMN SYS_QUICK_MY_COLLECTION.MENU_ROUTE IS '菜单路由地址';
COMMENT ON COLUMN SYS_QUICK_MY_COLLECTION.REMARK IS '备注(图标)';
COMMENT ON COLUMN SYS_QUICK_MY_COLLECTION.CREATE_USER IS '创建人';
COMMENT ON COLUMN SYS_QUICK_MY_COLLECTION.CREATE_TIME IS '创建时间';
COMMENT ON COLUMN SYS_QUICK_MY_COLLECTION.UPDATE_USER IS '修改人';
COMMENT ON COLUMN SYS_QUICK_MY_COLLECTION.UPDATE_TIME IS '修改时间';

CREATE TABLE SYS_MONITOR_API (
 ID bigint NOT NULL,
 APP_NAME varchar(50) DEFAULT NULL,
 URI varchar(255) DEFAULT NULL,
 RESP_TIME integer DEFAULT NULL,
 RESP_STATUS integer DEFAULT NULL,
 CREATE_TIME timestamp,
 PRIMARY KEY (ID)
);

COMMENT ON TABLE SYS_MONITOR_API IS 'API性能监控';
COMMENT ON COLUMN SYS_MONITOR_API.ID IS '主键';
COMMENT ON COLUMN SYS_MONITOR_API.APP_NAME IS '系统名称';
COMMENT ON COLUMN SYS_MONITOR_API.URI IS '接口地址';
COMMENT ON COLUMN SYS_MONITOR_API.RESP_TIME IS '响应时长(毫秒)';
COMMENT ON COLUMN SYS_MONITOR_API.RESP_STATUS IS '返回状态:0正常 1异常';
COMMENT ON COLUMN SYS_MONITOR_API.CREATE_TIME IS '创建时间';

CREATE TABLE SYS_MONITOR_DB (
ID bigint NOT NULL DEFAULT nextval('sys_monitor_db_id_seq'::regclass) COMMENT '主键ID',
PERCENT numeric(10,2) DEFAULT NULL,
CREATE_TIME timestamp,
M_TYPE varchar(1) DEFAULT NULL,
PRIMARY KEY (ID)
);
COMMENT ON TABLE SYS_MONITOR_DB IS '数据库服务器监控';
COMMENT ON COLUMN SYS_MONITOR_DB.ID IS '主键ID';
COMMENT ON COLUMN SYS_MONITOR_DB.PERCENT IS '百分比';
COMMENT ON COLUMN SYS_MONITOR_DB.CREATE_TIME IS '插入时间';
COMMENT ON COLUMN SYS_MONITOR_DB.M_TYPE IS '监控类型: 1cpu 2内存 3磁盘'

CREATE TABLE SYS_MONITOR_SQL (
 ID bigint NOT NULL,
 URI varchar(255) DEFAULT NULL,
 SQL_STR text,
 SQL_TIME integer DEFAULT NULL,
 CREATE_TIME timestamp,
 PRIMARY KEY (ID)
);
COMMENT ON TABLE SYS_MONITOR_SQL IS 'API性能监控';
COMMENT ON COLUMN SYS_MONITOR_SQL.ID IS '主键';
COMMENT ON COLUMN SYS_MONITOR_SQL.URI IS '接口地址';
COMMENT ON COLUMN SYS_MONITOR_SQL.SQL_STR IS 'SQL语句';
COMMENT ON COLUMN SYS_MONITOR_SQL.SQL_TIME IS 'SQL时长(毫秒)';
COMMENT ON COLUMN SYS_MONITOR_SQL.CREATE_TIME IS '创建时间';

CREATE TABLE SYS_CACHE_HELP (
ID bigint NOT NULL,
QUERY_KEY varchar(100),
STATUS varchar(2),
SQL_COMMAND varchar(2000),
BASE_COMMENT varchar(255),
MEMO varchar(400),
MEMO_DETAIL varchar(400),
SYS varchar(30),
LANGUAGE_TYPE varchar(10),
CREATE_TIME timestamp,
UPDATE_TIME timestamp,
CREATE_USER varchar(50),
UPDATE_USER varchar(50),
PL_VERSION varchar(100),
DB_VERSION varchar(100),
PRIMARY KEY (ID),
UNIQUE (QUERY_KEY, SYS, LANGUAGE_TYPE)
);
COMMENT ON TABLE SYS_CACHE_HELP IS '缓存参数维护';
COMMENT ON COLUMN SYS_CACHE_HELP.ID IS '自增主键';
COMMENT ON COLUMN SYS_CACHE_HELP.QUERY_KEY IS '查询key';
COMMENT ON COLUMN SYS_CACHE_HELP.STATUS IS '状态-----保留';
COMMENT ON COLUMN SYS_CACHE_HELP.SQL_COMMAND IS 'SQL';
COMMENT ON COLUMN SYS_CACHE_HELP.BASE_COMMENT IS '备注';
COMMENT ON COLUMN SYS_CACHE_HELP.MEMO IS 'MEMO';
COMMENT ON COLUMN SYS_CACHE_HELP.MEMO_DETAIL IS 'MEMO_DETAIL';
COMMENT ON COLUMN SYS_CACHE_HELP.SYS IS '使用系统';
COMMENT ON COLUMN SYS_CACHE_HELP.LANGUAGE_TYPE IS '语言类型';
COMMENT ON COLUMN SYS_CACHE_HELP.CREATE_TIME IS '创建时间';
COMMENT ON COLUMN SYS_CACHE_HELP.UPDATE_TIME IS '更新时间';
COMMENT ON COLUMN SYS_CACHE_HELP.CREATE_USER IS '创建人';
COMMENT ON COLUMN SYS_CACHE_HELP.UPDATE_USER IS '修改人';
COMMENT ON COLUMN SYS_CACHE_HELP.PL_VERSION IS '当前缓存版本';
COMMENT ON COLUMN SYS_CACHE_HELP.DB_VERSION IS '当前DB库版本';