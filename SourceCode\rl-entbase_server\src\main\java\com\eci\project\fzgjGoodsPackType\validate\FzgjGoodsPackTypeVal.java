package com.eci.project.fzgjGoodsPackType.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.fzgjGoodsPackType.entity.FzgjGoodsPackTypeEntity;

import org.springframework.stereotype.Service;


/**
* 包装类型Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-06-26
*/
@Service
public class FzgjGoodsPackTypeVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(FzgjGoodsPackTypeEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(FzgjGoodsPackTypeEntity entity, BusinessType businessType) {

    }

}
