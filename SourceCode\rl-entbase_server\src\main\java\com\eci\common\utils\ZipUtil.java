package com.eci.common.utils;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * <Description>压缩工具类</description>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/23
 */
public class ZipUtil {

    /**
     * 将多个文件压缩成一个 zip 文件并保存到指定路径
     *
     * @param files    要压缩的文件列表
     * @param filePath 压缩包输出路径
     * @throws IOException IO 异常
     */
    public static void Compression(List<File> files, String filePath) throws IOException {
        byte[] zipBytes = CompressionToBytes(files);
        Files.write(Paths.get(filePath), zipBytes);
    }

    /**
     * 将多个文件压缩为字节流
     *
     * @param files 要压缩的文件列表
     * @return 压缩后的字节流
     * @throws IOException IO 异常
     */
    public static byte[] CompressionToBytes(List<File> files) throws IOException {
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        ZipOutputStream zos = new ZipOutputStream(bos);

        for (File file : files) {
            FileInputStream fis = new FileInputStream(file);
            ZipEntry zipEntry = new ZipEntry(file.getName());
            zos.putNextEntry(zipEntry);

            byte[] bytes = new byte[1024];
            int length;
            while ((length = fis.read(bytes)) >= 0) {
                zos.write(bytes, 0, length);
            }
            zos.closeEntry();
            fis.close();
        }

        zos.close();
        return bos.toByteArray();
    }
}