package com.eci.common.cache;

import com.eci.common.cache.config.CacheConfigDetector;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * CacheConfigDetector 单元测试
 */
@ExtendWith(MockitoExtension.class)
public class CacheConfigDetectorTest {

    @Mock
    private RedisConnectionFactory redisConnectionFactory;

    @Mock
    private RedisConnection redisConnection;

    private CacheConfigDetector detector;

    @BeforeEach
    void setUp() {
        detector = new CacheConfigDetector(redisConnectionFactory);
    }

    @Test
    void testAutoStrategyWithRedisAvailable() {
        // 设置配置
        ReflectionTestUtils.setField(detector, "redisHost", "localhost");
        ReflectionTestUtils.setField(detector, "redisPort", 6379);
        ReflectionTestUtils.setField(detector, "cacheStrategy", "auto");

        // Mock Redis 连接成功
        when(redisConnectionFactory.getConnection()).thenReturn(redisConnection);
        when(redisConnection.ping()).thenReturn("PONG");

        // 执行检测
        detector.detectCacheStrategy();

        // 验证结果
        assertEquals(CacheConfigDetector.CacheStrategy.REDIS, detector.getCurrentStrategy());
        assertTrue(detector.isRedisAvailable());
    }

    @Test
    void testAutoStrategyWithRedisUnavailable() {
        // 设置配置
        ReflectionTestUtils.setField(detector, "redisHost", "localhost");
        ReflectionTestUtils.setField(detector, "redisPort", 6379);
        ReflectionTestUtils.setField(detector, "cacheStrategy", "auto");

        // Mock Redis 连接失败
        when(redisConnectionFactory.getConnection()).thenThrow(new RuntimeException("Connection failed"));

        // 执行检测
        detector.detectCacheStrategy();

        // 验证结果
        assertEquals(CacheConfigDetector.CacheStrategy.LOCAL, detector.getCurrentStrategy());
        assertFalse(detector.isRedisAvailable());
    }

    @Test
    void testForceRedisStrategy() {
        // 设置配置
        ReflectionTestUtils.setField(detector, "redisHost", "localhost");
        ReflectionTestUtils.setField(detector, "redisPort", 6379);
        ReflectionTestUtils.setField(detector, "cacheStrategy", "redis");

        // Mock Redis 连接成功
        when(redisConnectionFactory.getConnection()).thenReturn(redisConnection);
        when(redisConnection.ping()).thenReturn("PONG");

        // 执行检测
        detector.detectCacheStrategy();

        // 验证结果
        assertEquals(CacheConfigDetector.CacheStrategy.REDIS, detector.getCurrentStrategy());
        assertTrue(detector.isRedisAvailable());
    }

    @Test
    void testForceRedisStrategyWithConnectionFailure() {
        // 设置配置
        ReflectionTestUtils.setField(detector, "redisHost", "localhost");
        ReflectionTestUtils.setField(detector, "redisPort", 6379);
        ReflectionTestUtils.setField(detector, "cacheStrategy", "redis");

        // Mock Redis 连接失败
        when(redisConnectionFactory.getConnection()).thenThrow(new RuntimeException("Connection failed"));

        // 执行检测
        detector.detectCacheStrategy();

        // 验证结果 - 强制 Redis 失败时应降级到本地缓存
        assertEquals(CacheConfigDetector.CacheStrategy.LOCAL, detector.getCurrentStrategy());
        assertFalse(detector.isRedisAvailable());
    }

    @Test
    void testForceLocalStrategy() {
        // 设置配置
        ReflectionTestUtils.setField(detector, "cacheStrategy", "local");

        // 执行检测
        detector.detectCacheStrategy();

        // 验证结果
        assertEquals(CacheConfigDetector.CacheStrategy.LOCAL, detector.getCurrentStrategy());
        assertFalse(detector.isRedisAvailable());

        // 验证没有尝试连接 Redis
        verify(redisConnectionFactory, never()).getConnection();
    }

    @Test
    void testIncompleteRedisConfig() {
        // 设置不完整的配置
        ReflectionTestUtils.setField(detector, "redisHost", "");
        ReflectionTestUtils.setField(detector, "redisPort", 6379);
        ReflectionTestUtils.setField(detector, "cacheStrategy", "auto");

        // 执行检测
        detector.detectCacheStrategy();

        // 验证结果
        assertEquals(CacheConfigDetector.CacheStrategy.LOCAL, detector.getCurrentStrategy());
        assertFalse(detector.isRedisAvailable());

        // 验证没有尝试连接 Redis
        verify(redisConnectionFactory, never()).getConnection();
    }

    @Test
    void testRedisConnectionTimeout() {
        // 设置配置
        ReflectionTestUtils.setField(detector, "redisHost", "localhost");
        ReflectionTestUtils.setField(detector, "redisPort", 6379);
        ReflectionTestUtils.setField(detector, "cacheStrategy", "auto");
        ReflectionTestUtils.setField(detector, "redisConnectionTestTimeout", 1000L);

        // Mock 慢连接
        when(redisConnectionFactory.getConnection()).thenAnswer(invocation -> {
            Thread.sleep(1500); // 超过超时时间
            return redisConnection;
        });
        when(redisConnection.ping()).thenReturn("PONG");

        // 执行检测
        long startTime = System.currentTimeMillis();
        detector.detectCacheStrategy();
        long duration = System.currentTimeMillis() - startTime;

        // 验证超时处理
        assertTrue(duration < 2000); // 应该在超时时间内返回
        assertEquals(CacheConfigDetector.CacheStrategy.LOCAL, detector.getCurrentStrategy());
    }

    @Test
    void testForceRedetect() {
        // 初始设置为本地缓存
        ReflectionTestUtils.setField(detector, "redisHost", "");
        ReflectionTestUtils.setField(detector, "cacheStrategy", "auto");
        detector.detectCacheStrategy();
        assertEquals(CacheConfigDetector.CacheStrategy.LOCAL, detector.getCurrentStrategy());

        // 更新配置为有效的 Redis 配置
        ReflectionTestUtils.setField(detector, "redisHost", "localhost");
        ReflectionTestUtils.setField(detector, "redisPort", 6379);

        // Mock Redis 连接成功
        when(redisConnectionFactory.getConnection()).thenReturn(redisConnection);
        when(redisConnection.ping()).thenReturn("PONG");

        // 强制重新检测
        detector.forceRedetect();

        // 验证策略已更新
        assertEquals(CacheConfigDetector.CacheStrategy.REDIS, detector.getCurrentStrategy());
        assertTrue(detector.isRedisAvailable());
    }

    @Test
    void testGetCacheConfig() {
        // 设置配置
        ReflectionTestUtils.setField(detector, "redisHost", "test-host");
        ReflectionTestUtils.setField(detector, "redisPort", 6380);
        ReflectionTestUtils.setField(detector, "redisDatabase", 5);
        ReflectionTestUtils.setField(detector, "redisPassword", "test-password");
        ReflectionTestUtils.setField(detector, "localCacheMaxSize", 2000L);
        ReflectionTestUtils.setField(detector, "localCacheExpireMinutes", 90L);

        // Mock Redis 连接成功
        when(redisConnectionFactory.getConnection()).thenReturn(redisConnection);
        when(redisConnection.ping()).thenReturn("PONG");

        detector.detectCacheStrategy();

        // 获取配置
        CacheConfigDetector.CacheConfig config = detector.getCacheConfig();

        // 验证配置
        assertNotNull(config);
        assertEquals(CacheConfigDetector.CacheStrategy.REDIS, config.getStrategy());
        assertEquals("test-host", config.getRedisHost());
        assertEquals(6380, config.getRedisPort());
        assertEquals(5, config.getRedisDatabase());
        assertEquals("test-password", config.getRedisPassword());
        assertEquals(2000L, config.getLocalCacheMaxSize());
        assertEquals(90L, config.getLocalCacheExpireMinutes());
        assertTrue(config.isRedisAvailable());
    }

    @Test
    void testPeriodicRedetection() throws InterruptedException {
        // 设置配置
        ReflectionTestUtils.setField(detector, "redisHost", "localhost");
        ReflectionTestUtils.setField(detector, "redisPort", 6379);
        ReflectionTestUtils.setField(detector, "cacheStrategy", "auto");

        // 初始检测 - Redis 不可用
        when(redisConnectionFactory.getConnection()).thenThrow(new RuntimeException("Connection failed"));
        detector.detectCacheStrategy();
        assertEquals(CacheConfigDetector.CacheStrategy.LOCAL, detector.getCurrentStrategy());

        // 模拟时间过去，Redis 变为可用
        ReflectionTestUtils.setField(detector, "lastRedisCheckTime", 
                System.currentTimeMillis() - TimeUnit.MINUTES.toMillis(6));
        
        when(redisConnectionFactory.getConnection()).thenReturn(redisConnection);
        when(redisConnection.ping()).thenReturn("PONG");

        // 再次获取策略应该触发重新检测
        CacheConfigDetector.CacheStrategy strategy = detector.getCurrentStrategy();

        // 验证策略已更新
        assertEquals(CacheConfigDetector.CacheStrategy.REDIS, strategy);
        assertTrue(detector.isRedisAvailable());
    }

    @Test
    void testRedisConnectionFactoryNull() {
        // 创建没有 RedisConnectionFactory 的检测器
        CacheConfigDetector nullFactoryDetector = new CacheConfigDetector(null);
        
        ReflectionTestUtils.setField(nullFactoryDetector, "redisHost", "localhost");
        ReflectionTestUtils.setField(nullFactoryDetector, "redisPort", 6379);
        ReflectionTestUtils.setField(nullFactoryDetector, "cacheStrategy", "auto");

        // 执行检测
        nullFactoryDetector.detectCacheStrategy();

        // 验证结果
        assertEquals(CacheConfigDetector.CacheStrategy.LOCAL, nullFactoryDetector.getCurrentStrategy());
        assertFalse(nullFactoryDetector.isRedisAvailable());
    }

    @Test
    void testCacheConfigToString() {
        // 设置配置
        ReflectionTestUtils.setField(detector, "redisHost", "test-host");
        ReflectionTestUtils.setField(detector, "redisPort", 6379);
        ReflectionTestUtils.setField(detector, "cacheStrategy", "auto");

        when(redisConnectionFactory.getConnection()).thenReturn(redisConnection);
        when(redisConnection.ping()).thenReturn("PONG");

        detector.detectCacheStrategy();
        CacheConfigDetector.CacheConfig config = detector.getCacheConfig();

        // 验证 toString 方法
        String configString = config.toString();
        assertNotNull(configString);
        assertTrue(configString.contains("REDIS"));
        assertTrue(configString.contains("test-host"));
        assertTrue(configString.contains("6379"));
        assertTrue(configString.contains("true"));
    }
}
