package com.eci.common;

import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.temporal.TemporalAccessor;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

public class ZsrDateUtils {

    // 私有构造函数，防止实例化
    private ZsrDateUtils() {
        throw new IllegalStateException("Utility class");
    }

    // 定义支持的日期时间格式列表，顺序很重要，从最具体到最通用
    private static final List<DateTimeFormatter> FORMATTERS = new ArrayList<>();

    static {
        // ISO 8601 格式 (带毫秒和时区Z) - 例如: 2025-03-31T16:00:00.000Z
        FORMATTERS.add(DateTimeFormatter.ISO_INSTANT);

        // ISO 8601 格式 (带毫秒和时区偏移) - 例如: 2025-03-31T16:00:00.000+08:00
        FORMATTERS.add(DateTimeFormatter.ISO_OFFSET_DATE_TIME);

        // ISO 8601 格式 (不带时区) - 例如: 2025-03-31T16:00:00
        FORMATTERS.add(DateTimeFormatter.ISO_LOCAL_DATE_TIME);

        // 常用格式 - 带毫秒
        FORMATTERS.add(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS"));
        FORMATTERS.add(DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss.SSS"));
        FORMATTERS.add(DateTimeFormatter.ofPattern("MM/dd/yyyy HH:mm:ss.SSS"));
        FORMATTERS.add(DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm:ss.SSS"));

        // 常用格式 - 不带毫秒
        FORMATTERS.add(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        FORMATTERS.add(DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss"));
        FORMATTERS.add(DateTimeFormatter.ofPattern("MM/dd/yyyy HH:mm:ss"));
        FORMATTERS.add(DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm:ss"));

        // 常用格式 - 日期 only
        FORMATTERS.add(DateTimeFormatter.ISO_LOCAL_DATE); // 例如: 2025-03-31
        FORMATTERS.add(DateTimeFormatter.ofPattern("yyyy/MM/dd"));
        FORMATTERS.add(DateTimeFormatter.ofPattern("MM/dd/yyyy"));
        FORMATTERS.add(DateTimeFormatter.ofPattern("dd/MM/yyyy"));
    }

    /**
     * 将字符串解析为 java.util.Date 对象，尝试多种预定义格式。
     * 如果字符串为 null 或空，或者所有格式都无法匹配解析，则返回 null。
     * 对于不包含时区信息的格式 (如 yyyy-MM-dd HH:mm:ss)，默认使用系统时区进行转换。
     *
     * @param dateString 要解析的日期时间字符串
     * @return 解析成功的 java.util.Date 对象，或 null 如果解析失败
     */
    public static Date stringToDate(String dateString) {
        if (dateString == null || dateString.trim().isEmpty()) {
            return null;
        }

        String trimmedDateString = dateString.trim();

        for (DateTimeFormatter formatter : FORMATTERS) {
            try {
                // 尝试用当前 formatter 解析字符串
                TemporalAccessor accessor = formatter.parse(trimmedDateString);

                // 尝试从解析结果中获取 Instant。
                // Instant 代表时间线上的一个瞬时点，通常是转换为 java.util.Date 的最佳中间类型。
                // 如果 accessor 包含时区信息 (如 ISO_INSTANT, ISO_OFFSET_DATE_TIME)，可以直接转为 Instant。
                // 如果 accessor 是 LocalDateTime (不含时区)，会抛出 DateTimeException。
                try {
                    Instant instant = Instant.from(accessor);
                    return Date.from(instant); // 成功解析并转换为 Instant，返回 Date
                } catch (java.time.DateTimeException e2) {
                    // 如果无法直接获取 Instant (可能是 LocalDateTime 或 LocalDate)
                    // 尝试将其解析为 LocalDateTime (需要日期和时间字段)
                    try {
                        LocalDateTime ldt = LocalDateTime.from(accessor);
                        // 将 LocalDateTime 结合系统默认时区转换为 Instant
                        Instant instant = ldt.atZone(ZoneId.systemDefault()).toInstant();
                        return Date.from(instant); // 成功解析并转换为 Date
                    } catch (java.time.DateTimeException e3) {
                        // 如果也无法解析为 LocalDateTime (可能是 LocalDate only)
                        // 尝试将其解析为 LocalDate，并使用当天开始时间结合系统默认时区转换
                        try {
                            java.time.LocalDate ld = java.time.LocalDate.from(accessor);
                            LocalDateTime ldt = ld.atStartOfDay();
                            Instant instant = ldt.atZone(ZoneId.systemDefault()).toInstant();
                            return Date.from(instant); // 成功解析并转换为 Date
                        } catch (java.time.DateTimeException e4) {
                            // 如果以上转换都失败，说明当前 formatter 解析的 accessor
                            // 无法有效转换为 java.util.Date，继续尝试下一个 formatter。
                            continue;
                        }
                    }
                }
            } catch (DateTimeParseException e) {
                // 当前 formatter 不匹配，继续尝试下一个
                continue;
            }
        }

        // 所有格式都尝试失败
        return null;
    }


    /**
     * 将Date类型的时间转换为指定格式的字符串
     *
     * @param date 需要转换的Date对象
     * @param format 日期格式，例如 "yyyy-MM-dd HH:mm:ss"
     * @return 格式化后的字符串
     */
    public static String dateToString(Date date, String format) {
        if (date == null) {
            return null;
        }
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        return sdf.format(date);
    }

    /**
     * 将Date类型的时间转换为 "yyyy-MM-dd HH:mm:ss" 格式的字符串
     *
     * @param date 需要转换的Date对象
     * @return 格式化后的字符串
     */
    public static String dateToString(Date date) {
        return dateToString(date, "yyyy-MM-dd HH:mm:ss");
    }

    /**
     * 判断日期是否小于今天
     *
     * @param date 需要判断的时间
     * @return true:小于今天，false:大于等于今天
     */
    public static boolean isBeforeTodayWithLocalDate(Date date) {
        LocalDate today = LocalDate.now();
        LocalDate compareDate = date.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDate();

        return compareDate.isBefore(today);
    }

    static Calendar calendar = Calendar.getInstance();

    /**
     * 把一个年月日的时间，加上时分秒：时分秒为：23:59:59 查询的时候使用
     *
     * @param yearMonthDD 一个包含年月日的日期
     * @return
     */
    public static Date parseYearMonthDDmmHHss(Date yearMonthDD) {
        calendar.setTime(yearMonthDD);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        return calendar.getTime();
    }

    /**
     * 接受一个整数作为参数，表示年份和季度（例如：202501代表2025年第1季度），并返回该季度的时间段（包括时分秒的Date类型）
     *
     * @param yearQuarter 202501代表2025年第1季度
     * @return
     */
    public static Date[] getQuarterDateRange(int yearQuarter) {
        int year = yearQuarter / 100;  // 获取年份
        int quarter = yearQuarter % 100;  // 获取季度

        LocalDateTime startDateTime = null;
        LocalDateTime endDateTime = null;

        // 根据季度决定时间段
        switch (quarter) {
            case 1:
                startDateTime = LocalDateTime.of(year, Month.JANUARY, 1, 0, 0, 0, 0);  // 1月1日00:00:00
                endDateTime = LocalDateTime.of(year, Month.MARCH, 31, 23, 59, 59, 999999999);  // 3月31日23:59:59.999999999
                break;
            case 2:
                startDateTime = LocalDateTime.of(year, Month.APRIL, 1, 0, 0, 0, 0);  // 4月1日00:00:00
                endDateTime = LocalDateTime.of(year, Month.JUNE, 30, 23, 59, 59, 999999999);  // 6月30日23:59:59.999999999
                break;
            case 3:
                startDateTime = LocalDateTime.of(year, Month.JULY, 1, 0, 0, 0, 0);  // 7月1日00:00:00
                endDateTime = LocalDateTime.of(year, Month.SEPTEMBER, 30, 23, 59, 59, 999999999);  // 9月30日23:59:59.999999999
                break;
            case 4:
                startDateTime = LocalDateTime.of(year, Month.OCTOBER, 1, 0, 0, 0, 0);  // 10月1日00:00:00
                endDateTime = LocalDateTime.of(year, Month.DECEMBER, 31, 23, 59, 59, 999999999);  // 12月31日23:59:59.999999999
                break;
            default:
                throw new IllegalArgumentException("Invalid quarter number. It must be between 1 and 4.");
        }

        // 转换为java.util.Date
        Date startDate = java.sql.Timestamp.valueOf(startDateTime);
        Date endDate = java.sql.Timestamp.valueOf(endDateTime);

        return new Date[]{startDate, endDate};
    }

    /**
     * 接受一个整数作为参数，表示年份和季度（例如：202501代表2025年第1季度），并返回该季度的时间段（包括时分秒的Date类型）
     *
     * @param yearQuarter 202501代表2025年第1季度
     * @return
     */
    public static String[] getQuarterDateRangeX(int yearQuarter) {
        int year = yearQuarter / 100;  // 获取年份
        int quarter = yearQuarter % 100;  // 获取季度

        LocalDateTime startDateTime = null;
        LocalDateTime endDateTime = null;

        // 根据季度决定时间段
        switch (quarter) {
            case 1:
                startDateTime = LocalDateTime.of(year, Month.JANUARY, 1, 0, 0, 0, 0);  // 1月1日00:00:00
                endDateTime = LocalDateTime.of(year, Month.MARCH, 31, 23, 59, 59, 999999999);  // 3月31日23:59:59.999999999
                break;
            case 2:
                startDateTime = LocalDateTime.of(year, Month.APRIL, 1, 0, 0, 0, 0);  // 4月1日00:00:00
                endDateTime = LocalDateTime.of(year, Month.JUNE, 30, 23, 59, 59, 999999999);  // 6月30日23:59:59.999999999
                break;
            case 3:
                startDateTime = LocalDateTime.of(year, Month.JULY, 1, 0, 0, 0, 0);  // 7月1日00:00:00
                endDateTime = LocalDateTime.of(year, Month.SEPTEMBER, 30, 23, 59, 59, 999999999);  // 9月30日23:59:59.999999999
                break;
            case 4:
                startDateTime = LocalDateTime.of(year, Month.OCTOBER, 1, 0, 0, 0, 0);  // 10月1日00:00:00
                endDateTime = LocalDateTime.of(year, Month.DECEMBER, 31, 23, 59, 59, 999999999);  // 12月31日23:59:59.999999999
                break;
            default:
                throw new IllegalArgumentException("Invalid quarter number. It must be between 1 and 4.");
        }

        // 转换为java.util.Date
        Date startDate = java.sql.Timestamp.valueOf(startDateTime);
        Date endDate = java.sql.Timestamp.valueOf(endDateTime);

        return new String[]{ZsrDateUtils.dateToString(startDate, "yyyyMM"), ZsrDateUtils.dateToString(endDate, "yyyyMM")};
    }

    /**
     * 根据季度编码获取季度月份列表
     *
     * @param quarterCode 季度编码，格式为 YYYYQQ，例如 201901 表示 2019年第一季度
     * @return 季度月份列表，例如 [201901, 201902, 201903]
     * @throws IllegalArgumentException 如果季度编码格式不正确或季度数字无效
     */
    public static List<String> getQuarterMonths(String quarterCode) {
        if (quarterCode == null || quarterCode.length() != 6) {
            throw new IllegalArgumentException("季度编码格式不正确，应为 YYYYQQ 格式，例如 201901");
        }

        String yearStr = quarterCode.substring(0, 4);
        String quarterStr = quarterCode.substring(4, 6);

        int year;
        int quarter;
        try {
            year = Integer.parseInt(yearStr);
            quarter = Integer.parseInt(quarterStr);
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("季度编码年份或季度数字格式不正确，应为数字");
        }

        if (quarter < 1 || quarter > 4) {
            throw new IllegalArgumentException("季度数字无效，应为 1-4 之间的数字");
        }

        List<String> months = new ArrayList<>();
        int startMonth = 0;
        int endMonth = 0;

        switch (quarter) {
            case 1:
                startMonth = 1;
                endMonth = 3;
                break;
            case 2:
                startMonth = 4;
                endMonth = 6;
                break;
            case 3:
                startMonth = 7;
                endMonth = 9;
                break;
            case 4:
                startMonth = 10;
                endMonth = 12;
                break;
        }

        for (int month = startMonth; month <= endMonth; month++) {
            months.add(String.format("%04d%02d", year, month));
        }

        return months;
    }

}
