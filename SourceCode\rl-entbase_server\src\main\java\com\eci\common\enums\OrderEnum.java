package com.eci.common.enums;

/**
 * @ClassName: OrderEnum
 * @Author: guangyan.mei
 * @Date: 2025/4/21 15:23
 * @Description: TODO
 */
public class OrderEnum {
    /**
     * 订单操作信息枚举
     */
    public enum OrderLogStatus {
        ZC("ZC", "订单创建"),
        SH("SH", "订单送审"),
        SX("SX", "订单生效"),
        WC("WC", "订单完成");

        private final String code;
        private final String description;

        OrderLogStatus(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        // 根据code获取对应的枚举值
        public static OrderStatus fromCode(String code) {
            for (OrderStatus status : OrderStatus.values()) {
                if (status.getCode().equals(code)) {
                    return status;
                }
            }
            throw new IllegalArgumentException("Unknown code: " + code);
        }

        @Override
        public String toString() {
            return "OrderStatus{" +
                    "code='" + code + '\'' +
                    ", description='" + description + '\'' +
                    '}';
        }
    }

    /**
     * 订单产生模式
     * <remark>在创建订单号的时候使用</remark>
     */
    public enum OrderConstants {
        ORDER_SOURCE_TEMPLATE('2', "新增订单模板"),
        ORDER_SOURCE_MANUAL('1', "手动新增订单"),
        ORDER_SOURCE_AUTO('0', "外部创建订单");
        // 值
        private final char code;
        // 描述
        private final String name;

        OrderConstants(char code, String name) {
            this.code = code;
            this.name = name;
        }

        // 获取对应取值
        public char getCode() {
            return code;
        }


        // 获取描述对应值
        public String getName() {
            return name;
        }
    }

    public enum ServiceItem {
        /**
         * 保税仓入库 (Bonded Warehouse Inbound)
         */
        BSCRK("400100100", "保税仓入库"),

        /**
         * 保税仓出库 (Bonded Warehouse Outbound)
         */
        BSCCK("400100200", "保税仓出库"),

        /**
         * 普仓入库 (General Warehouse Inbound)
         */
        PCRK("400200100", "普仓入库"),

        /**
         * 普仓入库 (General Warehouse Outbound)
         */
        PCCK("400200200", "普仓入库"), // Note: Description might be a typo in original, kept as is.

        /**
         * 库存增值服务 (Inventory Value-added Services)
         */
        KCZZFW("400300200", "库存增值服务"),

        /**
         * 库存属性转移 (Inventory Attribute Transfer)
         */
        KCSXZY("400300300", "库存属性转移"),

        /**
         * 库存仓储费周期结算 (Inventory Storage Fee Periodic Settlement)
         */
        KCCCDFZQJS("400300100", "库存仓储费周期结算"),

        /**
         * 库存仓储费周期结算 无作业 (Inventory Storage Fee Periodic Settlement No Operation)
         */
        KCCCDFZQJSWZY("400300110", "库存仓储费周期结算 无作业"),

        /**
         * 租赁仓储费结算单 (Leasing Storage Fee Settlement Document)
         */
        ZLCCFJS("400300210", "租赁仓储费结算单"),

        /**
         * 其他仓储费结算 (Other Storage Fee Settlement)
         */
        QTCCFJS("400300220", "其他仓储费结算"),

        /**
         * 国际公路 (International Road Transport)
         */
        TMS_GJGL("100200100", "国际公路"),

        /**
         * 国内公路 (Domestic Road Transport)
         */
        TMS_GNGL("200100", "国内公路"),

        /**
         * 国内公路短驳 (Domestic Road Transport Short Haul)
         */
        TMS_GNGLDB("200101", "国内公路短驳"),

        /**
         * 国内公路|简版 (Domestic Road Transport | Simple Version)
         */
        TMS_GNGLJB("200102", "国内公路|简版"),

        /**
         * 国内铁路 (Domestic Rail Transport)
         */
        TMS_GNTL("200110", "国内铁路"),

        /**
         * 国内水运 (Domestic Water Transport)
         */
        TMS_GNSY("200120", "国内水运"),

        /**
         * 国内空运 (Domestic Air Transport)
         */
        TMS_GNKY("200130", "国内空运"),

        /**
         * 国外公路运输 (International Road Transport)
         */
        TMS_GWGL("100100100", "国外公路运输"),

        /**
         * 国外铁路 (International Rail Transport)
         */
        TMS_GWTL("100100110", "国外铁路"),

        /**
         * 国外海运 (International Sea Transport)
         */
        TMS_GWHY("100100120", "国外海运"),

        /**
         * 国外空运 (International Air Transport)
         */
        TMS_GWKY("100100130", "国外空运"),

        /**
         * 国际铁路 (International Rail Transport)
         */
        TMS_GJTL("100200200", "国际铁路"),

        /**
         * 国际海运 (International Sea Transport)
         */
        TMS_GJHY("100200300", "国际海运"),

        /**
         * 国际海运进口 (International Sea Transport Import)
         */
        TMS_GJHYJK("100200310", "国际海运进口"),

        /**
         * 国际海运出口 (International Sea Transport Export)
         */
        TMS_GJHYCK("100200320", "国际海运出口"),

        /**
         * 国际空运 (International Air Transport)
         */
        TMS_GJKY("100200400", "国际空运"),

        /**
         * 国际空运进口 (International Air Transport Import)
         */
        TMS_GJKYJK("100200410", "国际空运进口"),

        /**
         * 国际空运出口 (International Air Transport Export)
         */
        TMS_GJKYCK("100200420", "国际空运出口"),

        /**
         * 国际陆运进口 (International Land Transport Import)
         */
        TMS_GJLYJK("100200110", "国际陆运进口"),

        /**
         * 国际陆运出口 (International Land Transport Export)
         */
        TMS_GJLYCK("100200120", "国际陆运出口"),

        /**
         * 国际多式联运 100200500 (International Multimodal Transport 100200500)
         */
        TMS_GJDSLY("100200500", "国际多式联运"),

        /**
         * 国际运输_ 100200600 (International Transport_ 100200600)
         */
        TMS_GJYS("100200600", "国际运输_"),

        /**
         * 国内运输_ 200300 (Domestic Transport_ 200300)
         */
        TMS_GNYS("200300", "国内运输_"),

        /**
         * 国外报关 (Overseas Customs Declaration)
         */
        GWBGBJ("100100200", "国外报关"), // Note: Original has duplicate key GWBGBJ with 800200, using the first one.

        /**
         * 国外证件代办 (Overseas Document Processing)
         */
        GWZJDB("100100300", "国外证件代办"),

        /**
         * 国内报关 (Domestic Customs Declaration)
         */
        GNBG("300100", "国内报关"),

        /**
         * 口岸清关 (Port Clearance)
         */
        KAQG("300100100", "口岸清关"),

        /**
         * 进口口岸清关 (Import Port Clearance)
         */
        JKKAQG("300100100100", "进口口岸清关"),

        /**
         * 出口口岸清关 (Export Port Clearance)
         */
        CKKAQG("300100100200", "出口口岸清关"),

        /**
         * 通关一体化 (Integrated Customs Clearance)
         */
        TGYTH("*********", "通关一体化"),

        /**
         * 进口通关一体化 (Import Integrated Customs Clearance)
         */
        JKYTH("*********100", "进口通关一体化"),

        /**
         * 出口通关一体化 (Export Integrated Customs Clearance)
         */
        CKYTH("*********200", "出口通关一体化"),

        /**
         * 后续补税 (Subsequent Tax Payment)
         */
        HXBS("*********", "后续补税"),

        /**
         * 国内报关报检-国内进出区-二线入区-出口进区-出口报关 (Domestic Customs Declaration & Inspection - Domestic Entry/Exit - Second Line Entry - Export Entry - Export Customs Declaration)
         */
        EXRQ_CKJQ_CKBG("300100400100001001", "国内报关报检-国内进出区-二线入区-出口进区-出口报关"),

        /**
         * 国内报关报检-进区报关 (Domestic Customs Declaration & Inspection - Entry Customs Declaration)
         */
        EXRQ_CKJQ_JQBG("300100400100001002", "国内报关报检-进区报关"),

        /**
         * 国内报关报检-出口报关 (Domestic Customs Declaration & Inspection - Export Customs Declaration)
         */
        EXRQ_JZBG_CKBG("300100400100002001", "国内报关报检-出口报关"),

        /**
         * 国内报关报检-进区报关 (Domestic Customs Declaration & Inspection - Entry Customs Declaration)
         */
        EXRQ_JZBG_JQBG("300100400100002002", "国内报关报检-进区报关"),

        /**
         * 国内报关报检-分批送货 (Domestic Customs Declaration & Inspection - Batch Delivery)
         */
        EXRQ_FPSH("300100400100003", "国内报关报检-分批送货"),

        /**
         * 国内报关报检-区区流转 (Domestic Customs Declaration & Inspection - Zone to Zone Transfer)
         */
        EXRQ_QQLZ("300100400100004", "国内报关报检-区区流转"),

        /**
         * 国内报关报检-卡口登记 (Domestic Customs Declaration & Inspection - Checkpoint Registration)
         */
        EXRQ_KKDJ("300100400100005", "国内报关报检-卡口登记"),

        /**
         * 国内报关报检-跨关区区区流转 (Domestic Customs Declaration & Inspection - Cross-Customs Zone Transfer)
         */
        EXRQ_KGQQQLZ("300100400100006", "国内报关报检-跨关区区区流转"),

        /**
         * 国内报关报检-二线出区-进口出区-出区报关 (Domestic Customs Declaration & Inspection - Second Line Exit - Import Exit - Exit Customs Declaration)
         */
        EXCQ_JKCQ_CQBG("300100400200001001", "国内报关报检-二线出区-进口出区-出区报关"),

        /**
         * 国内报关报检-进口报关 (Domestic Customs Declaration & Inspection - Import Customs Declaration)
         */
        EXCQ_JKCQ_JKBG("300100400200001002", "国内报关报检-进口报关"),

        /**
         * 国内报关报检-出区报关 (Domestic Customs Declaration & Inspection - Exit Customs Declaration)
         */
        EXCQ_JZBG_CQBG("300100400200002001", "国内报关报检-出区报关"),

        /**
         * 国内报关报检-进口报关 (Domestic Customs Declaration & Inspection - Import Customs Declaration)
         */
        EXCQ_JZBG_JKBG("300100400200002002", "国内报关报检-进口报关"),

        /**
         * 国内报关报检-分批送货 (Domestic Customs Declaration & Inspection - Batch Delivery)
         */
        EXCQ_FPSH("300100400200003", "国内报关报检-分批送货"),

        /**
         * 国内报关报检-区区流转 (Domestic Customs Declaration & Inspection - Zone to Zone Transfer)
         */
        EXCQ_QQLZ("300100400200004", "国内报关报检-区区流转"),

        /**
         * 国内报关报检-卡口登记 (Domestic Customs Declaration & Inspection - Checkpoint Registration)
         */
        EXCQ_KKDJ("300100400200005", "国内报关报检-卡口登记"),

        /**
         * 国内报关报检-跨关区区区流转 (Domestic Customs Declaration & Inspection - Cross-Customs Zone Transfer)
         */
        EXCQ_KGQQQLZ("300100400200006", "国内报关报检-跨关区区区流转"),

        /**
         * 国内报关报检-区内调拨-区内调拨 (Domestic Customs Declaration & Inspection - Inter-zone Transfer - Inter-zone Transfer)
         */
        QNDB("300100400300001", "国内报关报检-区内调拨-区内调拨"),

        /**
         * 国内报关报检-区内调拨-调拨集中报关-转出报关 (Domestic Customs Declaration & Inspection - Inter-zone Transfer - Centralized Transfer Declaration - Transfer Out Declaration)
         */
        QNDB_JZBG_ZCBG("300100400300002001", "国内报关报检-区内调拨-调拨集中报关-转出报关"),

        /**
         * 国内报关报检-区内调拨-调拨集中报关-转入报关 (Domestic Customs Declaration & Inspection - Inter-zone Transfer - Centralized Transfer Declaration - Transfer In Declaration)
         */
        QNDB_JZBG_ZRBG("300100400300002002", "国内报关报检-区内调拨-调拨集中报关-转入报关"),

        /**
         * 国内报关报检-区内报关调拨-转出报关 (Domestic Customs Declaration & Inspection - Inter-zone Customs Declaration Transfer - Transfer Out Declaration)
         */
        QNDB_BGDB_ZCBG("300100400300003001", "国内报关报检-区内报关调拨-转出报关"),

        /**
         * 国内报关报检-转入报关 (Domestic Customs Declaration & Inspection - Transfer In Declaration)
         */
        QNDB_BGDB_ZRBG("300100400300003002", "国内报关报检-转入报关"),

        /**
         * 国内报关报检-其他关务服务-账册备案 (Domestic Customs Declaration & Inspection - Other Customs Services - Handbook Filing)
         */
        ZCBA("300200001", "国内报关报检-其他关务服务-账册备案"),

        /**
         * 国内报关报检-账册核销 (Domestic Customs Declaration & Inspection - Handbook Verification)
         */
        ZCHX("300200002", "国内报关报检-账册核销"),

        /**
         * 国内报关报检-删改单 (Domestic Customs Declaration & Inspection - Delete/Modify Declaration)
         */
        SGD("300200003", "国内报关报检-删改单"),

        /**
         * 国内报关报检-商品预归类 (Domestic Customs Declaration & Inspection - Product Pre-classification)
         */
        SPYGL("300200004", "国内报关报检-商品预归类"),

        /**
         * 国内报关报检-证件代办 (Domestic Customs Declaration & Inspection - Document Processing)
         */
        ZJDB("300200005", "国内报关报检-证件代办"),

        /**
         * 国内报关报检-卡口作业 (Domestic Customs Declaration & Inspection - Checkpoint Operations)
         */
        KKZY("300200006", "国内报关报检-卡口作业"),

        /**
         * 国内报关报检-木制品熏蒸 (Domestic Customs Declaration & Inspection - Wood Product Fumigation)
         */
        MZPXZ("300200007", "国内报关报检-木制品熏蒸"),

        /**
         * 国内报关报检-电子口岸入网申请 (Domestic Customs Declaration & Inspection - Electronic Port Network Access Application)
         */
        DZKARWSQ("300200013", "国内报关报检-电子口岸入网申请"),

        /**
         * 国内报关报检-装运前预检验 (Domestic Customs Declaration & Inspection - Pre-shipment Inspection)
         */
        ZYQYJY("300200008", "国内报关报检-装运前预检验"),

        /**
         * 国内报关报检-其他报关-免表办理 (Domestic Customs Declaration & Inspection - Other Customs Declaration - Exemption Certificate Processing)
         */
        MBBL("300200014", "国内报关报检-其他报关-免表办理"),

        /**
         * 国内报关报检-其他报关-报检管理 (Domestic Customs Declaration & Inspection - Other Customs Declaration - Inspection Management)
         */
        BJGL("300200015", "国内报关报检-其他报关-报检管理"),

        /**
         * 国内报关报检-代购空白单据 (Domestic Customs Declaration & Inspection - Procurement of Blank Documents)
         */
        DGKBDJ("300200009", "国内报关报检-代购空白单据"),

        /**
         * 国内报关报检-系统代录入 (Domestic Customs Declaration & Inspection - System Data Entry)
         */
        XTDLR("300200010", "国内报关报检-系统代录入"),

        /**
         * 国内报关报检-劳务外派 (Domestic Customs Declaration & Inspection - Labor Dispatch)
         */
        LWP("300200011", "国内报关报检-劳务外派"),

        /**
         * 国内报关报检-途中监管 (Domestic Customs Declaration & Inspection - En Route Supervision)
         */
        TZJG("300200012", "国内报关报检-途中监管"),

        // Region: 俩服务项目对应 (Corresponding Service Items)
        /**
         * 国内报关报检-进口-核注清单申报 (Domestic Customs Declaration & Inspection - Import - Verification List Declaration)
         */
        HZQD_I("300100500100", "国内报关报检-进口-核注清单申报"),

        /**
         * 国内报关报检-进口-报关单申报 (Domestic Customs Declaration & Inspection - Import - Customs Declaration Form Declaration)
         */
        BGD_I("300100500200", "国内报关报检-进口-报关单申报"),

        /**
         * 国内报关报检-进口-核注清单+报关单申报 (Domestic Customs Declaration & Inspection - Import - Verification List + Customs Declaration Form Declaration)
         */
        HZQD_BGD_I("300100500300", "国内报关报检-进口-核注清单+报关单申报"),

        /**
         * 国内报关报检-进口-核注清单申报(单一) (Domestic Customs Declaration & Inspection - Import - Verification List Declaration (Single))
         */
        HZQD_I_SW("300100500500", "国内报关报检-进口-核注清单申报(单一)"),

        /**
         * 国内报关报检-进口-报关单申报(单一) (Domestic Customs Declaration & Inspection - Import - Customs Declaration Form Declaration (Single))
         */
        BGD_I_SW("300100500600", "国内报关报检-进口-报关单申报(单一)"),

        /**
         * 国内报关报检-进口-核注清单(单一)+报关单申报(单一) (Domestic Customs Declaration & Inspection - Import - Verification List (Single) + Customs Declaration Form Declaration (Single))
         */
        HZQD_BGD_I_SW("300100500700", "国内报关报检-进口-核注清单(单一)+报关单申报(单一)"),

        /**
         * 国内报关报检-出口-核注清单申报 (Domestic Customs Declaration & Inspection - Export - Verification List Declaration)
         */
        HZQD_E("300100600100", "国内报关报检-出口-核注清单申报"),

        /**
         * 国内报关报检--出口-报关单申报 (Domestic Customs Declaration & Inspection - Export - Customs Declaration Form Declaration)
         */
        BGD_E("300100600200", "国内报关报检--出口-报关单申报"),

        /**
         * 国内报关报检--出口-核注清单+报关单申报 (Domestic Customs Declaration & Inspection - Export - Verification List + Customs Declaration Form Declaration)
         */
        HZQD_BGD_E("300100600300", "国内报关报检--出口-核注清单+报关单申报"),

        /**
         * 国内报关报检-出口-核注清单申报(单一) (Domestic Customs Declaration & Inspection - Export - Verification List Declaration (Single))
         */
        HZQD_E_SW("300100600500", "国内报关报检-出口-核注清单申报(单一)"),

        /**
         * 国内报关报检--出口-报关单申报(单一) (Domestic Customs Declaration & Inspection - Export - Customs Declaration Form Declaration (Single))
         */
        BGD_E_SW("300100600600", "国内报关报检--出口-报关单申报(单一)"),

        /**
         * 国内报关报检--出口-核注清单(单一)+报关单申报(单一) (Domestic Customs Declaration & Inspection - Export - Verification List (Single) + Customs Declaration Form Declaration (Single))
         */
        HZQD_BGD_E_SW("300100600700", "国内报关报检--出口-核注清单(单一)+报关单申报(单一)"),

        /**
         * 300110100100 报关单申报 (300110100100 Customs Declaration Form Declaration)
         * /*"300110" ;//300110 国内申报|支持两进两处 - 一线进口
         */ // Original comment included, kept as is.

        BGD_I_YX_P("300110100100", "报关单申报"),

        /**
         * 300110100200 报关单申报(单一) (300110100200 Customs Declaration Form Declaration (Single))
         */
        BGD_I_YX_D("300110100200", "报关单申报(单一)"),

        /**
         * 300110100300 核注清单+报关单申报 (300110100300 Verification List + Customs Declaration Form Declaration)
         */
        HZQD_BGD_I_YX_PP("300110100300", "核注清单+报关单申报"),

        /**
         * 300110100400 核注清单(单一)+报关单申报(单一) (300110100400 Verification List (Single) + Customs Declaration Form Declaration (Single))
         */
        HZQD_BGD_I_YX_DD("300110100400", "核注清单(单一)+报关单申报(单一)"),

        /**
         * 300110100500 核注清单+报关单申报(单一) (300110100500 Verification List + Customs Declaration Form Declaration (Single))
         */
        HZQD_BGD_I_YX_DP("300110100500", "核注清单+报关单申报(单一)"),

        /**
         * 300110100600 核注清单(单一)+报关单申报 (300110100600 Verification List (Single) + Customs Declaration Form Declaration)
         */
        HZQD_BGD_I_YX_PD("300110100600", "核注清单(单一)+报关单申报"),

        /**
         * 300110100700 核注清单申报(请务必勾选不接单只作业) (300110100700 Verification List Declaration (Please be sure to select 'do not accept orders, only operate'))
         */
        HZQD_I_YX_P("300110100700", "核注清单申报(请务必勾选不接单只作业)"),

        /**
         * 300110100800 核注清单申报(单一)(请务必勾选不接单只作业) (300110100800 Verification List Declaration (Single) (Please be sure to select 'do not accept orders, only operate'))
         */
        HZQD_I_YX_D("300110100800", "核注清单申报(单一)(请务必勾选不接单只作业)"),

        /**
         * 进口进区 - 核注清单(单一)+报关单申报 (Import Entry - Verification List (Single) + Customs Declaration Form Declaration)
         */
        HZQD_BGD_JKJQ_DP("300100500900", "进口进区 - 核注清单(单一)+报关单申报"),

        /**
         * 进口进区 - 核注清单+报关单申报(单一) (Import Entry - Verification List + Customs Declaration Form Declaration (Single))
         */
        HZQD_BGD_JKJQ_PD("300100500910", "进口进区 - 核注清单+报关单申报(单一)"),

        /**
         * 出口出区 - 核注清单(单一)+报关单申报 (Export Exit - Verification List (Single) + Customs Declaration Form Declaration)
         */
        HZQD_BGD_CKCQ_DP("300100600900", "出口出区 - 核注清单(单一)+报关单申报"),

        /**
         * 出口出区 - 核注清单+报关单申报(单一) (Export Exit - Verification List + Customs Declaration Form Declaration (Single))
         */
        HZQD_BGD_CKCQ_PD("300100600910", "出口出区 - 核注清单+报关单申报(单一)"),

        /**
         * 300110200100 报关单申报 (300110200100 Customs Declaration Form Declaration)
         * /*"300110200" ;//300110200 一线出口
         */ // Original comment included, kept as is.

        BGD_E_YX_P("300110200100", "报关单申报"),

        /**
         * 300110200200 报关单申报(单一) (300110200200 Customs Declaration Form Declaration (Single))
         */
        BGD_E_YX_D("300110200200", "报关单申报(单一)"),

        /**
         * 300110200300 核注清单+报关单申报 (300110200300 Verification List + Customs Declaration Form Declaration)
         */
        HZQD_BGD_E_YX_PP("300110200300", "核注清单+报关单申报"),

        /**
         * 300110200400 核注清单(单一)+报关单申报(单一) (300110200400 Verification List (Single) + Customs Declaration Form Declaration (Single))
         */
        HZQD_BGD_E_YX_DD("300110200400", "核注清单(单一)+报关单申报(单一)"),

        /**
         * 300110200500 核注清单+报关单申报(单一) (300110200500 Verification List + Customs Declaration Form Declaration (Single))
         */
        HZQD_BGD_E_YX_DP("300110200500", "核注清单+报关单申报(单一)"),

        /**
         * 300110200600 核注清单(单一)+报关单申报 (300110200600 Verification List (Single) + Customs Declaration Form Declaration)
         */
        HZQD_BGD_E_YX_PD("300110200600", "核注清单(单一)+报关单申报"),

        /**
         * 300110200700 核注清单申报(请务必勾选不接单只作业) (300110200700 Verification List Declaration (Please be sure to select 'do not accept orders, only operate'))
         */
        HZQD_E_YX_P("300110200700", "核注清单申报(请务必勾选不接单只作业)"),

        /**
         * 300110200800 核注清单申报(单一)(请务必勾选不接单只作业) (300110200800 Verification List Declaration (Single) (Please be sure to select 'do not accept orders, only operate'))
         */
        HZQD_E_YX_D("300110200800", "核注清单申报(单一)(请务必勾选不接单只作业)"),

        /**
         * 300110300100 报关单申报 (300110300100 Customs Declaration Form Declaration)
         * /*"300110300" ;//300110300 二线区外企业进口
         */ // Original comment included, kept as is.

        BGD_I_EX_P("300110300100", "报关单申报"),

        /**
         * 300110300200 报关单申报(单一) (300110300200 Customs Declaration Form Declaration (Single))
         */
        BGD_I_EX_D("300110300200", "报关单申报(单一)"),

        /**
         * 300110300300 核注清单+报关单申报 (300110300300 Verification List + Customs Declaration Form Declaration)
         */
        HZQD_BGD_I_EX_PP("300110300300", "核注清单+报关单申报"),

        /**
         * 300110300400 核注清单(单一)+报关单申报(单一) (300110300400 Verification List (Single) + Customs Declaration Form Declaration (Single))
         */
        HZQD_BGD_I_EX_DD("300110300400", "核注清单(单一)+报关单申报(单一)"),

        /**
         * 300110300500 核注清单+报关单申报(单一) (300110300500 Verification List + Customs Declaration Form Declaration (Single))
         */
        HZQD_BGD_I_EX_DP("300110300500", "核注清单+报关单申报(单一)"),

        /**
         * 300110300600 核注清单(单一)+报关单申报 (300110300600 Verification List (Single) + Customs Declaration Form Declaration)
         */
        HZQD_BGD_I_EX_PD("300110300600", "核注清单(单一)+报关单申报"),

        /**
         * 300110300700 核注清单申报(请务必勾选不接单只作业) (300110300700 Verification List Declaration (Please be sure to select 'do not accept orders, only operate'))
         */
        HZQD_I_EX_P("300110300700", "核注清单申报(请务必勾选不接单只作业)"),

        /**
         * 300110300800 核注清单申报(单一)(请务必勾选不接单只作业) (300110300800 Verification List Declaration (Single) (Please be sure to select 'do not accept orders, only operate'))
         */
        HZQD_I_EX_D("300110300800", "核注清单申报(单一)(请务必勾选不接单只作业)"),

        /**
         * 300110400100 报关单申报 (300110400100 Customs Declaration Form Declaration)
         * /*300110400 300110400 二线区外企业出口
         */ // Original comment included, kept as is.

        BGD_E_EX_P("300110400100", "报关单申报"),

        /**
         * 300110400200 报关单申报(单一) (300110400200 Customs Declaration Form Declaration (Single))
         */
        BGD_E_EX_D("300110400200", "报关单申报(单一)"),

        /**
         * 300110400300 核注清单+报关单申报 (300110400300 Verification List + Customs Declaration Form Declaration)
         */
        HZQD_BGD_E_EX_PP("300110400300", "核注清单+报关单申报"),

        /**
         * 300110400400 核注清单(单一)+报关单申报(单一) (300110400400 Verification List (Single) + Customs Declaration Form Declaration (Single))
         */
        HZQD_BGD_E_EX_DD("300110400400", "核注清单(单一)+报关单申报(单一)"),

        /**
         * 300110400500 核注清单+报关单申报(单一) (300110400500 Verification List + Customs Declaration Form Declaration (Single))
         */
        HZQD_BGD_E_EX_DP("300110400500", "核注清单+报关单申报(单一)"),

        /**
         * 300110400600 核注清单(单一)+报关单申报 (300110400600 Verification List (Single) + Customs Declaration Form Declaration)
         */
        HZQD_BGD_E_EX_PD("300110400600", "核注清单(单一)+报关单申报"),

        /**
         * 300110400700 核注清单申报(请务必勾选不接单只作业) (300110400700 Verification List Declaration (Please be sure to select 'do not accept orders, only operate'))
         */
        HZQD_E_EX_P("300110400700", "核注清单申报(请务必勾选不接单只作业)"),

        /**
         * 300110400800 核注清单申报(单一)(请务必勾选不接单只作业) (300110400800 Verification List Declaration (Single) (Please be sure to select 'do not accept orders, only operate'))
         */
        HZQD_E_EX_D("300110400800", "核注清单申报(单一)(请务必勾选不接单只作业)"),

        /**
         * 300110700100 核注清单申报 (300110700100 Verification List Declaration)
         * /*"300110700" ;//"300110700" 区内结转/区区流转转入
         */ // Original comment included, kept as is.

        HZQD_I_QN_P("300110700100", "核注清单申报"),

        /**
         * 300110700200 核注清单申报(单一) (300110700200 Verification List Declaration (Single))
         */
        HZQD_I_QN_D("300110700200", "核注清单申报(单一)"),

        /**
         * 300110700300 核注清单+报关单申报 (300110700300 Verification List + Customs Declaration Form Declaration)
         */
        HZQD_BGD_I_QN_PP("300110700300", "核注清单+报关单申报"),

        /**
         * 300110700400 核注清单(单一)+报关单申报(单一) (300110700400 Verification List (Single) + Customs Declaration Form Declaration (Single))
         */
        HZQD_BGD_I_QN_DD("300110700400", "核注清单(单一)+报关单申报(单一)"),

        /**
         * 300110700500 核注清单+报关单申报(单一) (300110700500 Verification List + Customs Declaration Form Declaration (Single))
         */
        HZQD_BGD_I_QN_PD("300110700500", "核注清单+报关单申报(单一)"),

        /**
         * 300110700600 核注清单(单一)+报关单申报 (300110700600 Verification List (Single) + Customs Declaration Form Declaration)
         */
        HZQD_BGD_I_QN_DP("300110700600", "核注清单(单一)+报关单申报"),

        /**
         * 300110700700 报关单申报(请务必勾选不接单只作业) (300110700700 Customs Declaration Form Declaration (Please be sure to select 'do not accept orders, only operate'))
         */
        BGD_I_QN_P("300110700700", "报关单申报(请务必勾选不接单只作业)"),

        /**
         * 300110700800 报关单申报(单一)(请务必勾选不接单只作业) (300110700800 Customs Declaration Form Declaration (Single) (Please be sure to select 'do not accept orders, only operate'))
         */
        BGD_I_QN_D("300110700800", "报关单申报(单一)(请务必勾选不接单只作业)"),

        /**
         * 300110800100 核注清单申报 (300110800100 Verification List Declaration)
         * /* 300110800 300110800 区内结转/区区流转转出
         */ // Original comment included, kept as is.

        HZQD_E_QN_P("300110800100", "核注清单申报"),

        /**
         * 300110800200 核注清单申报(单一) (300110800200 Verification List Declaration (Single))
         */
        HZQD_E_QN_D("300110800200", "核注清单申报(单一)"),

        /**
         * 300110500100 核注清单申报 (300110500100 Verification List Declaration)
         * /*300110500 300110500 二线区内企业进区
         */ // Original comment included, kept as is.

        HZQD_I_QN_EX_P("300110500100", "核注清单申报"),

        /**
         * 300110500200 核注清单申报(单一) (300110500200 Verification List Declaration (Single))
         */
        HZQD_I_QN_EX_D("300110500200", "核注清单申报(单一)"),

        /**
         * 300110600100 核注清单申报 (300110600100 Verification List Declaration)
         * /*300110600 300110600 二线区内企业出区
         */ // Original comment included, kept as is.

        HZQD_E_QN_EX_P("300110600100", "核注清单申报"),

        /**
         * 300110600200 核注清单申报(单一) (300110600200 Verification List Declaration (Single))
         */
        HZQD_E_QN_EX_D("300110600200", "核注清单申报(单一)"),

        /**
         * 100300100 口岸抽、换单 (100300100 Port Drawing/Exchanging Documents)
         */
        KACHD("100300100", "口岸抽、换单"),

        /**
         * 100110 国外当地操作简版 (100110 Overseas Local Operations Simple Version)
         */
        GWDDCC("100110", "国外当地操作简版"),

        // End Region: 俩服务项目对应

        /**
         * 国内报关报检-进口-出入库单申报 (Domestic Customs Declaration & Inspection - Import - In/Outbound Order Declaration)
         */
        CRKD_I("300100500400", "国内报关报检-进口-出入库单申报"),

        /**
         * 国内报关报检-进口-出入库单申报(单一) (Domestic Customs Declaration & Inspection - Import - In/Outbound Order Declaration (Single))
         */
        CRKD_SW("300100500800", "国内报关报检-进口-出入库单申报(单一)"),

        /**
         * 国内报关报检--出口-出入库单申报 (Domestic Customs Declaration & Inspection - Export - In/Outbound Order Declaration)
         */
        CRKD_E("300100600400", "国内报关报检--出口-出入库单申报"),

        /**
         * 国内报关报检--出口-出入库单申报(单一) (Domestic Customs Declaration & Inspection - Export - In/Outbound Order Declaration (Single))
         */
        CRKD_E_SW("300100600800", "国内报关报检--出口-出入库单申报(单一)"),

        /**
         * 300110500300 出入库单申报 (300110500300 In/Outbound Order Declaration)
         * /*"300110500"; 300110500 二线区内企业进区
         */ // Original comment included, kept as is.

        CRKD_I_EX_P("300110500300", "出入库单申报"),

        /**
         * 300110500400 出入库单申报(单一) (300110500400 In/Outbound Order Declaration (Single))
         */
        CRKD_I_EX_D("300110500400", "出入库单申报(单一)"),

        /**
         * 300110600300 出入库单申报 (300110600300 In/Outbound Order Declaration)
         * /*"300110600"; 300110600 二线区内企业出区
         */ // Original comment included, kept as is.

        CRKD_E_EX_P("300110600300", "出入库单申报"),

        /**
         * ************ 出入库单申报(单一) (************ In/Outbound Order Declaration (Single))
         */
        CRKD_E_EX_D("************", "出入库单申报(单一)"),

        /**
         * 国内报关报检-进口-其他关务 (Domestic Customs Declaration & Inspection - Import - Other Customs Services)
         */
        QTGW("*********", "国内报关报检-进口-其他关务"),

        /**
         * 国内报关报检--其他关务台账 (Domestic Customs Declaration & Inspection - Other Customs Account Ledger)
         */
        QTGWTZ("*********", "国内报关报检--其他关务台账"),

        /**
         * 保税仓租赁 (Bonded Warehouse Leasing)
         */
        BSCZL("*********", "保税仓租赁"),

        /**
         * 场地租赁 (Yard Leasing)
         */
        CDZL("700100", "场地租赁"),

        /**
         * 普仓租赁 (General Warehouse Leasing)
         */
        PCZL("*********", "普仓租赁"),

        /**
         * 办公楼租赁 (Office Building Leasing)
         */
        BGLZL("*********", "办公楼租赁"),

        /**
         * 停车场租赁 (Parking Lot Leasing)
         */
        TCCZL("*********", "停车场租赁"),

        /**
         * 堆场租赁 (Stacking Yard Leasing)
         */
        DCZL("*********", "堆场租赁"),

        /**
         * 集装箱租赁-仅租赁 (Container Leasing - Leasing Only)
         */
        ONLYJZX("*********", "集装箱租赁-仅租赁"),

        /**
         * 集装箱租赁-配套运输 (Container Leasing -配套运输) // Note: "配套运输" is a phrase meaning supporting/配套运输, kept as is.
         */
        JZXZLPTYS("700200100100", "集装箱租赁-配套运输"),

        /**
         * 集装箱租赁-口岸换箱 (Container Leasing - Port Box Exchange)
         */
        JZXZLKAHX("700200100200", "集装箱租赁-口岸换箱"),

        /**
         * 其他设备租赁-叉车租赁 (Other Equipment Leasing - Forklift Leasing)
         */
        SBZLCC("700200300", "其他设备租赁-叉车租赁"),

        /**
         * 其他设备租赁-正面吊 (Other Equipment Leasing - Reach Stacker)
         */
        SBZLZMD("700200400", "其他设备租赁-正面吊"),

        /**
         * 劳务租赁 (Labor Leasing)
         */
        LWZL("700300", "劳务租赁"),

        /**
         * 租赁结算-保税仓 (Leasing Settlement - Bonded Warehouse)
         */
        ZLJSBSC("700400100100", "租赁结算-保税仓"),

        /**
         * 租赁结算-普仓 (Leasing Settlement - General Warehouse)
         */
        ZLJSPC("700400100200", "租赁结算-普仓"),

        /**
         * 租赁结算-办公楼 (Leasing Settlement - Office Building)
         */
        ZLJSBGL("700400100300", "租赁结算-办公楼"),

        /**
         * 租赁结算-停车场 (Leasing Settlement - Parking Lot)
         */
        ZLJTCC("700400100400", "租赁结算-停车场"),

        /**
         * 租赁结算-堆场 (Leasing Settlement - Stacking Yard)
         */
        ZLJSDC("700400100500", "租赁结算-堆场"),

        /**
         * 租赁结算-设备 (Leasing Settlement - Equipment)
         */
        ZLJSSBZL("700400200", "租赁结算-设备"),

        /**
         * 租赁结算-集装箱 (Leasing Settlement - Container)
         */
        ZLJSJZX("700400200100", "租赁结算-集装箱"),

        /**
         * 租赁结算-叉车 (Leasing Settlement - Forklift)
         */
        ZLJSCC("700400200200", "租赁结算-叉车"),

        /**
         * 租赁结算-正面吊 (Leasing Settlement - Reach Stacker)
         */
        ZLJSZMD("700400200300", "租赁结算-正面吊"),

        /**
         * 租赁结算-劳务租赁 (Leasing Settlement - Labor Leasing)
         */
        ZLJSLwzl("700400300", "租赁结算-劳务租赁"),

        /**
         * DHL-空运出口 (DHL - Air Freight Export)
         */
        DHL_AFR_EXP("200200100", "DHL-空运出口"),

        /**
         * DHL-空运进口 (DHL - Air Freight Import)
         */
        DHL_AFR_IMP("200200200", "DHL-空运进口"),

        /**
         * DHL-海运出口_整箱 (DHL - Sea Freight Export_Full Container Load)
         */
        DHL_OFR_FCL_EXP("200200300", "DHL-海运出口_整箱"),

        /**
         * DHL-海运进口_整箱 (DHL - Sea Freight Import_Full Container Load)
         */
        DHL_OFR_FCL_IMP("200200400", "DHL-海运进口_整箱"),

        /**
         * DHL-海运出口_拼箱 (DHL - Sea Freight Export_Less than Container Load)
         */
        DHL_OFR_LCL_EXP("200200500", "DHL-海运出口_拼箱"),

        /**
         * DHL-海运进口_拼箱 (DHL - Sea Freight Import_Less than Container Load)
         */
        DHL_OFR_LCL_IMP("200200600", "DHL-海运进口_拼箱"),

        /**
         * 国内报关报检|东盟 (Domestic Customs Declaration & Inspection | ASEAN)
         */
        GNBGBJ_DM("800100", "国内报关报检|东盟"),

        /**
         * 国外报关报检|东盟 (Overseas Customs Declaration & Inspection | ASEAN)
         */
        GWBGBJ_DM("800200", "国外报关报检|东盟"),

        /**
         * 其他报关报检|东盟 (Other Customs Declaration & Inspection | ASEAN)
         */
        QTBGBJ_DM("800300", "其他报关报检|东盟"),

        /**
         * 报关报检|东盟 | 卡口登记 (Customs Declaration & Inspection | ASEAN | Checkpoint Registration)
         */
        KKDJ_DM("800300100", "报关报检|东盟 | 卡口登记"),

        /**
         * 报关报检|东盟 | 区内调拨 (Customs Declaration & Inspection | ASEAN | Inter-zone Transfer)
         */
        QNDB_DM("*********", "报关报检|东盟 | 区内调拨"),

        /**
         * 报关报检 |东盟 | 核注清单 (Customs Declaration & Inspection | ASEAN | Verification List)
         */
        HZQD_DM("*********", "报关报检 |东盟 | 核注清单"),

        /**
         * 其他增值服务台账 (Other Value-added Services Account Ledger)
         */
        QTZZFWTZ("999100", "其他增值服务台账");

        // 值
        private final String code;
        // 描述
        private final String name;

        ServiceItem(String code, String name) {
            this.code = code;
            this.name = name;
        }


        public static ServiceItem fromCode(String value) {
            for (OrderEnum.ServiceItem type : OrderEnum.ServiceItem.values()) {
                if (type.getCode().equals(value)) {
                    return type;
                }
            }

            return null;
            //throw new IllegalArgumentException("No code  found for value: " + value);
        }

        // 获取对应取值
        public String getCode() {
            return code;
        }

        // 获取描述对应值
        public String getName() {
            return name;
        }

    }

    public enum OrderFromData {
        SIMPLE("SIMPLE", "普通订单"),
        TEMPLATE("TEMPLATE", "从模板新增"),
        EXCEL("EXCEL", "EXCEL导入"),
        COPY("COPY", "复制新增"),
        XT("XT", "协同订单");

        // 值
        private final String code;
        // 描述
        private final String name;

        OrderFromData(String code, String name) {
            this.code = code;
            this.name = name;
        }

        // 获取对应取值
        public String getCode() {
            return code;
        }


        // 获取描述对应值
        public String getName() {
            return name;
        }

    }


    /**
     * /// DHL_WORK_BASIC表
     * /// TO_STATUS 字段枚举
     **/
    public enum DHLOrderStatus {
        Submit("Submit", "待接收"),
        Back("Back", "退回"),
        Accept("Accept", "审核通过"),
        Reject("Reject", "审核退回"),
        Departure("Departure", "发车"),
        Arrived("Reject", "到达"),
        Complete("Reject", "完成");

        // 值
        private final String code;
        // 描述
        private final String name;

        DHLOrderStatus(String code, String name) {
            this.code = code;
            this.name = name;
        }

        // 获取对应取值
        public String getCode() {
            return code;
        }


        // 获取描述对应值
        public String getName() {
            return name;
        }

    }

    public enum OrderSaveType {
        /**
         * 确认接单
         */
        QRJD("QRJD", "确认接单"),

        /**
         * 协同订单审核
         */
        XTSH("XTSH", "协同订单审核"),

        /**
         * 保存订单模板
         */
        SAVETEMPLATE("SAVETEMPLATE", "保存订单模板"),

        /**
         * 从模板新增
         */
        ADDFROMTEMPLATE("ADDFROMTEMPLATE", "从模板新增"),

        /**
         * 保存订单
         */
        SAVEORDER("SAVEORDER", "保存订单"),

        /**
         * 作废
         */
        PAGEZF("PAGEZF", "作废"),

        /**
         * 协同订单
         */
        XT("XT", "协同订单"),

        /**
         * 跟踪
         */
        TRACE("TRACE", "跟踪"),
        /**
         * 运维
         */
        YUWEI("YUWEI", "运维");

        // 值
        private final String code;
        // 描述
        private final String name;

        OrderSaveType(String code, String name) {
            this.code = code;
            this.name = name;
        }

        // 获取对应取值
        public String getCode() {
            return code;
        }


        // 获取描述对应值
        public String getName() {
            return name;
        }

    }

    /**
     * 订单操作枚举
     */
    public enum OrderOpType {
        /**
         * （订单暂存）确认接单
         */
        QRJD("QRJD", "确认接单"),

        /**
         * （订单暂存）作废
         */
        ZF("ZF", "作废"),

        /**
         * 取消确认接单
         */
        QXQRJD("QXQRJD", "取消确认接单"),

        /**
         * 协作分发
         */
        XZFF("XZFF", "协作分发"),

        /**
         * 取消作废
         */
        QXZF("QXZF", "取消作废"),

        /**
         * 取消退单
         */
        QXTD("QXTD", "取消退单"),

        /**
         * 退单
         */
        TD("TD", "退单"),

        /**
         * 结案
         */
        JA("JA", "结案");

        private final String code;
        private final String name;

        // 构造函数
        OrderOpType(String code, String name) {
            this.code = code;
            this.name = name;
        }

        // Getter 方法
        public String getCode() {
            return code;
        }

        public String getName() {
            return name;
        }

        /**
         * 根据 code 获取对应的枚举常量
         *
         * @param code 订单操作代码字符串
         * @return 对应的 OrderOpType 枚举常量，如果找不到则返回 null
         */
        public static OrderOpType fromCode(String code) {
            for (OrderOpType type : OrderOpType.values()) {
                if (type.getCode().equals(code)) {
                    return type;
                }
            }
            return null;
        }

    }

    public enum OrderStatus {
        ZC("ZC", "暂存"),
        SX("SX", "生效"),
        JA("JA", "结案"),
        ZF("ZF", "作废"),
        TD("TD", "退单");

        // 值
        private final String code;
        // 描述
        private final String name;

        OrderStatus(String code, String name) {
            this.code = code;
            this.name = name;
        }

        // 获取对应取值
        public String getCode() {
            return code;
        }


        // 获取描述对应值
        public String getName() {
            return name;
        }

    }

    /**
     * 订单执行阶段
     */
    public enum OrderStage {

        /**
         * 暂存
         */
        ZC("ZC", "暂存"), // 新增：暂存阶段，Code定为"ZC"

        /**
         * 接单
         */
        JD("JD", "接单"),

        /**
         * 分发中
         * <remark>
         * 如果是所有协作任务都被退单了，则是 分发中 的状态
         * 该状态是由对接外部系统（灵境关务）-> 得到接单的结果
         * </remark>
         */
        FFZ("FFZ", "分发中"),

        /**
         * 分发
         * <remark>
         * 只要有一个协作任务被接单了，就该是 分发状态
         * 该状态是由对接外部系统（灵境关务）-> 得到接单的结果
         * </remark>
         */
        FF("FF", "分发"),

        /**
         * 部分作业完成
         * <remark>
         * 对接外部系统 获取结果
         * 反馈作业节点 （表：OMS_ORDER_FWXM_WORK_TRACE 作业跟踪）
         * </remark>
         */
        BFWC("BFWC", "部分作业完成"), // 新增：部分作业完成，Code定为"BFWC"

        /**
         * 全部作业完成
         * <remark>
         * 对接外部系统 获取结果
         * 反馈作业完成
         * </remark>
         */
        QBWC("QBWC", "全部作业完成"); // 新增：全部作业完成，Code定为"QBWC"


        private final String code;
        private final String name;

        OrderStage(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public String getName() {
            return name;
        }

        /**
         * 根据code获取对应的枚举常量
         *
         * @param code 阶段代码
         * @return 对应的OrderStage枚举常量，如果未找到则返回null
         */
        public static OrderStage getByCode(String code) {
            for (OrderStage stage : OrderStage.values()) {
                if (stage.getCode().equals(code)) {
                    return stage;
                }
            }
            return null;
        }

        /**
         * 根据name获取对应的枚举常量
         *
         * @param name 阶段名称
         * @return 对应的OrderStage枚举常量，如果未找到则返回null
         */
        public static OrderStage getByName(String name) {
            for (OrderStage stage : OrderStage.values()) {
                if (stage.getName().equals(name)) {
                    return stage;
                }
            }
            return null;
        }
    }

    public enum DocStatus {
        OP_COMPLETE_OK("01", "OP_COMPLETE_OK"),
        DATA_OK("02", "DATA_OK"),
        ZF("03", "OP_COMPLETE_OK");

        // 值
        private final String code;
        // 描述
        private final String name;

        DocStatus(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public static DocStatus fromCode(String value) {
            for (OrderEnum.DocStatus type : OrderEnum.DocStatus.values()) {
                if (type.getCode().equals(value)) {
                    return type;
                }
            }


            throw new IllegalArgumentException("No code  found for value: " + value);
        }

        // 获取对应取值
        public String getCode() {
            return code;
        }


        // 获取描述对应值
        public String getName() {
            return name;
        }

    }

    public enum OrderWorkResponse {
        PT("PT", "平台企业系统反馈"),
        OWN("OWN", "自有作业系统反馈"),
        NO("NO", "无作业系统反馈"),
        OMS("OMS", "平台订单系统反馈");;

        // 值
        private final String code;
        // 描述
        private final String name;

        OrderWorkResponse(String code, String name) {
            this.code = code;
            this.name = name;
        }

        // 获取对应取值
        public String getCode() {
            return code;
        }


        // 获取描述对应值
        public String getName() {
            return name;
        }

    }

    public enum WorkStatus {
        ZC("ZC", "暂存"),
        XZFF("XZFF", "协作分发"),
        XZJD("XZJD", "协作接单"),
        JA("JA", "结案"),
        TH("TH", "退回");

        // 值
        private final String code;
        // 描述
        private final String name;

        WorkStatus(String code, String name) {
            this.code = code;
            this.name = name;
        }

        // 获取对应取值
        public String getCode() {
            return code;
        }


        // 获取描述对应值
        public String getName() {
            return name;
        }

    }


}
