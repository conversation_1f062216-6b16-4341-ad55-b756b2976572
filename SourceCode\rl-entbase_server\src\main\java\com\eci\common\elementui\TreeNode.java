package com.eci.common.elementui;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * elementui 树形结构对象
 */
@Data
public class TreeNode {
    private String guid;
    private String code;
    private String name;
    private String status;
    /**
     * 页面跳转地址
     */
    private String pageUrl;
    private List<TreeNode> children;

    /**
     * 构造函数
     *
     * @param guid
     * @param code
     * @param name
     * @param status
     */
    public TreeNode(String guid, String code, String name, String status, String pageUrl) {
        this.guid = guid;
        this.code = code;
        this.name = name;
        this.status = status;
        this.pageUrl = pageUrl;
        this.children = new ArrayList<>();
    }


    /**
     * 添加子节点的方法
     *
     * @param child
     */
    public void addChild(TreeNode child) {
        this.children.add(child);
    }
}
