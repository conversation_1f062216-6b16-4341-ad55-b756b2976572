package com.eci.common;

import java.lang.annotation.*;

/**
 * 字典注解
 */
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.FIELD})
public @interface DictField {

    /**
     * 字典key（原有逻辑）
     */
    String queryKey() default "";

    /**
     * 使用时间格式化
     */
    boolean useDateFormat() default false;

    /**
     * 时间格式化模板
     */
    String dateFormat() default "yyyy-MM-dd";

    /**
     * 字段后缀，默认是 Name
     */
    String suffix() default "Name";

    /**
     * 自定义数据源：CodeNameCommon ✅ 新增：JSON 数据源
     * @DictField(data = {
     *     "{\"code\":\"ENABLE\",\"name\":\"启用\"}",
     *     "{\"code\":\"DISABLE\",\"name\":\"禁用\"}"
     * })
     *
     * @DictField(data = {
     *     "{'code':'1','name':'男'}",
     *     "{'code':'2','name':'女'}"
     * })
     */
    String[] data() default {};

    /**
     * 自定义 SQL 查询语句（可带参数）
     * @DictField(
     *     sql = "SELECT name FROM some_table WHERE type_code = '${type}' AND status = '${status}'",
     *     params = {"type", "status"}
     * )
     */
    String sql() default "";

    /**
     * SQL 参数字段名（从实体中获取值）
     */
    String[] params() default {};
}