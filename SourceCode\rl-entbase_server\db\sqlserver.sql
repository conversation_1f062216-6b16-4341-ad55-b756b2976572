CREATE TABLE [dbo].[SYS_OPER_LOG](
	[ID] [bigint] NOT NULL,
	[TITLE] [nvarchar](100) NULL,
	[BUSINESS_TYPE] [int] NULL,
	[OPER_URL] [nvarchar](255) NULL,
	[METHOD] [nvarchar](255) NULL,
	[REQUEST_METHOD] [nvarchar](10) NULL,
	[OPER_IP] [nvarchar](15) NULL,
	[OPER_LOCATION] [nvarchar](100) NULL,
	[OPER_STATUS] [int] NULL,
	[ERROR_MSG] [nvarchar](max) NULL,
	[OPER_TIME] [datetime] NULL,
	[OPER_PARAM] [nvarchar](max) NULL,
	[JSON_RESULT] [nvarchar](max) NULL,
	[USER_ID] [nvarchar](64) NULL,
	[LOGIN_NAME] [nvarchar](64) NULL,
	[OPER_OS] [nvarchar](20) NULL,
	[BROWSER] [nvarchar](50) NULL,
	[SERVER] [nvarchar](20) NULL,
 CONSTRAINT [PK__SYS_OPER__3214EC27E5062DFE] PRIMARY KEY CLUSTERED
(
	[ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]



EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'主键id' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_OPER_LOG', @level2type=N'COLUMN',@level2name=N'ID'


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'操作模块' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_OPER_LOG', @level2type=N'COLUMN',@level2name=N'TITLE'


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'业务类型|0其它 1新增 2修改 3删除' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_OPER_LOG', @level2type=N'COLUMN',@level2name=N'BUSINESS_TYPE'


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'请求url' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_OPER_LOG', @level2type=N'COLUMN',@level2name=N'OPER_URL'


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'请求后端方法' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_OPER_LOG', @level2type=N'COLUMN',@level2name=N'METHOD'


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'请求方式' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_OPER_LOG', @level2type=N'COLUMN',@level2name=N'REQUEST_METHOD'


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'操作地址' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_OPER_LOG', @level2type=N'COLUMN',@level2name=N'OPER_IP'


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'操作地点' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_OPER_LOG', @level2type=N'COLUMN',@level2name=N'OPER_LOCATION'


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'操作状态|0正常 1异常' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_OPER_LOG', @level2type=N'COLUMN',@level2name=N'OPER_STATUS'


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'错误消息' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_OPER_LOG', @level2type=N'COLUMN',@level2name=N'ERROR_MSG'


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'操作时间' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_OPER_LOG', @level2type=N'COLUMN',@level2name=N'OPER_TIME'


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'请求参数' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_OPER_LOG', @level2type=N'COLUMN',@level2name=N'OPER_PARAM'


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'返回响应体' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_OPER_LOG', @level2type=N'COLUMN',@level2name=N'JSON_RESULT'


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'用户id' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_OPER_LOG', @level2type=N'COLUMN',@level2name=N'USER_ID'


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'用户名' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_OPER_LOG', @level2type=N'COLUMN',@level2name=N'LOGIN_NAME'


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'操作系统' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_OPER_LOG', @level2type=N'COLUMN',@level2name=N'OPER_OS'


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'浏览器' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_OPER_LOG', @level2type=N'COLUMN',@level2name=N'BROWSER'


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'本地服务器' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_OPER_LOG', @level2type=N'COLUMN',@level2name=N'SERVER'


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'操作日志记录表' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_OPER_LOG'



CREATE TABLE [dbo].[SYS_DATA_HELP2] (
    [ID] bigint NOT NULL IDENTITY(1,1) PRIMARY KEY,
    [QUERY_KEY] nvarchar(200) NULL,
    [QUERY_TYPE] nchar(1) NULL,
    [SQL_COMMAND] nvarchar(max) NULL,
    [BASE_COMMENT] nvarchar(800) NULL,
    [SYS] nvarchar(30) NULL,
    [CREATE_TIME] datetime NULL,
    [UPDATE_TIME] datetime NULL,
    [USE_CACHE] char(1) DEFAULT N'N',
    [LANGUAGE_TYPE] nvarchar(10) NULL,
    [DATA_CHANGE_CODE] nvarchar(50) COLLATE Chinese_PRC_CI_AS NULL,
    [DATA_CHANGE_NAME] nvarchar(50) COLLATE Chinese_PRC_CI_AS NULL,
    [TG_SORT_FIELD] nvarchar(50) NULL,
    [CREATE_USER] nvarchar(50) COLLATE Chinese_PRC_CI_AS NULL,
    [UPDATE_USER] nvarchar(50) COLLATE Chinese_PRC_CI_AS NULL,
    [CODE_MEMO] nvarchar(50) COLLATE Chinese_PRC_CI_AS NULL,
    [NAME_MEMO] nvarchar(50) COLLATE Chinese_PRC_CI_AS NULL,
    [COMPARE_TYPE] nvarchar(20) NULL,
    [CHOOSE_SHOW] nvarchar(max) NULL,
    [TG_QUERY_FIELD] nvarchar(100) NULL,
    [AUTO_UPPER] char(1) NULL,
    [QUERY_MODE] nvarchar(20) NULL,
    [STATUS] nvarchar(2) NULL,
    [ECI_LOCK] char(1) NULL,
    [CONVERT_SQL] nvarchar(max) NULL,
    [PL_VERSION] nvarchar(200) NULL,
    [NEED_DOWNLOAD] char(1) DEFAULT NULL,
    [MEMO_DETAIL] nvarchar(800) NULL,
    [ADMIN_LOCK] char(1) NULL,
    [ASSEMBLY_NAME] nvarchar(400) NULL,
    [FILTER] char(1) NULL,
    [LAYOUT] nvarchar(200) NULL,
    [EDIT_URL] nvarchar(255) NULL,
    [CONFIG] nvarchar(255) NULL,
    [WIDTH] nvarchar(20) NULL,
    [HEIGHT] nvarchar(20) NULL,
    [QUERY_DATA] nvarchar(255) NULL,
    [TG_PAGE_SIZE] nvarchar(20) NULL,

    -- 添加唯一键约束
    CONSTRAINT uk_sys_data_help UNIQUE (QUERY_KEY, SYS, LANGUAGE_TYPE)
    );



CREATE TABLE [dbo].[SYS_USER_INFO](
	[ID] [bigint]  NOT NULL,
	[USER_ID] [nvarchar](50) NOT NULL,
	[LOGIN_NAME] [nvarchar](30) NOT NULL,
	[TRUE_NAME] [nvarchar](30) NULL,
	[COMPANY_CODE] [nvarchar](20) NOT NULL,
	[COMPANY_NAME] [nvarchar](255) NOT NULL,
	[CUST_CODE] [nvarchar](4) NULL,
	[CUST_NAME] [nvarchar](20) NULL,
	[USER_NICKNAME] [nvarchar](30) NULL,
	[USER_IMG] [nvarchar](255) NULL,
	[USER_SEX] [nvarchar](1) NULL,
	[LOGIN_COUNT] [int] NULL,
	[LOGIN_TIME] [datetime] NULL,
	[LOGIN_LAST_TIME] [datetime] NULL,
	[CLIENT_IP] [nvarchar](30) NULL,
	[CLIENT_PROVINCE] [nvarchar](30) NULL,
	[CLIENT_CITY] [nvarchar](30) NULL,
	[CLIENT_BROWSER] [nvarchar](30) NULL,
	[CLIENT_OS] [nvarchar](30) NULL,
	[USER_TOKEN] [nvarchar](255) NULL,
	[BIND_PHONE_NO] [nvarchar](20) NULL,
	[WEB_SIDE_TYPE] [nvarchar](1) NULL,
	[WEB_HEAD_TYPE] [nvarchar](1) NULL,
	[WEB_THEME] [nvarchar](10) NULL,
	[WEB_LAYOUT] [nvarchar](1) NULL,
	[WEB_SIDE_IS_ICON] [nvarchar](1) NULL,
	[WEB_SIDE_IS_OPEN] [nvarchar](1) NULL,
	[WEB_IS_TAB] [nvarchar](1) NULL,
	[WEB_TAB_TYPE] [nvarchar](1) NULL,
	[MAIL_ADDRESS] [nvarchar](255) NULL,
	[PWD_LAST_UPDATE_TIME] [datetime] NULL,
	[DEV] [nvarchar](1) NULL,
 CONSTRAINT [PK__SYS_USER__3214EC27C44C68F5] PRIMARY KEY CLUSTERED
(
	[ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]



ALTER TABLE [dbo].[SYS_USER_INFO] ADD  CONSTRAINT [DF_SYS_USER_INFO_USER_SEX]  DEFAULT ('0') FOR [USER_SEX]


ALTER TABLE [dbo].[SYS_USER_INFO] ADD  CONSTRAINT [DF_SYS_USER_INFO_LOGIN_COUNT]  DEFAULT ((0)) FOR [LOGIN_COUNT]


ALTER TABLE [dbo].[SYS_USER_INFO] ADD  CONSTRAINT [DF_SYS_USER_INFO_WEB_SIDE_TYPE]  DEFAULT ('2') FOR [WEB_SIDE_TYPE]


ALTER TABLE [dbo].[SYS_USER_INFO] ADD  CONSTRAINT [DF_SYS_USER_INFO_WEB_HEAD_TYPE]  DEFAULT ('3') FOR [WEB_HEAD_TYPE]


ALTER TABLE [dbo].[SYS_USER_INFO] ADD  CONSTRAINT [DF_SYS_USER_INFO_WEB_THEME]  DEFAULT ('#409eff') FOR [WEB_THEME]


ALTER TABLE [dbo].[SYS_USER_INFO] ADD  CONSTRAINT [DF_SYS_USER_INFO_WEB_LAYOUT]  DEFAULT ('3') FOR [WEB_LAYOUT]


ALTER TABLE [dbo].[SYS_USER_INFO] ADD  CONSTRAINT [DF_SYS_USER_INFO_WEB_SIDE_IS_ICON]  DEFAULT ('1') FOR [WEB_SIDE_IS_ICON]


ALTER TABLE [dbo].[SYS_USER_INFO] ADD  CONSTRAINT [DF_SYS_USER_INFO_WEB_SIDE_IS_OPEN]  DEFAULT ('1') FOR [WEB_SIDE_IS_OPEN]


ALTER TABLE [dbo].[SYS_USER_INFO] ADD  CONSTRAINT [DF_SYS_USER_INFO_WEB_IS_TAB]  DEFAULT ('1') FOR [WEB_IS_TAB]


ALTER TABLE [dbo].[SYS_USER_INFO] ADD  CONSTRAINT [DF_SYS_USER_INFO_WEB_TAB_TYPE]  DEFAULT ('1') FOR [WEB_TAB_TYPE]


ALTER TABLE [dbo].[SYS_USER_INFO] ADD  CONSTRAINT [DF_SYS_USER_INFO_PWD_LAST_UPDATE_TIME]  DEFAULT (getdate()) FOR [PWD_LAST_UPDATE_TIME]


ALTER TABLE [dbo].[SYS_USER_INFO] ADD  CONSTRAINT [DF_SYS_USER_INFO_DEV]  DEFAULT ('0') FOR [DEV]


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'自增列' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_USER_INFO', @level2type=N'COLUMN',@level2name=N'ID'


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'权限平台用户ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_USER_INFO', @level2type=N'COLUMN',@level2name=N'USER_ID'


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'权限平台登录用户名' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_USER_INFO', @level2type=N'COLUMN',@level2name=N'LOGIN_NAME'


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'权限平台真实姓名' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_USER_INFO', @level2type=N'COLUMN',@level2name=N'TRUE_NAME'


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'企业代码' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_USER_INFO', @level2type=N'COLUMN',@level2name=N'COMPANY_CODE'


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'企业名称' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_USER_INFO', @level2type=N'COLUMN',@level2name=N'COMPANY_NAME'


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'关区代码' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_USER_INFO', @level2type=N'COLUMN',@level2name=N'CUST_CODE'


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'关区名称' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_USER_INFO', @level2type=N'COLUMN',@level2name=N'CUST_NAME'


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'用户别名' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_USER_INFO', @level2type=N'COLUMN',@level2name=N'USER_NICKNAME'


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'头像' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_USER_INFO', @level2type=N'COLUMN',@level2name=N'USER_IMG'


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'性别(0-保密/1-男/2-女)' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_USER_INFO', @level2type=N'COLUMN',@level2name=N'USER_SEX'


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'登录次数' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_USER_INFO', @level2type=N'COLUMN',@level2name=N'LOGIN_COUNT'


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'本次登录时间' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_USER_INFO', @level2type=N'COLUMN',@level2name=N'LOGIN_TIME'


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'上次登录时间' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_USER_INFO', @level2type=N'COLUMN',@level2name=N'LOGIN_LAST_TIME'


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'客户端IP' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_USER_INFO', @level2type=N'COLUMN',@level2name=N'CLIENT_IP'


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'客户端IP所在省份' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_USER_INFO', @level2type=N'COLUMN',@level2name=N'CLIENT_PROVINCE'


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'客户端IP所在城市' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_USER_INFO', @level2type=N'COLUMN',@level2name=N'CLIENT_CITY'


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'客户端浏览器' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_USER_INFO', @level2type=N'COLUMN',@level2name=N'CLIENT_BROWSER'


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'客户端操作系统' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_USER_INFO', @level2type=N'COLUMN',@level2name=N'CLIENT_OS'


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'USER_TOKEN' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_USER_INFO', @level2type=N'COLUMN',@level2name=N'USER_TOKEN'


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'绑定手机号' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_USER_INFO', @level2type=N'COLUMN',@level2name=N'BIND_PHONE_NO'


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'侧边栏类型(1-栏式1/2-栏式2)' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_USER_INFO', @level2type=N'COLUMN',@level2name=N'WEB_SIDE_TYPE'


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'顶部模式(1-白/2-黑/3-主题色)' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_USER_INFO', @level2type=N'COLUMN',@level2name=N'WEB_HEAD_TYPE'


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'主题颜色' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_USER_INFO', @level2type=N'COLUMN',@level2name=N'WEB_THEME'


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'布局模式(1-侧边/2-顶部/3-混合)' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_USER_INFO', @level2type=N'COLUMN',@level2name=N'WEB_LAYOUT'


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'侧边栏彩色图标' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_USER_INFO', @level2type=N'COLUMN',@level2name=N'WEB_SIDE_IS_ICON'


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'侧栏排它展开' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_USER_INFO', @level2type=N'COLUMN',@level2name=N'WEB_SIDE_IS_OPEN'


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'启用标签页' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_USER_INFO', @level2type=N'COLUMN',@level2name=N'WEB_IS_TAB'


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'标签显示风格(1-默认/2-圆点/3-卡片)' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_USER_INFO', @level2type=N'COLUMN',@level2name=N'WEB_TAB_TYPE'


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'用户邮箱地址' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_USER_INFO', @level2type=N'COLUMN',@level2name=N'MAIL_ADDRESS'


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'密码最后修改时间' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_USER_INFO', @level2type=N'COLUMN',@level2name=N'PWD_LAST_UPDATE_TIME'


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'是否运维|0否1是' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_USER_INFO', @level2type=N'COLUMN',@level2name=N'DEV'


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'用户信息' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_USER_INFO'





CREATE TABLE [dbo].[SYS_USER_LOGIN_LOG](
	[ID] [bigint]  NOT NULL,
	[USER_ID] [nvarchar](50) NULL,
	[LOGIN_NAME] [nvarchar](30) NULL,
	[SYS_CODE] [nvarchar](30) NULL,
	[CLIENT_IP] [nvarchar](30) NULL,
	[CLIENT_PROVINCE] [nvarchar](30) NULL,
	[CLIENT_CITY] [nvarchar](30) NULL,
	[CLIENT_BROWSER] [nvarchar](30) NULL,
	[CLIENT_OS] [nvarchar](30) NULL,
	[LOGIN_TIME] [datetime] NULL,
	[LOGIN_STATUS] [int] NULL,
	[LOGIN_DESCRIPTION] [nvarchar](255) NULL,
	[ACTION_PARAMS] [nvarchar](max) NULL,
 CONSTRAINT [PK__SYS_USER__3214EC27117F7DD0] PRIMARY KEY CLUSTERED
(
	[ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]



EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'自增列(业务无关)' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_USER_LOGIN_LOG', @level2type=N'COLUMN',@level2name=N'ID'


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'权限平台用户ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_USER_LOGIN_LOG', @level2type=N'COLUMN',@level2name=N'USER_ID'


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'权限平台登录用户名' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_USER_LOGIN_LOG', @level2type=N'COLUMN',@level2name=N'LOGIN_NAME'


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'系统代码' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_USER_LOGIN_LOG', @level2type=N'COLUMN',@level2name=N'SYS_CODE'


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'客户端IP' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_USER_LOGIN_LOG', @level2type=N'COLUMN',@level2name=N'CLIENT_IP'


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'客户端IP所在省份' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_USER_LOGIN_LOG', @level2type=N'COLUMN',@level2name=N'CLIENT_PROVINCE'


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'客户端IP所在城市' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_USER_LOGIN_LOG', @level2type=N'COLUMN',@level2name=N'CLIENT_CITY'


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'客户端浏览器' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_USER_LOGIN_LOG', @level2type=N'COLUMN',@level2name=N'CLIENT_BROWSER'


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'客户端操作系统' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_USER_LOGIN_LOG', @level2type=N'COLUMN',@level2name=N'CLIENT_OS'


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'登录时间' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_USER_LOGIN_LOG', @level2type=N'COLUMN',@level2name=N'LOGIN_TIME'


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'登录状态(1-成功/0-失败)' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_USER_LOGIN_LOG', @level2type=N'COLUMN',@level2name=N'LOGIN_STATUS'


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'登录失败原因' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_USER_LOGIN_LOG', @level2type=N'COLUMN',@level2name=N'LOGIN_DESCRIPTION'


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'请求参数' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_USER_LOGIN_LOG', @level2type=N'COLUMN',@level2name=N'ACTION_PARAMS'


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'用户登录日志' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_USER_LOGIN_LOG'





CREATE TABLE [dbo].[SYS_ENTERPRISE_INFO](
	[ID] [bigint]  NOT NULL,
	[COMPANY_CODE] [nvarchar](20) NOT NULL,
	[COMPANY_NAME] [nvarchar](255) NOT NULL,
	[EP_SCCD] [nvarchar](18) NULL,
	[CUST_CODE] [nvarchar](4) NULL,
	[CUST_NAME] [nvarchar](20) NULL,
	[CONTACTS] [nvarchar](100) NULL,
	[CONTACTS_TEL] [nvarchar](100) NULL,
	[MAIL_ADDRESS] [nvarchar](255) NULL,
	[EP_ADDRESS] [nvarchar](255) NULL,
	[EP_LO_URL] [nvarchar](255) NULL,
	[EP_SYS_NAME] [nvarchar](255) NULL,
	[REMARK] [nvarchar](255) NULL,
	[CREATE_TIME] [datetime] NULL,
	[UPDATE_TIME] [datetime] NULL,
 CONSTRAINT [PK__SYS_ENTE__3214EC27C2E35FA5] PRIMARY KEY CLUSTERED
(
	[ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]



ALTER TABLE [dbo].[SYS_ENTERPRISE_INFO] ADD  CONSTRAINT [DF_SYS_ENTERPRISE_INFO_CREATE_TIME]  DEFAULT (getdate()) FOR [CREATE_TIME]


ALTER TABLE [dbo].[SYS_ENTERPRISE_INFO] ADD  CONSTRAINT [DF_SYS_ENTERPRISE_INFO_UPDATE_TIME]  DEFAULT (getdate()) FOR [UPDATE_TIME]


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'自增列(业务无关)' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_ENTERPRISE_INFO', @level2type=N'COLUMN',@level2name=N'ID'


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'企业编码' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_ENTERPRISE_INFO', @level2type=N'COLUMN',@level2name=N'COMPANY_CODE'


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'企业名称' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_ENTERPRISE_INFO', @level2type=N'COLUMN',@level2name=N'COMPANY_NAME'


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'企业18位社会信用代码' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_ENTERPRISE_INFO', @level2type=N'COLUMN',@level2name=N'EP_SCCD'


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'关区代码' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_ENTERPRISE_INFO', @level2type=N'COLUMN',@level2name=N'CUST_CODE'


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'关区名称' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_ENTERPRISE_INFO', @level2type=N'COLUMN',@level2name=N'CUST_NAME'


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'联系人' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_ENTERPRISE_INFO', @level2type=N'COLUMN',@level2name=N'CONTACTS'


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'联系电话' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_ENTERPRISE_INFO', @level2type=N'COLUMN',@level2name=N'CONTACTS_TEL'


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'邮件地址' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_ENTERPRISE_INFO', @level2type=N'COLUMN',@level2name=N'MAIL_ADDRESS'


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'企业地址' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_ENTERPRISE_INFO', @level2type=N'COLUMN',@level2name=N'EP_ADDRESS'


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'企业专属loURL' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_ENTERPRISE_INFO', @level2type=N'COLUMN',@level2name=N'EP_LO_URL'


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'企业专属系统名称' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_ENTERPRISE_INFO', @level2type=N'COLUMN',@level2name=N'EP_SYS_NAME'


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'备注' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_ENTERPRISE_INFO', @level2type=N'COLUMN',@level2name=N'REMARK'


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'创建时间' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_ENTERPRISE_INFO', @level2type=N'COLUMN',@level2name=N'CREATE_TIME'


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'修改时间' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_ENTERPRISE_INFO', @level2type=N'COLUMN',@level2name=N'UPDATE_TIME'


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'企业信息表' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_ENTERPRISE_INFO'




CREATE TABLE [dbo].[SYS_TABLE_SETTING](
	[ID] [bigint]  NOT NULL,
	[USER_ID] [nvarchar](36) NULL,
	[SETTING_TYPE] [nvarchar](1) NULL,
	[TABLE_CODE] [nvarchar](255) NULL,
	[JSON_DETAIL] [nvarchar](max) NULL,
	[CREATE_TIME] [datetime] NULL,
 CONSTRAINT [PK__SYS_TABL__3214EC27831F5E3C] PRIMARY KEY CLUSTERED
(
	[ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]



EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'主键' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_TABLE_SETTING', @level2type=N'COLUMN',@level2name=N'ID'


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'用户id' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_TABLE_SETTING', @level2type=N'COLUMN',@level2name=N'USER_ID'


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'设置类型|1筛选项2表单项' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_TABLE_SETTING', @level2type=N'COLUMN',@level2name=N'SETTING_TYPE'


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'表名' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_TABLE_SETTING', @level2type=N'COLUMN',@level2name=N'TABLE_CODE'


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'json明细' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_TABLE_SETTING', @level2type=N'COLUMN',@level2name=N'JSON_DETAIL'


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'创建时间' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_TABLE_SETTING', @level2type=N'COLUMN',@level2name=N'CREATE_TIME'


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'页面列表设置信息' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_TABLE_SETTING'





CREATE TABLE [dbo].[SYS_QUICK_MY_COLLECTION](
	[ID] [bigint]  NOT NULL,
	[USER_ID] [nvarchar](50) NULL,
	[LOGIN_NAME] [nvarchar](30) NULL,
	[TRUE_NAME] [nvarchar](50) NULL,
	[MENU_ID] [nvarchar](50) NULL,
	[MENU_NAME] [nvarchar](100) NULL,
	[MENU_ROUTE] [nvarchar](100) NULL,
	[REMARK] [nvarchar](255) NULL,
	[CREATE_USER] [nvarchar](50) NULL,
	[CREATE_TIME] [datetime] NULL,
	[UPDATE_USER] [nvarchar](50) NULL,
	[UPDATE_TIME] [datetime] NULL,
 CONSTRAINT [PK__SYS_QUIC__3214EC27CDE51F81] PRIMARY KEY CLUSTERED
(
	[ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]



ALTER TABLE [dbo].[SYS_QUICK_MY_COLLECTION] ADD  CONSTRAINT [DF_SYS_QUICK_MY_COLLECTION_CREATE_TIME]  DEFAULT (getdate()) FOR [CREATE_TIME]


ALTER TABLE [dbo].[SYS_QUICK_MY_COLLECTION] ADD  CONSTRAINT [DF_SYS_QUICK_MY_COLLECTION_UPDATE_TIME]  DEFAULT (getdate()) FOR [UPDATE_TIME]


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'自增主键' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_QUICK_MY_COLLECTION', @level2type=N'COLUMN',@level2name=N'ID'


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'权限平台用户ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_QUICK_MY_COLLECTION', @level2type=N'COLUMN',@level2name=N'USER_ID'


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'用户名(权限平台登录用户名)' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_QUICK_MY_COLLECTION', @level2type=N'COLUMN',@level2name=N'LOGIN_NAME'


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'真实姓名' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_QUICK_MY_COLLECTION', @level2type=N'COLUMN',@level2name=N'TRUE_NAME'


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'菜单编号' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_QUICK_MY_COLLECTION', @level2type=N'COLUMN',@level2name=N'MENU_ID'


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'菜单名称' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_QUICK_MY_COLLECTION', @level2type=N'COLUMN',@level2name=N'MENU_NAME'


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'菜单路由地址' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_QUICK_MY_COLLECTION', @level2type=N'COLUMN',@level2name=N'MENU_ROUTE'


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'备注(图标)' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_QUICK_MY_COLLECTION', @level2type=N'COLUMN',@level2name=N'REMARK'


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'创建人' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_QUICK_MY_COLLECTION', @level2type=N'COLUMN',@level2name=N'CREATE_USER'


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'创建时间' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_QUICK_MY_COLLECTION', @level2type=N'COLUMN',@level2name=N'CREATE_TIME'


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'修改人' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_QUICK_MY_COLLECTION', @level2type=N'COLUMN',@level2name=N'UPDATE_USER'


EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'修改时间' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SYS_QUICK_MY_COLLECTION', @level2type=N'COLUMN',@level2name=N'UPDATE_TIME'


CREATE TABLE dbo.SYS_CACHE_HELP
(
    ID bigint NOT NULL IDENTITY(1,1),
    QUERY_KEY nvarchar(100) NULL,
    [STATUS] nvarchar(2) NULL,
    SQL_COMMAND nvarchar(2000) NULL,
    BASE_COMMENT nvarchar(255) NULL,
    MEMO nvarchar(400) NULL,
    MEMO_DETAIL nvarchar(400) NULL,
    SYS nvarchar(30) NULL,
    LANGUAGE_TYPE nvarchar(10) NULL,
    CREATE_TIME datetime NULL,
    UPDATE_TIME datetime NULL,
    CREATE_USER nvarchar(50) NULL,
    UPDATE_USER nvarchar(50) NULL,
    PL_VERSION nvarchar(100) DEFAULT N'',
    DB_VERSION nvarchar(100) DEFAULT N'',
    CONSTRAINT PK_SYS_CACHE_HELP PRIMARY KEY (ID),
    CONSTRAINT uk_sys_cache_help UNIQUE (QUERY_KEY, SYS, LANGUAGE_TYPE)
    ) ON [PRIMARY];

