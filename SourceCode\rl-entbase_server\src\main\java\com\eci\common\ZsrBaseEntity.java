package com.eci.common;

import com.baomidou.mybatisplus.annotation.TableField;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import lombok.Data;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

// Base Entity
@Data
//@JsonInclude(JsonInclude.Include.NON_NULL)
public class ZsrBaseEntity extends EciBaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableField(exist = false)
    private Map<String, Object> extraFields;

    @JsonAnySetter
    public void push(String key, Object value) {
        if (extraFields == null) {
            extraFields = new HashMap<>();
        }
        extraFields.put(key, value);
    }

    @JsonAnyGetter
    public Map<String, Object> getExtraFields() {
        return extraFields;
    }

    public void setExtraFields(Map<String, Object> extraFields) {
        this.extraFields = extraFields;
    }
}
