package com.eci.common;

import java.time.LocalDateTime;
        import java.util.Map;
        import java.util.concurrent.ConcurrentHashMap;
        import java.util.function.Supplier;

/**
 * 缓存工具类，用于管理缓存。
 * @param <K> 缓存的 key 类型
 * @param <V> 缓存的值类型
 * <remark>使用说明：
 *      private final ZsrCacheUtil<String, String> serviceTypeNameCache = new ZsrCacheUtil<>(30); // 缓存周期 30 秒
 *       return serviceTypeNameCache.get(serviceNo, () -> {
 *             QueryWrapper<ServiceType> serviceTypeQueryWrapper = new QueryWrapper<>();
 *             serviceTypeQueryWrapper.eq("CODE", serviceNo);
 *             ServiceType serviceType = serviceTypeMapper.selectOne(serviceTypeQueryWrapper);
 *             return (serviceType != null) ? serviceType.getName() : null;
 *         });
 * </remark>
 */
public class ZsrCacheUtil<K, V> {

    private final Map<K, CacheEntry<V>> cache = new ConcurrentHashMap<>();
    private final long durationSeconds;

    private static class CacheEntry<T> {
        private final T value;
        private final LocalDateTime expiryTime;

        public CacheEntry(T value, LocalDateTime expiryTime) {
            this.value = value;
            this.expiryTime = expiryTime;
        }

        public T getValue() {
            return value;
        }

        public LocalDateTime getExpiryTime() {
            return expiryTime;
        }

        public boolean isExpired() {
            return LocalDateTime.now().isAfter(expiryTime);
        }
    }

    public ZsrCacheUtil(long durationSeconds) {
        this.durationSeconds = durationSeconds;
    }

    /**
     * 从缓存中获取值，如果不存在或已过期，则使用 supplier 重新获取并放入缓存。
     *
     * @param key      缓存的 key
     * @param supplier 用于获取值的 Supplier
     * @return 缓存中的值
     */
    public V get(K key, Supplier<V> supplier) {
        CacheEntry<V> cachedEntry = cache.get(key);
        if (cachedEntry != null && !cachedEntry.isExpired()) {
            return cachedEntry.getValue();
        }

        V value = supplier.get();
        if (value != null) {
            LocalDateTime expiryTime = LocalDateTime.now().plusSeconds(durationSeconds);
            cache.put(key, new CacheEntry<>(value, expiryTime));
        }
        return value;
    }

    /**
     * 直接从缓存中获取值，不进行过期检查。
     *
     * @param key 缓存的 key
     * @return 缓存中的值，如果不存在则返回 null
     */
    public V getIfPresent(K key) {
        CacheEntry<V> cachedEntry = cache.get(key);
        return cachedEntry != null ? cachedEntry.getValue() : null;
    }

    /**
     * 手动将值放入缓存。
     *
     * @param key   缓存的 key
     * @param value 需要缓存的值
     */
    public void put(K key, V value) {
        if (value != null) {
            LocalDateTime expiryTime = LocalDateTime.now().plusSeconds(durationSeconds);
            cache.put(key, new CacheEntry<>(value, expiryTime));
        } else {
            cache.remove(key); // 如果值为空，则移除缓存
        }
    }

    /**
     * 移除指定 key 的缓存。
     *
     * @param key 缓存的 key
     */
    public void invalidate(K key) {
        cache.remove(key);
    }

    /**
     * 清空所有缓存。
     */
    public void invalidateAll() {
        cache.clear();
    }

    /**
     * 获取当前缓存的大小。
     *
     * @return 缓存的大小
     */
    public int size() {
        return cache.size();
    }
}