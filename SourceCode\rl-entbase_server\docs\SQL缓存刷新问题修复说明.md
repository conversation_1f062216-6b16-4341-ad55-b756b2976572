# SQL缓存刷新问题修复说明

## 🐛 问题描述

用户反馈：在使用DictField配置SQL查询的字典时，即使配置了1分钟的定时刷新，程序仍然会重复执行SQL查询，而不是从缓存中读取数据。

### 问题日志
```
2025-06-25 14:20:38.953 -[]- [ForkJoinPool.commonPool-worker-5] DEBUG c.e.c.ZsrDBHelper - [logSQL,299] - 执行 QUERY SQL: SELECT CODE,NAME FROM CRM_CUSTOMER A WHERE A.STATUS='Y' 
2025-06-25 14:20:39.340 -[ea4b33af76e74ef793992c84732e2e54]- [http-nio-9527-exec-10] DEBUG c.e.c.ZsrDBHelper - [logSQL,299] - 执行 QUERY SQL: SELECT A.CODE,A.NAME FROM FZGJ_BD_OP_TYPE A
```

## 🔍 根本原因分析

### 1. **缓存架构设计缺陷**
原始设计中，SQL类型的字典使用了两层缓存：
- `DYNAMIC_DICT_CACHE`：存储SQL模板信息
- `SQL_RESULT_CACHE`：存储SQL执行结果

### 2. **刷新逻辑错误**
```java
// 原始的错误刷新逻辑
private static void refreshSqlCache(String cacheKey, DictField dictField) {
    // ❌ 只刷新了SQL模板，没有刷新实际的查询结果
    Map<String, CodeNameCommon> newCodeMap = new TreeMap<>();
    newCodeMap.put("SQL_TEMPLATE", new CodeNameCommon("SQL_TEMPLATE", "SQL_TEMPLATE", sqlTemplate));
    DYNAMIC_DICT_CACHE.put(cacheKey, newCodeMap);
}
```

### 3. **缓存命中失败**
- 定时刷新只更新了模板缓存，没有清理结果缓存
- 每次查询时，`SQL_RESULT_CACHE`中仍然有旧数据
- 但由于缓存键包含参数信息，不同参数的查询会产生不同的缓存键
- 导致看起来像是"没有缓存"，实际上是缓存键不匹配

## ✅ 修复方案

### 1. **改进刷新策略**
```java
private static void refreshSqlCache(String cacheKey, DictField dictField) {
    try {
        // 记录刷新时间
        SQL_LAST_REFRESH_TIME.put(cacheKey, System.currentTimeMillis());
        
        // 清理所有SQL结果缓存，强制下次查询重新执行SQL
        SQL_RESULT_CACHE.invalidateAll();
        
        // 保持SQL模板缓存和字典缓存不变
        SqlTemplate sqlTemplate = SQL_TEMPLATE_CACHE.get(dictField.sql());
        if (sqlTemplate != null) {
            Map<String, CodeNameCommon> newCodeMap = new TreeMap<>(String.CASE_INSENSITIVE_ORDER);
            newCodeMap.put("SQL_TEMPLATE", new CodeNameCommon("SQL_TEMPLATE", "SQL_TEMPLATE", sqlTemplate));
            DYNAMIC_DICT_CACHE.put(cacheKey, newCodeMap);
            logger.info("已刷新 SQL 缓存，清理所有SQL结果缓存：{}", cacheKey);
        }
    } catch (Exception e) {
        logger.warn("刷新 SQL 缓存失败：{}", cacheKey, e);
    }
}
```

### 2. **添加刷新时间跟踪**
```java
// 记录SQL查询的最后刷新时间
private static final Map<String, Long> SQL_LAST_REFRESH_TIME = new ConcurrentHashMap<>();

// 检查SQL缓存是否需要刷新（用于调试）
public static boolean isSqlCacheRefreshed(String cacheKey) {
    Long lastRefreshTime = SQL_LAST_REFRESH_TIME.get(cacheKey);
    return lastRefreshTime != null && (System.currentTimeMillis() - lastRefreshTime) < 70000;
}
```

### 3. **修复核心逻辑**
- **清理策略**：定时刷新时清理所有SQL结果缓存
- **强制刷新**：下次查询时必须重新执行SQL
- **日志增强**：添加详细的刷新日志，便于调试

## 🔧 修复效果

### 修复前
```
每次查询 -> 检查DYNAMIC_DICT_CACHE -> 找到SQL_TEMPLATE -> 检查SQL_RESULT_CACHE -> 找到旧结果 -> 返回旧数据
```

### 修复后
```
定时刷新 -> 清理SQL_RESULT_CACHE -> 下次查询 -> 检查DYNAMIC_DICT_CACHE -> 找到SQL_TEMPLATE -> 检查SQL_RESULT_CACHE -> 缓存为空 -> 重新执行SQL -> 获取最新数据
```

## 📊 验证方法

### 1. **日志验证**
修复后，应该看到以下日志：
```
INFO - 已刷新 SQL 缓存，清理所有SQL结果缓存：SQL_xxx
DEBUG - 执行 QUERY SQL: SELECT CODE,NAME FROM CRM_CUSTOMER A WHERE A.STATUS='Y'
```

### 2. **时间验证**
```java
// 检查缓存是否在最近70秒内刷新过
boolean isRefreshed = DictFieldUtils.isSqlCacheRefreshed("SQL_xxx");
```

### 3. **缓存统计**
```java
// 查看缓存统计信息
String stats = DictFieldUtils.getCacheStats();
```

## ⚠️ 注意事项

1. **性能影响**：修复后，SQL查询会在每次定时刷新后重新执行一次，这是预期行为
2. **缓存策略**：SQL结果缓存的过期时间设置为1分钟，与定时刷新频率一致
3. **并发安全**：所有缓存操作都是线程安全的
4. **资源管理**：应用关闭时会自动清理定时器资源

## 🎯 总结

这个修复解决了SQL字典定时刷新不生效的问题，确保：
- ✅ 定时刷新真正清理了缓存
- ✅ 下次查询会获取最新数据
- ✅ 保持了缓存的性能优势
- ✅ 提供了调试和监控能力

修复后，SQL类型的字典查询将按预期每1分钟刷新一次，获取最新的数据库数据。
