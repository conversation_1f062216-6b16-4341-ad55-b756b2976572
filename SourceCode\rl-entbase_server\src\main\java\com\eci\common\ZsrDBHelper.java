package com.eci.common;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.sql.DataSource;
import java.sql.*;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 增强版数据库助手类，支持事务管理、连接池复用和灵活查询
 */
public class ZsrDBHelper {

    private static final Logger logger = LoggerFactory.getLogger(ZsrDBHelper.class);
    private static final Pattern NAMED_PARAM_PATTERN = Pattern.compile("\\$\\{([^}]*)\\}");
    
    private static DataSource dataSource;
    private final ThreadLocal<Connection> connectionHolder = new ThreadLocal<>();
    private final ThreadLocal<Boolean> isTransactionStarted = new ThreadLocal<>();

    public static void setDataSource(DataSource dataSource) {
        ZsrDBHelper.dataSource = dataSource;
    }

    // =================== 查询操作 ===================

    /**
     * 查询返回 Map 列表
     */
    public List<Map<String, Object>> queryForList(String sql, Object... params) {
        return executeQuery(sql, ps -> {
            setParameters(ps, params);
            try (ResultSet rs = ps.executeQuery()) {
                return getResults(rs);
            }
        });
    }

    /**
     * 查询返回 Map 列表（支持命名参数）
     */
    public List<Map<String, Object>> queryForListWithNamedParams(String sql, Map<String, Object> params) {
        String renderedSql = replaceNamedParams(sql, params);
        List<Object> paramValues = new ArrayList<>(params.values());
        return executeQuery(renderedSql, ps -> {
            setParameters(ps, paramValues.toArray());
            try (ResultSet rs = ps.executeQuery()) {
                return getResults(rs);
            }
        });
    }

    // =================== 更新操作 ===================

    /**
     * 执行更新操作（INSERT / UPDATE / DELETE）
     */
    public int update(String sql, Object... params) {
        return executeUpdate(sql, ps -> {
            try {
                setParameters(ps, params);
            } catch (SQLException e) {
                throw new RuntimeException(e);
            }
            try {
                return ps.executeUpdate();
            } catch (SQLException e) {
                throw new RuntimeException(e);
            }
        });
    }

    /**
     * 执行更新操作（带命名参数）
     */
    public int updateWithNamedParams(String sql, Map<String, Object> params) {
        String renderedSql = replaceNamedParams(sql, params);
        List<Object> paramValues = new ArrayList<>(params.values());
        return executeUpdate(renderedSql, ps -> {
            try {
                setParameters(ps, paramValues.toArray());
            } catch (SQLException e) {
                throw new RuntimeException(e);
            }
            try {
                return ps.executeUpdate();
            } catch (SQLException e) {
                throw new RuntimeException(e);
            }
        });
    }

    /**
     * 批量更新操作
     */
    public int[] batchUpdate(String sql, List<Object[]> batchArgs) {
        return executeUpdate(sql, ps -> {
            for (Object[] args : batchArgs) {
                try {
                    setParameters(ps, args);
                } catch (SQLException e) {
                    throw new RuntimeException(e);
                }
                try {
                    ps.addBatch();
                } catch (SQLException e) {
                    throw new RuntimeException(e);
                }
            }
            try {
                return ps.executeBatch();
            } catch (SQLException e) {
                throw new RuntimeException(e);
            }
        });
    }

    // =================== 事务相关方法 ===================

    public void beginTransaction() {
        try {
            Connection conn = dataSource.getConnection();
            conn.setAutoCommit(false);
            connectionHolder.set(conn);
            isTransactionStarted.set(true);
        } catch (SQLException e) {
            throw new RuntimeException("开启事务失败", e);
        }
    }

    public void commit() {
        Connection conn = connectionHolder.get();
        if (conn != null && !isAutoCommit(conn)) {
            try {
                conn.commit();
            } catch (SQLException e) {
                throw new RuntimeException("提交事务失败", e);
            }
        }
    }

    public void rollback() {
        Connection conn = connectionHolder.get();
        if (conn != null && !isAutoCommit(conn)) {
            try {
                conn.rollback();
            } catch (SQLException e) {
                throw new RuntimeException("回滚事务失败", e);
            }
        }
    }

    public void endTransaction() {
        Connection conn = connectionHolder.get();
        if (conn != null) {
            try {
                conn.setAutoCommit(true);
            } catch (SQLException e) {
                logger.warn("设置 autoCommit 失败", e);
            } finally {
                closeConnection(conn);
                connectionHolder.remove();
                isTransactionStarted.remove();
            }
        }
    }

    public void executeInTransaction(Runnable operation) {
        try {
            beginTransaction();
            operation.run();
            commit();
        } catch (Exception e) {
            rollback();
            throw new RuntimeException("事务执行失败", e);
        } finally {
            endTransaction();
        }
    }

    // =================== 内部辅助方法 ===================

    private <T> T executeQuery(String sql, SqlFunction<PreparedStatement, T> handler) {
        try {
            Connection conn = getCurrentConnection();
            try (PreparedStatement ps = conn.prepareStatement(sql)) {
                logSQL(sql, "query", null);
                return handler.apply(ps);
            }
        } catch (SQLException e) {
            throw new RuntimeException("SQL 查询失败：" + sql, e);
        }
    }

    private <T> T executeUpdate(String sql, Function<PreparedStatement, T> handler) {
        try {
            Connection conn = getCurrentConnection();
            try (PreparedStatement ps = conn.prepareStatement(sql)) {
                logSQL(sql, "update", null);
                return handler.apply(ps);
            }
        } catch (SQLException e) {
            throw new RuntimeException("SQL 更新失败：" + sql, e);
        }
    }

    private void setParameters(PreparedStatement ps, Object[] params) throws SQLException {
        for (int i = 0; i < params.length; i++) {
            ps.setObject(i + 1, params[i]);
        }
    }

    private List<Map<String, Object>> getResults(ResultSet rs) throws SQLException {
        List<Map<String, Object>> result = new ArrayList<>();
        ResultSetMetaData metaData = rs.getMetaData();
        int columnCount = metaData.getColumnCount();

        while (rs.next()) {
            Map<String, Object> row = new LinkedHashMap<>();
            for (int i = 1; i <= columnCount; i++) {
                String columnName = metaData.getColumnName(i);
                Object value = rs.getObject(i);
                row.put(columnName, value);
            }
            result.add(row);
        }

        return result;
    }

    private String replaceNamedParams(String sql, Map<String, Object> params) {
        StringBuffer sb = new StringBuffer();
        Matcher matcher = NAMED_PARAM_PATTERN.matcher(sql);
        
        while (matcher.find()) {
            String paramName = matcher.group(1).trim();
            if (!params.containsKey(paramName)) {
                throw new IllegalArgumentException("未找到对应的命名参数：" + paramName);
            }
            matcher.appendReplacement(sb, "?");
        }
        matcher.appendTail(sb);
        
        return sb.toString();
    }

    private boolean isAutoCommit(Connection conn) {
        try {
            return conn.getAutoCommit();
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
    }

    public Connection getCurrentConnection() throws SQLException {
        Connection conn = connectionHolder.get();
        if (conn == null) {
            conn = dataSource.getConnection();
            connectionHolder.set(conn);
            if (isTransactionStarted.get() == null || !isTransactionStarted.get()) {
                conn.setAutoCommit(true);
            }
        }
        return conn;
    }

    public void closeConnection(Connection conn) {
        if (conn != null) {
            try {
                conn.close();
            } catch (SQLException e) {
                logger.warn("关闭连接失败", e);
            }
        }
    }

    public void releaseResources() {
        Connection conn = connectionHolder.get();
        if (conn != null && isAutoCommit(conn)) {
            closeConnection(conn);
            connectionHolder.remove();
        }
    }

    public <T> T executeWithConnection(Function<ZsrDBHelper, T> operation) {
        try {
            return operation.apply(this);
        } finally {
            releaseResources();
        }
    }

    private void logSQL(String sql, String type, Object... params) {
        if (logger.isDebugEnabled()) {
            logger.debug("执行 {} SQL: {}", type.toUpperCase(), sql);
            if (params != null && params.length > 0) {
                logger.debug("参数：{}", Arrays.toString(params));
            }
        }
    }
}