package com.eci.common.enums;

/**
 * <AUTHOR>
 * @description 公共枚举
 * @created 2024/8/27
 */
public class Enums {

    /**
     * SQL数据类型枚举
     */
    enum SqlDataType {
        TEXT,
        DATE,
        NUMBER
    }

    /**
     * 是否删除（0:未删除；1：已删除）
     */
    public enum isDelete {
        No("未删除", 1), Yes("已删除", 0);
        private final String name;
        private final int value;

        isDelete(String name, int value) {
            this.name = name;
            this.value = value;
        }

        isDelete() {
            this.name = "未删除";
            this.value = 0;
        }

        public int getValue() {
            return value;
        }

        public String getName() {
            return name;
        }

        // 获取对应取值
        public int getCode() {
            return value;
        }
    }

    /**
     * <AUTHOR>
     * @description 数据源类型
     * @created 2024-8-26 17:04:50
     */
    public enum Datasource {
        Oracle("Oracle", 101, "oracle.jdbc.driver.OracleDriver"),
        DM("DM", 102, "dm.jdbc.driver.DmDriver"),
        MySql("MySql", 103, "com.mysql.cj.jdbc.Driver"),

        IBMMQ("IBMMQ", 201, "");
        // 值
        private final int code;
        // 描述
        private final String name;
        /**
         * 驱动名称
         */
        private final String driverName;

        Datasource(String name, int code, String driverName) {
            this.code = code;
            this.name = name;
            this.driverName = driverName;
        }

        public static Datasource matchKey(int key) {
            Datasource result = null;
            for (Datasource source : values()) {
                if (source.getCode() == key) {
                    result = source;
                    break;
                }
            }
            return result;
        }

        /**
         * 根据value获取对应的数据库类型驱动名称
         *
         * @param value
         * @return
         */
        public static String getDriverNameByValue(int value) {
            for (Datasource type : Datasource.values()) {
                if (type.getCode() == value) {
                    return type.getDriverName();
                }
            }
            throw new IllegalArgumentException("No database type found for value: " + value);
        }

        /**
         * 根据value获取对应的数据库名称
         *
         * @param value
         * @return
         */
        public static String getNameByValue(int value) {
            for (Datasource type : Datasource.values()) {
                if (type.getCode() == value) {
                    return type.getName();
                }
            }
            throw new IllegalArgumentException("No database type found for value: " + value);
        }

        // 获取对应取值
        public int getCode() {
            return code;
        }

//        public static List<FtypeNnumDto> getList() {
//            List<FtypeNnumDto> dataList = new ArrayList<FtypeNnumDto>();
//            for (Datasource c : Datasource.values()) {
//
//                FtypeNnumDto ftypeNnumDto = new FtypeNnumDto();
//                ftypeNnumDto.setCode(c.getCode());
//                ftypeNnumDto.setName(c.getName());
//                dataList.add(ftypeNnumDto);
//            }
//            return dataList;
//        }

        // 获取描述对应值
        public String getName() {
            return name;
        }

        public String getDriverName() {
            return driverName;
        }

    }


    /**
     * @ClassName:IsEnabled
     * @Description:禁用/启用
     * @Author:shuairong.zeng
     * @Date:2024-8-26 17:01:33
     */
    public enum IsEnabled {
        NO("未启用", 0),
        YES("已启用", 1);

        // 值
        private final int code;
        // 描述
        private final String name;

        IsEnabled(String name, int code) {
            this.code = code;
            this.name = name;

        }

        // 获取对应取值
        public int getCode() {
            return code;
        }


        // 获取描述对应值
        public String getName() {
            return name;
        }

    }


    /**
     * <AUTHOR>
     * @description 数据库驱动
     * @created 2024-8-26 17:00:05
     */
    public enum DatasourceDriver {

        OracleDriver("oracle.jdbc.driver.OracleDriver", 101),
        DmDriver("dm.jdbc.driver.DmDriver", 102),
        MySqlDriver("com.mysql.cj.jdbc.Driver", 103);
        // 值
        private final int code;
        // 描述
        private final String name;

        DatasourceDriver(String name, int code) {
            this.code = code;
            this.name = name;

        }

        // 获取对应取值
        public int getCode() {
            return code;
        }

        // 获取描述对应值
        public String getName() {
            return name;
        }
    }

    /**
     * 数据类型
     */
    public enum dataType {
        INT("整数", 1), FLOAT("小数(保留两位)", 2), DOUBLE("小数(保留四位)", 3),
        TEXT("文本", 10), DATE1("日期(YYYY-MM-DD)", 20), DATE2("日期(YYYY-MM-DD HH:mm:ss)", 21),
        DATE3("日期(YYYYMMDD)", 22), TIME1("时间(HH:mm:ss)", 30), TIME2("时间(HH:mm)", 31);
        private final String name;
        private final int value;

        dataType(String name, int value) {
            this.name = name;
            this.value = value;
        }

        dataType() {
            this.name = "";
            this.value = 0;
        }

        /**
         * 根据value获取name
         *
         * @param value
         * @return
         */
        public static String getNameByValue(int value) {
            for (dataType v : dataType.values()) {
                if (v.getValue() == value) {
                    return v.getName();
                }
            }
            return null;
        }

        /**
         * 获取数据类型值
         *
         * @param name
         * @return
         */
        public static int getValueByName(String name) {
            for (dataType v : dataType.values()) {
                if (v.getName().equals(name)) {
                    return v.getValue();
                }
            }
            return 0;
        }

        public int getValue() {
            return value;
        }

        public String getName() {
            return name;
        }
    }

    public enum RoleType {
        NODE("操作点/组织", "0"),
        USER("登录人", "R"),
        COMPANY("公司", "C"),
        GROUP("集团", "G"),
        RIGHT("数据授权", "S");

        // 值
        private final String code;
        // 描述
        private final String name;

        RoleType(String name, String code) {
            this.code = code;
            this.name = name;
        }

        // 获取对应取值
        public String getCode() {
            return code;
        }

        // 获取描述对应值
        public String getName() {
            return name;
        }
    }

    public enum PreOrderCh {
        TG("通过", "TG"),
        TH("退回", "TH");

        // 值
        private final String code;
        // 描述
        private final String name;

        PreOrderCh(String name, String code) {
            this.code = code;
            this.name = name;
        }

        // 获取对应取值
        public String getCode() {
            return code;
        }

        // 获取描述对应值
        public String getName() {
            return name;
        }
    }

    public enum PreOrderStatus {
        ZC("暂存", "ZC"),
        XDDS("下单待审", "XDDS"),
        YSL("已受理", "YSL"),
        ZYWC("作业完成", "ZYWC"),
        SHTH("审核退回", "SHTH");

        // 值
        private final String code;
        // 描述
        private final String name;

        PreOrderStatus(String name, String code) {
            this.code = code;
            this.name = name;
        }

        // 获取对应取值
        public String getCode() {
            return code;
        }

        // 获取描述对应值
        public String getName() {
            return name;
        }
    }

    /**
     * 东盟车辆修改标志
     */
    public enum ModMark {
        BB("不变", "0"), XG("修改", "1"), SC("删除", "2"), XZ("新增", "3");
        private final String name;
        private final String value;

        ModMark(String name, String value) {
            this.name = name;
            this.value = value;
        }

        ModMark() {
            this.name = "不变";
            this.value = "0";
        }

        public String getValue() {
            return value;
        }

        public String getName() {
            return name;
        }
    }

    public enum OrderStatus {
        ZC("暂存", "ZC"),
        SX("生效", "SX"),
        JA("结案", "JA"),
        ZF("作废", "ZF"),
        TD("退单", "TD");

        // 值
        private final String code;
        // 描述
        private final String name;

        OrderStatus(String name, String code) {
            this.code = code;
            this.name = name;
        }

        // 获取对应取值
        public String getCode() {
            return code;
        }

        // 获取描述对应值
        public String getName() {
            return name;
        }
    }

    public enum YNStatus {
        Y("是", "Y"),
        N("否", "N");

        // 值
        private final String code;
        // 描述
        private final String name;

        YNStatus(String name, String code) {
            this.code = code;
            this.name = name;
        }

        // 获取对应取值
        public String getCode() {
            return code;
        }

        // 获取描述对应值
        public String getName() {
            return name;
        }
    }

}