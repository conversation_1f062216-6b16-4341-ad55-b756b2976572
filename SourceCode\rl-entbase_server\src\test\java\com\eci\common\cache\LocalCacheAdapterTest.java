package com.eci.common.cache;

import com.eci.common.cache.adapter.CacheAdapter;
import com.eci.common.cache.adapter.LocalCacheAdapter;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;

/**
 * LocalCacheAdapter 单元测试
 */
public class LocalCacheAdapterTest {

    private LocalCacheAdapter<String, String> cache;

    @BeforeEach
    void setUp() {
        cache = new LocalCacheAdapter<>(100, 1, TimeUnit.SECONDS, true);
    }

    @AfterEach
    void tearDown() {
        if (cache != null) {
            cache.shutdown();
        }
    }

    @Test
    void testBasicOperations() {
        // 测试基本的 put/get 操作
        cache.put("key1", "value1");
        assertEquals("value1", cache.get("key1"));
        
        // 测试不存在的键
        assertNull(cache.get("nonexistent"));
        
        // 测试 containsKey
        assertTrue(cache.containsKey("key1"));
        assertFalse(cache.containsKey("nonexistent"));
    }

    @Test
    void testPermanentCache() {
        // 测试永久缓存
        cache.putPermanent("permanent_key", "permanent_value");
        assertEquals("permanent_value", cache.get("permanent_key"));
        
        // 等待超过过期时间
        try {
            Thread.sleep(1500);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        // 永久缓存应该仍然存在
        assertEquals("permanent_value", cache.get("permanent_key"));
    }

    @Test
    void testExpiration() {
        // 测试过期机制
        cache.put("temp_key", "temp_value", 500, TimeUnit.MILLISECONDS);
        assertEquals("temp_value", cache.get("temp_key"));
        
        // 等待过期
        try {
            Thread.sleep(600);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        // 应该已经过期
        assertNull(cache.get("temp_key"));
    }

    @Test
    void testGetWithLoader() {
        // 测试带加载器的 get 方法
        String result = cache.get("load_key", key -> "loaded_" + key);
        assertEquals("loaded_load_key", result);
        
        // 第二次调用应该从缓存获取
        String result2 = cache.get("load_key", key -> "should_not_be_called");
        assertEquals("loaded_load_key", result2);
    }

    @Test
    void testBatchOperations() {
        // 测试批量操作
        Map<String, String> batch = new HashMap<>();
        batch.put("batch1", "value1");
        batch.put("batch2", "value2");
        batch.put("batch3", "value3");
        
        cache.putAll(batch);
        
        assertEquals("value1", cache.get("batch1"));
        assertEquals("value2", cache.get("batch2"));
        assertEquals("value3", cache.get("batch3"));
    }

    @Test
    void testRemoveOperations() {
        // 测试删除操作
        cache.put("remove_key", "remove_value");
        assertTrue(cache.containsKey("remove_key"));
        
        boolean removed = cache.remove("remove_key");
        assertTrue(removed);
        assertFalse(cache.containsKey("remove_key"));
        
        // 删除不存在的键
        boolean notRemoved = cache.remove("nonexistent");
        assertFalse(notRemoved);
    }

    @Test
    void testCacheStats() {
        // 测试缓存统计
        cache.put("stats_key", "stats_value");
        cache.get("stats_key"); // 命中
        cache.get("nonexistent"); // 未命中
        
        CacheAdapter.CacheStats stats = cache.getStats();
        assertNotNull(stats);
        assertTrue(stats.getHitCount() > 0);
        assertTrue(stats.getMissCount() > 0);
    }

    @Test
    void testCacheType() {
        assertEquals(CacheAdapter.CacheType.LOCAL, cache.getType());
    }

    @Test
    void testAvailability() {
        assertTrue(cache.isAvailable());
    }

    @Test
    void testSize() {
        assertEquals(0, cache.size());
        
        cache.put("size1", "value1");
        cache.put("size2", "value2");
        assertEquals(2, cache.size());
        
        cache.putPermanent("permanent", "value");
        assertEquals(3, cache.size());
    }

    @Test
    void testClear() {
        cache.put("clear1", "value1");
        cache.putPermanent("clear2", "value2");
        assertEquals(2, cache.size());
        
        cache.clear();
        assertEquals(0, cache.size());
        assertNull(cache.get("clear1"));
        assertNull(cache.get("clear2"));
    }

    @Test
    void testRefresh() {
        cache.put("refresh_key", "refresh_value");
        assertTrue(cache.refresh("refresh_key"));
        
        // 刷新不存在的键
        assertFalse(cache.refresh("nonexistent"));
    }

    @Test
    void testExpire() {
        cache.putPermanent("expire_key", "expire_value");
        
        // 设置过期时间
        assertTrue(cache.expire("expire_key", 100, TimeUnit.MILLISECONDS));
        
        // 等待过期
        try {
            Thread.sleep(150);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        // 应该已经过期
        assertNull(cache.get("expire_key"));
    }

    @Test
    void testGetExpire() {
        // 永久缓存
        cache.putPermanent("permanent", "value");
        assertEquals(-1, cache.getExpire("permanent", TimeUnit.SECONDS));
        
        // 临时缓存
        cache.put("temp", "value", 10, TimeUnit.SECONDS);
        long ttl = cache.getExpire("temp", TimeUnit.SECONDS);
        assertTrue(ttl > 0);
        
        // 不存在的键
        assertEquals(-2, cache.getExpire("nonexistent", TimeUnit.SECONDS));
    }

    @Test
    void testConcurrentAccess() throws InterruptedException {
        // 测试并发访问
        int threadCount = 10;
        int operationsPerThread = 100;
        Thread[] threads = new Thread[threadCount];
        
        for (int i = 0; i < threadCount; i++) {
            final int threadId = i;
            threads[i] = new Thread(() -> {
                for (int j = 0; j < operationsPerThread; j++) {
                    String key = "thread_" + threadId + "_key_" + j;
                    String value = "thread_" + threadId + "_value_" + j;
                    
                    cache.put(key, value);
                    assertEquals(value, cache.get(key));
                }
            });
        }
        
        // 启动所有线程
        for (Thread thread : threads) {
            thread.start();
        }
        
        // 等待所有线程完成
        for (Thread thread : threads) {
            thread.join();
        }
        
        // 验证缓存大小
        assertEquals(threadCount * operationsPerThread, cache.size());
    }

    @Test
    void testMemoryPressure() {
        // 测试内存压力下的行为
        LocalCacheAdapter<String, String> smallCache = 
            new LocalCacheAdapter<>(10, 1, TimeUnit.MINUTES, true);
        
        try {
            // 添加超过最大大小的数据
            for (int i = 0; i < 20; i++) {
                smallCache.put("key_" + i, "value_" + i);
            }
            
            // 缓存大小应该被限制
            assertTrue(smallCache.size() <= 10);
            
        } finally {
            smallCache.shutdown();
        }
    }
}
