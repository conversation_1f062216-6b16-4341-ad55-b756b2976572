package com.eci.project.fzgjGoodsPackType.dao;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import com.eci.crud.dao.EciBaseDao;
import com.eci.project.fzgjGoodsPackType.entity.FzgjGoodsPackTypeEntity;


/**
* 包装类型Dao层
* 接口层, 直接查询数据库使用
* @<NAME_EMAIL>
* @date 2025-06-26
*/
public interface FzgjGoodsPackTypeDao extends EciBaseDao<FzgjGoodsPackTypeEntity> {

}