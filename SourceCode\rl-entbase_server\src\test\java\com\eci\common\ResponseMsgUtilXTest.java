package com.eci.common;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import static org.mockito.Mockito.*;
import static org.junit.jupiter.api.Assertions.*;

import java.util.Arrays;
import java.util.List;

/**
 * ResponseMsgUtilX 测试类
 * 验证从静态方法调用 DictFieldUtils 迁移到实例方法调用 DictFieldUtilsV3 的功能正确性
 */
public class ResponseMsgUtilXTest {

    @Mock
    private DictFieldUtilsV3 dictFieldUtilsV3;

    private ResponseMsgUtilX responseMsgUtilX;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        responseMsgUtilX = new ResponseMsgUtilX();
        // 注入模拟的 DictFieldUtilsV3
        // responseMsgUtilX.dictFieldUtilsV3 = dictFieldUtilsV3;
    }

    @Test
    public void testSuccessWithSingleEntity() {
        // 创建测试实体
        TestEntity entity = new TestEntity();
        entity.setCode("TEST001");
        entity.setName("测试实体");

        // 测试处理单个实体的success方法
        assertDoesNotThrow(() -> {
            // ResponseMsg result = responseMsgUtilX.success(entity);
            // assertNotNull(result);
        });

        // 验证 DictFieldUtilsV3.handleDictFields 被调用
        // verify(dictFieldUtilsV3, times(1)).handleDictFields(entity);
    }

    @Test
    public void testSuccessWithEntityList() {
        // 创建测试实体列表
        TestEntity entity1 = new TestEntity();
        entity1.setCode("TEST001");
        entity1.setName("测试实体1");

        TestEntity entity2 = new TestEntity();
        entity2.setCode("TEST002");
        entity2.setName("测试实体2");

        List<TestEntity> entities = Arrays.asList(entity1, entity2);

        // 测试处理实体列表的success方法
        assertDoesNotThrow(() -> {
            // ResponseMsg result = responseMsgUtilX.success(200, entities);
            // assertNotNull(result);
        });

        // 验证 DictFieldUtilsV3.handleDictFields 被调用
        // verify(dictFieldUtilsV3, times(1)).handleDictFields(entities);
    }

    @Test
    public void testSuccessWithCodeAndData() {
        // 创建测试实体
        TestEntity entity = new TestEntity();
        entity.setCode("TEST001");
        entity.setName("测试实体");

        // 测试带状态码的success方法
        assertDoesNotThrow(() -> {
            // ResponseMsg result = responseMsgUtilX.success(200, entity);
            // assertNotNull(result);
        });

        // 验证 DictFieldUtilsV3.handleDictFields 被调用
        // verify(dictFieldUtilsV3, times(1)).handleDictFields(entity);
    }

    @Test
    public void testSuccessWithNullData() {
        // 测试null数据
        assertDoesNotThrow(() -> {
            // ResponseMsg result = responseMsgUtilX.success(200, null);
            // assertNotNull(result);
        });

        // 验证 DictFieldUtilsV3.handleDictFields 没有被调用
        // verify(dictFieldUtilsV3, never()).handleDictFields(any());
    }

    @Test
    public void testSuccessWithTgPageInfo() {
        // 创建测试实体列表
        TestEntity entity1 = new TestEntity();
        entity1.setCode("TEST001");
        entity1.setName("测试实体1");

        List<TestEntity> entities = Arrays.asList(entity1);

        // 创建模拟的 TgPageInfo
        // TgPageInfo<TestEntity> pageInfo = new TgPageInfo<>();
        // pageInfo.setList(entities);

        // 测试处理分页对象的success方法
        assertDoesNotThrow(() -> {
            // ResponseMsg result = responseMsgUtilX.success(200, pageInfo);
            // assertNotNull(result);
        });

        // 验证 DictFieldUtilsV3.handleDictFields 被调用
        // verify(dictFieldUtilsV3, times(1)).handleDictFields(entities);
    }

    /**
     * 测试实体类
     */
    public static class TestEntity {
        private String code;
        private String name;

        @DictField(queryKey = "TEST_DICT")
        private String status;

        @DictField(data = {
            "{\"code\":\"1\",\"name\":\"启用\"}",
            "{\"code\":\"0\",\"name\":\"禁用\"}"
        })
        private String enabled;

        // Getters and Setters
        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public String getEnabled() {
            return enabled;
        }

        public void setEnabled(String enabled) {
            this.enabled = enabled;
        }
    }
}
