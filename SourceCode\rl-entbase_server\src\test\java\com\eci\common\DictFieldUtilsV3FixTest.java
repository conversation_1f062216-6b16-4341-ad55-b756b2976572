package com.eci.common;

import com.alibaba.fastjson.JSONObject;
import com.eci.common.cache.adapter.CacheAdapter;
import com.eci.common.cache.adapter.LocalCacheAdapter;
import com.eci.common.cache.adapter.RedisCacheAdapter;
import com.eci.common.cache.config.CacheConfigDetector;
import com.eci.common.cache.factory.CacheFactory;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * DictFieldUtilsV3修复验证测试
 */
@ExtendWith(MockitoExtension.class)
public class DictFieldUtilsV3FixTest {

    private static final Logger logger = LoggerFactory.getLogger(DictFieldUtilsV3FixTest.class);

    @Mock
    private CacheFactory cacheFactory;

    @Mock
    private CacheConfigDetector configDetector;

    private DictFieldUtilsV3 dictFieldUtils;

    @BeforeEach
    void setUp() {
        dictFieldUtils = new DictFieldUtilsV3();
        // 注入mock对象
        setField(dictFieldUtils, "cacheFactory", cacheFactory);
    }

    /**
     * 测试SQL模板JSON反序列化问题修复
     */
    @Test
    void testSqlTemplateJsonDeserialization() {
        logger.info("测试SQL模板JSON反序列化问题修复");

        // 创建一个包含SqlTemplate的CodeNameCommon
        SqlTemplate originalTemplate = new SqlTemplate("SELECT * FROM test WHERE id = ${id}");
        CodeNameCommon original = new CodeNameCommon("SQL_TEMPLATE", "SQL_TEMPLATE", originalTemplate);

        // 模拟JSON序列化和反序列化过程
        String json = com.alibaba.fastjson.JSON.toJSONString(original);
        logger.debug("序列化JSON: {}", json);

        // 反序列化
        CodeNameCommon deserialized = com.alibaba.fastjson.JSON.parseObject(json, CodeNameCommon.class);
        
        // 验证反序列化结果
        assertNotNull(deserialized);
        assertEquals("SQL_TEMPLATE", deserialized.getCode());
        assertEquals("SQL_TEMPLATE", deserialized.getName());
        
        // 检查SqlTemplate是否正确反序列化
        Object sqlTemplateObj = deserialized.getSqlTemplate();
        logger.debug("反序列化的SqlTemplate类型: {}", sqlTemplateObj != null ? sqlTemplateObj.getClass() : "null");
        
        if (sqlTemplateObj instanceof JSONObject) {
            logger.info("SqlTemplate被反序列化为JSONObject，这是预期的问题");
            
            // 测试convertJsonObjectToSqlTemplate方法
            JSONObject jsonObj = (JSONObject) sqlTemplateObj;
            SqlTemplate converted = convertJsonObjectToSqlTemplate(jsonObj);
            
            assertNotNull(converted, "应该能够从JSONObject转换为SqlTemplate");
            assertEquals(originalTemplate.getRenderedSql(), converted.getRenderedSql());
            assertEquals(originalTemplate.getParamNames(), converted.getParamNames());
        } else if (sqlTemplateObj instanceof SqlTemplate) {
            logger.info("SqlTemplate正确反序列化");
            SqlTemplate template = (SqlTemplate) sqlTemplateObj;
            assertEquals(originalTemplate.getRenderedSql(), template.getRenderedSql());
            assertEquals(originalTemplate.getParamNames(), template.getParamNames());
        } else {
            fail("SqlTemplate反序列化为未知类型: " + (sqlTemplateObj != null ? sqlTemplateObj.getClass() : "null"));
        }
    }

    /**
     * 测试缓存策略一致性
     */
    @Test
    void testCacheStrategyConsistency() {
        logger.info("测试缓存策略一致性");

        // 模拟CacheConfigDetector检测到Redis可用
        CacheConfigDetector.CacheConfig config = mock(CacheConfigDetector.CacheConfig.class);
        when(config.isRedisAvailable()).thenReturn(true);
        when(config.getStrategy()).thenReturn(CacheConfigDetector.CacheStrategy.REDIS);
        when(config.getLocalCacheExpireMinutes()).thenReturn(60L);

        when(configDetector.getCacheConfig()).thenReturn(config);
        when(configDetector.getCurrentStrategy()).thenReturn(CacheConfigDetector.CacheStrategy.REDIS);

        // 模拟缓存适配器创建
        CacheAdapter<String, Map<String, CodeNameCommon>> mockCache = mock(CacheAdapter.class);
        when(cacheFactory.createCache(any(), any(), any())).thenReturn(mockCache);
        when(cacheFactory.createCache(any(), any(), any(), any(), any(), any())).thenReturn(mockCache);
        when(cacheFactory.getConfigDetector()).thenReturn(configDetector);

        // 验证策略一致性
        assertEquals(CacheConfigDetector.CacheStrategy.REDIS, configDetector.getCurrentStrategy());
        assertTrue(config.isRedisAvailable());
    }

    /**
     * 测试Redis可用性检测改进
     */
    @Test
    void testRedisAvailabilityDetection() {
        logger.info("测试Redis可用性检测改进");

        // 创建本地缓存适配器进行对比测试
        LocalCacheAdapter<String, String> localCache = new LocalCacheAdapter<>(100, 1, TimeUnit.MINUTES, true);
        
        // 验证本地缓存始终可用
        assertTrue(localCache.isAvailable());
        
        // 测试基本操作
        localCache.put("test", "value");
        assertEquals("value", localCache.get("test"));
        assertTrue(localCache.containsKey("test"));
        
        localCache.shutdown();
    }

    /**
     * 测试SqlTemplate构造函数
     */
    @Test
    void testSqlTemplateConstructors() {
        logger.info("测试SqlTemplate构造函数");

        String originalSql = "SELECT * FROM users WHERE name = ${name} AND age > ${age}";
        
        // 测试主构造函数
        SqlTemplate template1 = new SqlTemplate(originalSql);
        assertEquals(originalSql, template1.getOriginalSql());
        assertEquals("SELECT * FROM users WHERE name = ? AND age > ?", template1.getRenderedSql());
        assertEquals(Arrays.asList("name", "age"), template1.getParamNames());
        
        // 测试反序列化构造函数
        SqlTemplate template2 = new SqlTemplate(originalSql, "SELECT * FROM users WHERE name = ? AND age > ?", 
                Arrays.asList("name", "age"));
        assertEquals(originalSql, template2.getOriginalSql());
        assertEquals("SELECT * FROM users WHERE name = ? AND age > ?", template2.getRenderedSql());
        assertEquals(Arrays.asList("name", "age"), template2.getParamNames());
    }

    /**
     * 辅助方法：使用反射设置私有字段
     */
    private void setField(Object target, String fieldName, Object value) {
        try {
            java.lang.reflect.Field field = target.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            field.set(target, value);
        } catch (Exception e) {
            throw new RuntimeException("Failed to set field: " + fieldName, e);
        }
    }

    /**
     * 复制DictFieldUtilsV3中的convertJsonObjectToSqlTemplate方法用于测试
     */
    private SqlTemplate convertJsonObjectToSqlTemplate(JSONObject jsonObj) {
        try {
            String originalSql = jsonObj.getString("originalSql");
            String renderedSql = jsonObj.getString("renderedSql");
            com.alibaba.fastjson.JSONArray paramNamesArray = jsonObj.getJSONArray("paramNames");
            
            if (originalSql != null) {
                // 如果有原始SQL，直接用它创建SqlTemplate（会重新解析）
                return new SqlTemplate(originalSql);
            } else if (renderedSql != null && paramNamesArray != null) {
                // 如果没有原始SQL但有渲染后的SQL和参数列表，尝试重构
                List<String> paramNames = new ArrayList<>();
                for (int i = 0; i < paramNamesArray.size(); i++) {
                    paramNames.add(paramNamesArray.getString(i));
                }
                // 使用反序列化构造函数
                return new SqlTemplate(renderedSql, renderedSql, paramNames);
            }
        } catch (Exception e) {
            logger.warn("转换JSONObject为SqlTemplate失败", e);
        }
        return null;
    }
}
