package com.eci.common;

import java.text.SimpleDateFormat;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.atomic.AtomicLong;

/**
 * @ClassName: NoManager
 * @Author: guangyan.mei
 * @Date: 2025/4/22 9:34
 * @Description: TODO
 */
public class NoManager {

    // 每秒内使用的序号（0~99）
    private static final AtomicLong sequence = new AtomicLong(0);
    // 最近一次时间戳（秒级）
    private static volatile long lastSecond = -1L;

    private static final DateTimeFormatter YEAR_MONTH_FORMATTER = DateTimeFormatter.ofPattern("yyyyMM");

    /// <summary>
    /// 创建订单号
    /// </summary>
    /// <param name="type">订单类型，'0' 表示接收，'1' 表示自行创建</param>
    /// <returns>生成的订单号</returns>
    public static String createOrderNo(char type) {
        // 1. 获取当前年月（6位数）
        String yearMonth = YearMonth.now().format(YEAR_MONTH_FORMATTER);

        // 2. 生成 6 位随机流水号
        // 使用 ThreadLocalRandom 获取当前线程的随机数生成器
        int randomNum = ThreadLocalRandom.current().nextInt(1000000);

        // 格式化为 6 位数字（不足补零）
        String sixDigitRandomCode = String.format("%06d", randomNum);

        // 3. 组合订单号：第一位数 + 年月 + 随机流水号
        StringBuilder orderNoBuilder = new StringBuilder();
        orderNoBuilder.append(type); // 添加第一位数 ('0' 或 '1')
        orderNoBuilder.append(yearMonth); // 添加当前年月
        orderNoBuilder.append(sixDigitRandomCode); // 添加 6 位随机流水号

        return orderNoBuilder.toString();
    }

    /// <summary>
    /// 创建OMS订单号
    /// </summary>
    /// <param name="prefixCode"></param>
    /// <param name="ts"></param>
    /// <returns></returns>
    public static String createOrderNo(String prefixCode) {
        // 生成规则：0+年（当前年月6位数）+ 6位流水号
        long currentTimeMillis = System.currentTimeMillis();
        long currentSecond = currentTimeMillis / 1000;

        synchronized (NoManager.class) {
            if (currentSecond != lastSecond) {
                lastSecond = currentSecond;
                sequence.set(0L); // 重置序号
            }
        }

        // 获取序号
        long seq = sequence.incrementAndGet();

        // 时间部分：取当前秒的后 4 位
        long timePart = currentSecond % 10000;

        // 构造 6 位流水号：timePart * 100 + seq
        long code = timePart * 100 + seq;

        // 格式化为 6 位数字（不足补零）
        String sixDigitCode = String.format("%06d", code);

        // 格式化年份为 4 位
        String year = new SimpleDateFormat("yyyyMM").format(new Date(currentTimeMillis));

        // 返回完整订单号
        return "0" + year + sixDigitCode;

        // return NoHelper.GetNo("OMS_ORDER_NO", "O" + prefixCode + DateTime.Now.ToyyMM(), 6, "OMS订单号", string.Empty, ts);
    }

    /// <summary>
    /// 创建协作任务编号
    /// </summary>
    /// <param name="prefixCode"></param>
    /// <param name="ts"></param>
    /// <returns></returns>
    public static String createWorkNo(String orderNo, String fwxmCode) {
        // 生成规则：订单编号+fwxmCode+2位流水号 这里要改，orderNo和fwxmCode可能为空
        StringBuilder prefixBuilder = new StringBuilder();

        // 处理 orderNo 非空情况
        if (orderNo != null && !orderNo.trim().isEmpty()) {
            prefixBuilder.append(orderNo);
        }
        prefixBuilder.append("-");

        // 处理 fwxmCode 非空情况
        if (fwxmCode != null && !fwxmCode.trim().isEmpty()) {
            prefixBuilder.append(fwxmCode);
        }
        prefixBuilder.append("-");

        // 获取当前时间的分钟部分
        String minutes = new SimpleDateFormat("MM").format(new Date(System.currentTimeMillis()));

        // 组合最终结果
        return prefixBuilder.toString() + minutes;
    }


    /// <summary>
    /// 创建OMS订单模板编号
    /// </summary>
    /// <param name="prefixCode"></param>
    /// <param name="ts"></param>
    /// <returns></returns>
    public static String createOrderTemplateNo(String prefixCode) {

        // 生成规则：JDMB+年份（当前）+8位流水号
        long currentTimeMillis = System.currentTimeMillis();
        long currentSecond = currentTimeMillis / 1000;

        synchronized (NoManager.class) {
            if (currentSecond != lastSecond) {
                lastSecond = currentSecond;
                sequence.set(0); // 每秒重置序号
            }
        }

        // 获取当前时间（小时和分钟）
        SimpleDateFormat sdf = new SimpleDateFormat("HHmm");
        String timePart = sdf.format(new Date(currentTimeMillis));

        // 获取当前序列号
        long seq = sequence.incrementAndGet();

        // 格式化为 4 位序列号（不足补零）
        String seqStr = String.format("%04d", seq);

        // 格式化年份为 4 位
        String year = new SimpleDateFormat("yyyy").format(new Date(currentTimeMillis));
        // 返回完整订单号
        return "JDMB" + year + timePart + seqStr;
        //return NoHelper.GetNo("OMS_JDMB_NO", "JDMB" + prefixCode, 8, "OMS订单模板编号", string.Empty, ts);
    }


    /// <summary>
    /// 创建客户自助下单 客户委托单协同编号，规则XTDM1908000201
    /// </summary>
    /// <param name="type">订单类型，'0' 表示接收，'1' 表示自行创建</param>
    /// <returns>生成的订单号</returns>
    public static String createOrderPreNo() {
        // 1. 获取当前年月（6位数）
        String yearMonth = YearMonth.now().format(YEAR_MONTH_FORMATTER);

        // 2. 生成 6 位随机流水号
        // 使用 ThreadLocalRandom 获取当前线程的随机数生成器
        int randomNum = ThreadLocalRandom.current().nextInt(1000000);

        // 格式化为 6 位数字（不足补零）
        String sixDigitRandomCode = String.format("%06d", randomNum);

        // 3. 组合订单号：第一位数 + 年月 + 随机流水号
        StringBuilder orderNoBuilder = new StringBuilder();
        orderNoBuilder.append("XTDM"); // 添加第一位数 ('0' 或 '1')
        orderNoBuilder.append(yearMonth); // 添加当前年月
        orderNoBuilder.append(sixDigitRandomCode); // 添加 6 位随机流水号

        return orderNoBuilder.toString();
    }
}
