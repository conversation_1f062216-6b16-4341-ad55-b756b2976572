package com.eci.common;


import com.eci.common.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.Field;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 字典处理工具类：自动将带有 @DictField 注解的字段映射为字典名称或格式化时间
 */
public class DictFieldUtils2 {

    private static final Logger logger = LoggerFactory.getLogger(DictFieldUtils2.class);


    // 缓存：实体类 -> 带有 DictField 注解的字段和注解的映射
    private static final Map<Class<?>, Map<Field, DictField>> DICT_FIELD_CACHE =
            new ConcurrentHashMap<>();


    // 设置默认缓存时间：5分钟 = 300000 毫秒
    private static final ExpiringMapCache<String, Map<String, CodeNameCommon>> DICT_DATA_CACHE =
            new ExpiringMapCache<>(300_000);

    private static final ExpiringMapCache<String, List<Map<String, Object>>> SQL_RESULT_CACHE =
            new ExpiringMapCache<>(300_000);

    // 缓存：SQL模板 -> 避免重复解析
    private static final Map<String, SqlTemplate> SQL_TEMPLATE_CACHE = new ConcurrentHashMap<>();

    private static final ZsrDBHelper zsrDBHelper = new ZsrDBHelper();

    /**
     * 处理单个实体的字典字段
     */
    public static <T> void handleDictFields(T entity) {
        if (entity == null) return;
        handleDictFields(Collections.singletonList(entity));
    }

    /**
     * 处理实体列表的字典字段
     */
    public static <T> void handleDictFields(List<T> entities) {
        if (CollectionUtils.isEmpty(entities)) return;
        Class<?> entityClass = entities.get(0).getClass();
        Map<Field, DictField> annotatedFields = getAnnotatedFields(entityClass);
        if (annotatedFields.isEmpty()) return;

        // 查询字典数据
        Map<String, Map<String, CodeNameCommon>> dictDataMap = fetchDictData(annotatedFields.values());

        // 使用并行流处理实体
        entities.parallelStream().forEach(entity -> {
            if (entity != null) {
                annotatedFields.forEach((field, dictField) -> {
                    try {
                        processField(entity, field, dictField, dictDataMap);
                    } catch (Exception e) {
                        logger.error("处理字典字段异常: {}.{}", entityClass.getSimpleName(), field.getName(), e);
                    }
                });
            }
        });
    }

    /**
     * 获取所有带 DictField 注解的字段（包括父类）
     */
    private static Map<Field, DictField> getAnnotatedFields(Class<?> clazz) {
        Map<Field, DictField> cached = DICT_FIELD_CACHE.computeIfAbsent(clazz, cls -> {
            List<Field> allFields = getAllFields(cls);
            return allFields.stream()
                    .filter(f -> f.isAnnotationPresent(DictField.class))
                    .collect(Collectors.toMap(
                            f -> f,
                            f -> f.getAnnotation(DictField.class),
                            (f1, f2) -> f1
                    ));
        });

        return new HashMap<>(cached); // 返回副本以避免并发问题
    }

    /**
     * 获取类及其父类的所有字段
     */
    private static List<Field> getAllFields(Class<?> clazz) {
        List<Field> fields = new ArrayList<>();
        Class<?> currentClass = clazz;
        while (currentClass != null && !currentClass.equals(Object.class)) {
            Collections.addAll(fields, currentClass.getDeclaredFields());
            currentClass = currentClass.getSuperclass();
        }
        return fields;
    }

    /**
     * 批量查询字典数据
     */
    private static Map<String, Map<String, CodeNameCommon>> fetchDictData(Collection<DictField> fields) {
        Map<String, Map<String, CodeNameCommon>> result = new HashMap<>();
        for (DictField dictField : fields) {
            String cacheKey = generateCacheKey(dictField);

            // 检查缓存
            Map<String, CodeNameCommon> cachedData = DICT_DATA_CACHE.get(cacheKey);
            if (cachedData != null) {
                result.put(cacheKey, cachedData);
                continue;
            }

            Map<String, CodeNameCommon> codeMap = new TreeMap<>(String.CASE_INSENSITIVE_ORDER);
            try {
                if (StringUtils.isNotBlank(dictField.queryKey())) {
                    codeMap = DataDictUtils.queryCodeNameMap(dictField.queryKey());
                } else if (dictField.data().length > 0) {
                    codeMap = parseDataFromJsonArray(dictField.data());
                } else if (StringUtils.isNotBlank(dictField.sql())) {
                    SqlTemplate sqlTemplate = SQL_TEMPLATE_CACHE.computeIfAbsent(
                            dictField.sql(),
                            SqlTemplate::new
                    );
                    codeMap.put("SQL_TEMPLATE", new CodeNameCommon("SQL_TEMPLATE", "SQL_TEMPLATE", sqlTemplate));
                }

                // 更新缓存
                DICT_DATA_CACHE.put(cacheKey, codeMap);
                result.put(cacheKey, codeMap);
            } catch (Exception e) {
                logger.warn("获取字典数据失败: {}", cacheKey, e);
                result.put(cacheKey, Collections.emptyMap());
            }
        }
        return result;
    }

    /**
     * 生成缓存 key
     */
    private static String generateCacheKey(DictField dictField) {
        if (StringUtils.isNotBlank(dictField.queryKey())) {
            return "QK_" + dictField.queryKey();
        } else if (dictField.data().length > 0) {
            return "DS_" + Arrays.hashCode(dictField.data());
        } else if (StringUtils.isNotBlank(dictField.sql())) {
            return "SQL_" + dictField.sql();
        }
        return "EMPTY";
    }

    /**
     * 处理单个字段
     */
    private static <T> void processField(T entity, Field field, DictField dictField,
                                         Map<String, Map<String, CodeNameCommon>> dictDataMap) throws Exception {
        field.setAccessible(true);
        Object value = field.get(entity);
        if (value == null) return;

        if (dictField.useDateFormat()) {
            String formattedDate = formatDateValue(value, dictField.dateFormat());
            if (formattedDate != null) {
                setFieldValue(entity, field.getName() + "Display", formattedDate);
            }
        } else {
            String cacheKey = generateCacheKey(dictField);
            Map<String, CodeNameCommon> codeMap = dictDataMap.get(cacheKey);
            if (codeMap != null) {
                CodeNameCommon templateEntry = codeMap.get("SQL_TEMPLATE");
                if (templateEntry != null && templateEntry.getSqlTemplate() != null) {
                    processSqlTemplate(entity, field, value, templateEntry.getSqlTemplate(), dictField);
                } else {
                    processCodeName(entity, field, value, codeMap, dictField);
                }
            }
        }
    }

    /**
     * 处理SQL模板
     */
    private static <T> void processSqlTemplate(T entity, Field field, Object value,
                                               SqlTemplate template, DictField dictField) throws Exception {
        Map<String, Object> params = new HashMap<>();
        List<String> paramNames = template.getParamNames();
        for (String paramName : paramNames) {
            Field paramField = entity.getClass().getDeclaredField(paramName);
            paramField.setAccessible(true);
            params.put(paramName, paramField.get(entity));
        }

        String sql = template.getRenderedSql();
        String cacheKey = generateSqlCacheKey(sql, params);

        List<Map<String, Object>> rows = SQL_RESULT_CACHE.get(cacheKey);
        if (rows == null) {
            rows = executeDynamicSql(sql, params);
            if (rows != null && !rows.isEmpty()) {
                SQL_RESULT_CACHE.put(cacheKey, rows);
            }
        }

        if (!rows.isEmpty()) {
            String name = getNameByCodeStreamFindFirst(rows, value.toString());
            if (name != null) {
                setFieldValue(entity, field.getName() + dictField.suffix(), name);
            }
        }
    }

    /**
     * 构建 SQL 缓存 key
     */
    private static String generateSqlCacheKey(String sql, Map<String, Object> params) {
        return sql + ":" + params.entrySet().stream()
                .sorted(Map.Entry.comparingByKey())
                .map(e -> e.getKey() + "=" + e.getValue())
                .collect(Collectors.joining("&"));
    }

    /**
     * 处理普通字典映射
     */
    private static <T> void processCodeName(T entity, Field field, Object value,
                                            Map<String, CodeNameCommon> codeMap, DictField dictField) throws Exception {
        String code = value.toString();
        CodeNameCommon common = codeMap.get(code);
        if (common != null) {
            setFieldValue(entity, field.getName() + dictField.suffix(), common.getName());
        }
    }

    /**
     * 执行动态SQL
     */
    private static List<Map<String, Object>> executeDynamicSql(String sql, Map<String, Object> params) {
        try {
            return zsrDBHelper.queryForListWithNamedParams(sql, params);
        } catch (Exception e) {
            logger.error("执行动态SQL失败: {}", sql, e);
            return Collections.emptyList();
        }
    }

    /**
     * 格式化时间值
     */
    private static String formatDateValue(Object value, String pattern) {
        try {
            if (value instanceof LocalDateTime) {
                return ((LocalDateTime) value).format(DateTimeFormatter.ofPattern(pattern));
            } else if (value instanceof LocalDate) {
                return ((LocalDate) value).format(DateTimeFormatter.ofPattern(pattern));
            } else if (value instanceof Date) {
                return new java.text.SimpleDateFormat(pattern).format((Date) value);
            } else if (value instanceof String) {
                String str = (String) value;
                return str.length() >= 10 ? str.split("T| ")[0] : str;
            }
        } catch (Exception e) {
            logger.warn("时间格式化失败: {}", value, e);
        }
        return null;
    }

    /**
     * 设置字段值
     */
    private static <T> void setFieldValue(T entity, String fieldName, Object value) throws Exception {
        try {
            Field field = entity.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            field.set(entity, value);
        } catch (NoSuchFieldException e) {
            createAndSetField(entity, fieldName, value);
        }
    }

    /**
     * 动态创建字段并赋值
     */
    private static <T> void createAndSetField(T entity, String fieldName, Object value) throws Exception {
        if (entity instanceof ZsrBaseEntity) {
            ((ZsrBaseEntity) entity).push(fieldName, value);
            logger.debug("字段 {} 使用ZsrBaseEntity基类put值 {}", fieldName, value);
        } else {
            logger.warn("实体类 {} 不支持动态添加字段", entity.getClass().getName());
        }
    }

    /**
     * 解析JSON数组数据
     */
    private static Map<String, CodeNameCommon> parseDataFromJsonArray(String[] dataArray) {
        if (dataArray == null || dataArray.length == 0) {
            return Collections.emptyMap();
        }
        return Arrays.stream(dataArray)
                .map(json -> {
                    try {
                        ZsrJson zsrJson = ZsrJson.parse(json);
                        return new CodeNameCommon(
                                zsrJson.getString("code"),
                                zsrJson.getString("name")
                        );
                    } catch (Exception e) {
                        logger.warn("解析JSON字段失败: {}", json, e);
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(
                        CodeNameCommon::getCode,
                        common -> common,
                        (v1, v2) -> v1,
                        () -> new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
                ));
    }

    /**
     * 根据code获取name
     */
    public static String getNameByCodeStreamFindFirst(List<Map<String, Object>> dataList, String code) {
        return dataList.stream()
                .filter(map -> code.equals(map.get("CODE")))
                .findFirst()
                .map(map -> (String) map.get("NAME"))
                .orElse(null);
    }
}