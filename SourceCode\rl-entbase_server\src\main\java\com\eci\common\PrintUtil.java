package com.eci.common;

/**
 * @author: shuairongzeng
 * @date: 2019/9/26 15:03
 * @description:打印日志工具
 */
public class PrintUtil {
    /**
     * 根据给定的模板和参数自定义打印信息
     * 该方法接受一个字符串模板和一个可变参数数组，将模板中的占位符替换为实际参数值后打印出来
     *
     * @param template 消息模板，其中的占位符用{}表示，将被参数数组中的值替换
     * @param args     可变参数数组，用于替换模板中的占位符
     */
    public static void customPrint(String template, Object... args) {
        // 将模板中的占位符{}替换为格式化字符串的%s，以便后续使用printf方法进行格式化打印
        String message = template.replaceAll("\\{}", "%s");
        // 使用格式化字符串打印消息，%n用于在当前操作系统添加换行符
        System.out.printf(message + "%n", args);
    }


    /**
     * 自定义错误信息打印方法
     * 该方法使用红色字体在控制台输出格式化的错误信息，以提高错误的可见性和易于识别
     *
     * @param template 错误信息的模板，其中的占位符为{}
     * @param args     错误信息中的变量参数，将替换模板中的占位符
     */
    public static void customErrorPrint(String template, Object... args) {
        // 设置红色字体
        String message = template.replaceAll("\\{}", "%s");
        // 使用printf方法打印格式化的红色错误信息
        System.out.printf("\033[31m" + message + "\033[0m%n", args); // \033[31m 为红色，\033[0m 恢复默认颜色
    }
}
