package com.eci.common.cache.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.util.concurrent.TimeUnit;

/**
 * 缓存配置检测器
 * 根据配置自动检测并选择合适的缓存策略
 */
@Component
public class CacheConfigDetector {

    private static final Logger logger = LoggerFactory.getLogger(CacheConfigDetector.class);

    @Value("${spring.redis.host:}")
    private String redisHost;

    @Value("${spring.redis.port:6379}")
    private int redisPort;

    @Value("${spring.redis.database:0}")
    private int redisDatabase;

    @Value("${spring.redis.password:}")
    private String redisPassword;

    @Value("${spring.redis.timeout:5000}")
    private long redisTimeout;

    @Value("${cache.strategy:auto}")
    private String cacheStrategy;

    @Value("${cache.local.max-size:1000}")
    private long localCacheMaxSize;

    @Value("${cache.local.expire-minutes:60}")
    private long localCacheExpireMinutes;

    @Value("${cache.redis.connection-test-timeout:3000}")
    private long redisConnectionTestTimeout;

    private volatile CacheStrategy detectedStrategy = CacheStrategy.LOCAL;
    private volatile boolean redisAvailable = false;
    private volatile long lastRedisCheckTime = 0;
    private static final long REDIS_CHECK_INTERVAL = TimeUnit.MINUTES.toMillis(5); // 5分钟检查一次

    private RedisConnectionFactory redisConnectionFactory;

    public CacheConfigDetector(RedisConnectionFactory redisConnectionFactory) {
        this.redisConnectionFactory = redisConnectionFactory;
    }

    @PostConstruct
    public void init() {
        detectCacheStrategy();
        logger.info("缓存策略检测完成，当前策略: {}", detectedStrategy);
    }

    /**
     * 检测缓存策略
     */
    public void detectCacheStrategy() {
        switch (cacheStrategy.toLowerCase()) {
            case "redis":
                // 强制使用 Redis
                if (isRedisConfigured() && testRedisConnection()) {
                    detectedStrategy = CacheStrategy.REDIS;
                    redisAvailable = true;
                    logger.info("强制使用 Redis 缓存策略");
                } else {
                    logger.warn("强制 Redis 策略失败，降级到本地缓存");
                    detectedStrategy = CacheStrategy.LOCAL;
                    redisAvailable = false;
                }
                break;
            case "local":
                // 强制使用本地缓存
                detectedStrategy = CacheStrategy.LOCAL;
                redisAvailable = false;
                logger.info("强制使用本地缓存策略");
                break;
            case "auto":
            default:
                // 自动检测
                if (isRedisConfigured() && testRedisConnection()) {
                    detectedStrategy = CacheStrategy.REDIS;
                    redisAvailable = true;
                    logger.info("自动检测: 使用 Redis 缓存策略");
                } else {
                    detectedStrategy = CacheStrategy.LOCAL;
                    redisAvailable = false;
                    logger.info("自动检测: 使用本地缓存策略 (Redis 不可用)");
                }
                break;
        }
        lastRedisCheckTime = System.currentTimeMillis();
    }

    /**
     * 检查 Redis 配置是否完整
     */
    private boolean isRedisConfigured() {
        boolean configured = StringUtils.hasText(redisHost) && redisPort > 0;
        if (!configured) {
            logger.debug("Redis 配置不完整: host={}, port={}", redisHost, redisPort);
        }
        return configured;
    }

    /**
     * 测试 Redis 连接
     */
    private boolean testRedisConnection() {
        if (redisConnectionFactory == null) {
            logger.debug("RedisConnectionFactory 未配置");
            return false;
        }

        try {
            long startTime = System.currentTimeMillis();
            RedisConnection connection = redisConnectionFactory.getConnection();
            
            // 执行简单的 ping 命令测试连接
            String pong = connection.ping();
            connection.close();
            
            long duration = System.currentTimeMillis() - startTime;
            logger.debug("Redis 连接测试成功，响应时间: {}ms, 响应: {}", duration, pong);
            
            if (duration > redisConnectionTestTimeout) {
                logger.warn("Redis 连接响应时间过长: {}ms > {}ms", duration, redisConnectionTestTimeout);
                return false;
            }
            
            return true;
        } catch (Exception e) {
            logger.debug("Redis 连接测试失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 获取当前缓存策略
     */
    public CacheStrategy getCurrentStrategy() {
        // 定期重新检测 Redis 可用性
        if (detectedStrategy == CacheStrategy.LOCAL && 
            System.currentTimeMillis() - lastRedisCheckTime > REDIS_CHECK_INTERVAL) {
            logger.debug("定期重新检测 Redis 可用性");
            detectCacheStrategy();
        }
        return detectedStrategy;
    }

    /**
     * Redis 是否可用
     */
    public boolean isRedisAvailable() {
        return redisAvailable;
    }

    /**
     * 强制重新检测
     */
    public void forceRedetect() {
        logger.info("强制重新检测缓存策略");
        detectCacheStrategy();
    }

    /**
     * 获取配置信息
     */
    public CacheConfig getCacheConfig() {
        return CacheConfig.builder()
                .strategy(detectedStrategy)
                .redisHost(redisHost)
                .redisPort(redisPort)
                .redisDatabase(redisDatabase)
                .redisPassword(redisPassword)
                .redisTimeout(redisTimeout)
                .localCacheMaxSize(localCacheMaxSize)
                .localCacheExpireMinutes(localCacheExpireMinutes)
                .redisAvailable(redisAvailable)
                .build();
    }

    /**
     * 缓存策略枚举
     */
    public enum CacheStrategy {
        LOCAL("本地缓存"),
        REDIS("Redis缓存"),
        HYBRID("混合缓存");

        private final String description;

        CacheStrategy(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 缓存配置信息
     */
    public static class CacheConfig {
        private CacheStrategy strategy;
        private String redisHost;
        private int redisPort;
        private int redisDatabase;
        private String redisPassword;
        private long redisTimeout;
        private long localCacheMaxSize;
        private long localCacheExpireMinutes;
        private boolean redisAvailable;

        // Builder 模式
        public static Builder builder() {
            return new Builder();
        }

        public static class Builder {
            private CacheConfig config = new CacheConfig();

            public Builder strategy(CacheStrategy strategy) {
                config.strategy = strategy;
                return this;
            }

            public Builder redisHost(String redisHost) {
                config.redisHost = redisHost;
                return this;
            }

            public Builder redisPort(int redisPort) {
                config.redisPort = redisPort;
                return this;
            }

            public Builder redisDatabase(int redisDatabase) {
                config.redisDatabase = redisDatabase;
                return this;
            }

            public Builder redisPassword(String redisPassword) {
                config.redisPassword = redisPassword;
                return this;
            }

            public Builder redisTimeout(long redisTimeout) {
                config.redisTimeout = redisTimeout;
                return this;
            }

            public Builder localCacheMaxSize(long localCacheMaxSize) {
                config.localCacheMaxSize = localCacheMaxSize;
                return this;
            }

            public Builder localCacheExpireMinutes(long localCacheExpireMinutes) {
                config.localCacheExpireMinutes = localCacheExpireMinutes;
                return this;
            }

            public Builder redisAvailable(boolean redisAvailable) {
                config.redisAvailable = redisAvailable;
                return this;
            }

            public CacheConfig build() {
                return config;
            }
        }

        // Getters
        public CacheStrategy getStrategy() { return strategy; }
        public String getRedisHost() { return redisHost; }
        public int getRedisPort() { return redisPort; }
        public int getRedisDatabase() { return redisDatabase; }
        public String getRedisPassword() { return redisPassword; }
        public long getRedisTimeout() { return redisTimeout; }
        public long getLocalCacheMaxSize() { return localCacheMaxSize; }
        public long getLocalCacheExpireMinutes() { return localCacheExpireMinutes; }
        public boolean isRedisAvailable() { return redisAvailable; }

        @Override
        public String toString() {
            return String.format("CacheConfig{strategy=%s, redisHost='%s', redisPort=%d, redisAvailable=%s}", 
                    strategy, redisHost, redisPort, redisAvailable);
        }
    }
}
