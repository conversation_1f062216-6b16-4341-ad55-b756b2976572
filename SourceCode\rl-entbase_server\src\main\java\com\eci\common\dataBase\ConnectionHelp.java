package com.eci.common.dataBase;

import com.eci.common.enums.Enums;
import lombok.extern.log4j.Log4j2;

import java.sql.Connection;
import java.sql.DriverManager;
import java.util.Properties;

/**
 * <AUTHOR>
 * @description 数据源-数据库链接类
 * @created 2024-8-26 16:57:53
 */
@Log4j2
public final class ConnectionHelp {

    /**
     * @return java.sql.Connection
     * @description
     * <AUTHOR>
     * @params driver 数据库驱动, url 地址, username 用户名, password 密码
     * @created 2024/8/27 13:29
     */
    public static Connection getConnection(String driver, String url, String username, String password) {

        Connection connection = null;
        try {
            // 加 载驱动
            Class.forName(driver);
            // 通过驱动管理类获取数据库连接
            connection = DriverManager.getConnection(url, username, password);
            // 关闭连接
            connection.close();
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
            log.error(e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            log.error(e.getMessage());
        }
        return connection;

    }

    // 默认超时时间设置（单位：秒）
    private static final int DEFAULT_LOGIN_TIMEOUT = 2;
    private static final int DEFAULT_CONNECT_TIMEOUT = 2;

    public static Connection getConnection(int DBType, String connString) {

        Connection connection = null;
        try {
            ConnConfig dbConfig = ParseConnString(DBType, connString);
            connection = getConnectionWithTimeout(dbConfig.getDriverClassName(), dbConfig.getUrl(), dbConfig.getUserName(), dbConfig.getPassword());
        } catch (Exception e) {
            e.printStackTrace();
            log.error(e.getMessage());
        }
        return connection;
    }

    private static Connection getConnectionWithTimeout(
            String driverClassName,
            String url,
            String userName,
            String password) throws Exception {

        // 设置登录超时
        DriverManager.setLoginTimeout(DEFAULT_LOGIN_TIMEOUT);

        // 创建连接属性
        Properties props = new Properties();
        props.setProperty("user", userName);
        props.setProperty("password", password);

        // 设置连接超时
        props.setProperty("connectTimeout", String.valueOf(DEFAULT_CONNECT_TIMEOUT * 1000));

        // 对于不同类型数据库的特殊超时设置
        if (url.toLowerCase().contains("mysql")) {
            props.setProperty("socketTimeout", String.valueOf(DEFAULT_CONNECT_TIMEOUT * 1000));
        } else if (url.toLowerCase().contains("postgresql")) {
            props.setProperty("loginTimeout", String.valueOf(DEFAULT_CONNECT_TIMEOUT));
        } else if (url.toLowerCase().contains("oracle")) {
            props.setProperty("oracle.net.CONNECT_TIMEOUT", String.valueOf(DEFAULT_CONNECT_TIMEOUT * 1000));
        } else if (url.toLowerCase().contains("sqlserver")) {
            props.setProperty("loginTimeout", String.valueOf(DEFAULT_CONNECT_TIMEOUT));
            url += (url.contains("?") ? "&" : "?") + "connectTimeout=" + DEFAULT_CONNECT_TIMEOUT;
        }

        // 加载驱动
        Class.forName(driverClassName);

        // 尝试建立连接
        return DriverManager.getConnection(url, props);
    }

    public static boolean testConnection(int DBType, String connString) {
        Connection conn = null;
        try {
            conn = getConnection(DBType, connString);
            return conn != null && !conn.isClosed();
        } catch (Exception e) {
            log.error("Connection test failed: " + e.getMessage(), e);
            return false;
        } finally {
            if (conn != null) {
                try {
                    conn.close();
                } catch (Exception e) {
                    log.error("Error closing connection: " + e.getMessage(), e);
                }
            }
        }
    }

    /**
     * @return void
     * @description 解析链接字符串
     * <AUTHOR>
     * @params ConnString
     * @created 2024-8-26 17:00:42
     */
    public static ConnConfig ParseConnString(int DBType, String connString) {
        ConnConfig config = new ConnConfig();
        try {
            if (DBType == Enums.DatasourceDriver.OracleDriver.getCode()) {
                config.setDriverClassName(Enums.DatasourceDriver.OracleDriver.getName());
            } else if (DBType == Enums.DatasourceDriver.DmDriver.getCode()) {
                config.setDriverClassName(Enums.DatasourceDriver.DmDriver.getName());
            } else if (DBType == Enums.DatasourceDriver.MySqlDriver.getCode()) {
                config.setDriverClassName(Enums.DatasourceDriver.MySqlDriver.getName());
            }
            String str[] = connString.split(";");
            for (int i = 0; i < str.length; i++) {
                String valStr[] = str[i].split("=");
                switch (valStr[0]) {
                    case "url":
                        config.setUrl(str[i].replace("url=", ""));
                        break;
                    case "username":
                        config.setUserName(str[i].replace("username=", ""));
                        break;
                    case "password":
                        config.setPassword(str[i].replace("password=", ""));
                        break;
                }
            }
        } catch (Exception e) {
            log.warn(e.toString());
        }
        return config;
    }


}

