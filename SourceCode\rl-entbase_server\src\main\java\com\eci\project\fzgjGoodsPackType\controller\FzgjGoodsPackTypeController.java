package com.eci.project.fzgjGoodsPackType.controller;

import com.eci.common.ResponseMsgUtilX;
import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.fzgjGoodsPackType.service.FzgjGoodsPackTypeService;
import com.eci.project.fzgjGoodsPackType.entity.FzgjGoodsPackTypeEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* 包装类型Controller
*
* @<NAME_EMAIL>
* @date 2025-06-26
*/
@Api(tags = "包装类型")
@RestController
@RequestMapping("/fzgjGoodsPackType")
public class FzgjGoodsPackTypeController extends EciBaseController {

    @Autowired
    private FzgjGoodsPackTypeService fzgjGoodsPackTypeService;


    @ApiOperation("包装类型:保存")
    @EciLog(title = "包装类型:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody FzgjGoodsPackTypeEntity entity){
        FzgjGoodsPackTypeEntity fzgjGoodsPackTypeEntity =fzgjGoodsPackTypeService.save(entity);
        return ResponseMsgUtil.success(10001,fzgjGoodsPackTypeEntity);
    }


    @ApiOperation("包装类型:查询列表")
    @EciLog(title = "包装类型:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody FzgjGoodsPackTypeEntity entity){
        List<FzgjGoodsPackTypeEntity> fzgjGoodsPackTypeEntities = fzgjGoodsPackTypeService.selectList(entity);
        return ResponseMsgUtil.success(10001,fzgjGoodsPackTypeEntities);
    }


    @ApiOperation("包装类型:分页查询列表")
    @EciLog(title = "包装类型:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody FzgjGoodsPackTypeEntity entity){
        TgPageInfo tgPageInfo = fzgjGoodsPackTypeService.queryPageList(entity);
        return ResponseMsgUtilX.success(10001,tgPageInfo);
    }


    @ApiOperation("包装类型:根据ID查一条")
    @EciLog(title = "包装类型:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody FzgjGoodsPackTypeEntity entity){
        FzgjGoodsPackTypeEntity  fzgjGoodsPackTypeEntity = fzgjGoodsPackTypeService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001,fzgjGoodsPackTypeEntity);
    }


    @ApiOperation("包装类型:根据ID删除一条")
    @EciLog(title = "包装类型:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody FzgjGoodsPackTypeEntity entity){
        int count = fzgjGoodsPackTypeService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("包装类型:根据ID字符串删除多条")
    @EciLog(title = "包装类型:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody FzgjGoodsPackTypeEntity entity) {
        int count = fzgjGoodsPackTypeService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }


}