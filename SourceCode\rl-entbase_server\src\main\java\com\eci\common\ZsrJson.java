package com.eci.common;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.eci.exception.BaseException;

import java.util.ArrayList;
import java.util.List;

/**
 * 帅哥的Json工具类
 */
public class ZsrJson {
    private final JSONObject json;

    private ZsrJson(String jsonString) {
        this.json = JSON.parseObject(jsonString);
    }

    // Constructor to allow creating ZsrJson from an existing JSONObject
    private ZsrJson(JSONObject jsonObject) {
        this.json = jsonObject;
    }

    /**
     * 静态工厂方法
     * @param jsonString json字符串
     * @return
     */
    public static ZsrJson parse(String jsonString) {
        if (Zsr.String.IsNullOrWhiteSpace(jsonString)) {
            return null;
        }
        return new ZsrJson(jsonString);
    }


    /**
     * 获取指定key对应的JSONObject，并封装为新的ZsrJson对象
     * @param key 键
     * @return 封装了JSONObject的ZsrJson对象，如果key不存在或不是JSONObject则返回null
     */
    public ZsrJson getJSONObject(String key) {
        JSONObject nestedJson = json.getJSONObject(key);
        if (nestedJson == null) {
            return null;
        }
        return new ZsrJson(nestedJson);
    }

    /**
     * 获取任意类型对象
     * @param key 键
     * @param clazz 对象
     * @return
     * @param <T> 对象
     */
    public <T> T getObject(String key, Class<T> clazz) {
        return json.getObject(key, clazz);
    }
    /**
     * 直接将根节点JSON转换为指定对象
     * @param clazz 对象类型
     * @param <T> 对象类型
     * @return
     */
    public <T> T toObject(Class<T> clazz) {
        return json.toJavaObject(clazz);
    }

    /**
     * 获取任意类型的列表
     * @param key 键
     * @param elementType 元素类型
     * @return
     * @param <T>
     */
    public <T> List<T> getList(String key, Class<T> elementType) {
        JSONArray array = json.getJSONArray(key);
        if (array == null) {
            return new ArrayList<>();
        }
        return array.toJavaList(elementType);
    }

    /**
     * 获取int（兼容旧方法）
     * @param key 键
     * @return
     */
    public int getInt(String key) {
        return json.getIntValue(key);
    }
    /**
     * 获取布尔值（兼容旧方法）
     * @param key 键
     * @return
     */
    public boolean getBoolean(String key) {
        return json.getBooleanValue(key);
    }

    /**
     * 获取字符串（兼容旧方法）
     * @param key 键
     * @return
     */
    public String getString(String key) {
        return json.getString(key);
    }


    /**
     * 获取字符串（兼容旧方法）
     * @param key 键
     * @param defaultValue 默认值
     * @return
     */
    public String getStringOrDefault(String key,String defaultValue) {
        String keyValue = json.getString(key);
        if (Zsr.String.IsNullOrWhiteSpace(keyValue)) {
            return defaultValue;
        }
        return keyValue;
    }

    /**
     * 获取字符串列表（兼容旧方法）
     * @param key 键
     * @return
     */
    public List<String> getStringList(String key) {
        return getList(key, String.class);
    }

    /**
     * 判断键是否存在且非空（返回布尔值）
     * @param key 键名
     * @return true: 存在且非空 | false: 不存在/空值
     */
    public boolean exists(String key) {
        Object value = json.get(key);
        return value != null && !value.toString().trim().isEmpty();
    }

    /**
     * 链式校验键值（失败抛异常，使用默认错误信息）
     * @param key 键名
     * @return 当前对象（支持链式调用）
     */
    public ZsrJson check(String key) {
        return check(key, "必填项缺失：" + key);
    }

    /**
     * 链式校验键值（失败抛异常）
     * @param key 键名
     * @param errorMessage 错误提示
     * @return 当前对象（支持链式调用）
     */
    public ZsrJson check(String key, String errorMessage) {
        if (!exists(key)) {
            throw new BaseException(errorMessage);
        }
        return this;
    }


    /**
     * 获取当前ZsrJson对象所代表的紧凑（无格式化空格）JSON字符串
     * @return 紧凑的JSON字符串
     */
    public String toCompactString() {
        return json.toJSONString(); // fastjson 默认生成的字符串就是紧凑的
    }

//
//    // 验证并获取必填字段
//    public <T> T getRequired(String key, Class<T> clazz, String errorMessage) {
//        T value = getObject(key, clazz);
//        if (value == null) {
//            throw new BaseException(errorMessage);
//        }
//        return value;
//    }
//
//    // 验证并获取非空列表
//    public <T> List<T> getRequiredList(String key, Class<T> elementType, String errorMessage) {
//        List<T> list = getList(key, elementType);
//        if (list.isEmpty()) {
//            throw new BaseException(errorMessage);
//        }
//        return list;
//    }
}
