package com.eci.project.fzgjGoodsPackType.service;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eci.common.DictFieldUtils;
import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.fzgjGoodsPackType.dao.FzgjGoodsPackTypeDao;
import com.eci.project.fzgjGoodsPackType.entity.FzgjGoodsPackTypeEntity;
import com.eci.project.fzgjGoodsPackType.validate.FzgjGoodsPackTypeVal;

import com.eci.sso.role.entity.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.List;


/**
 * 包装类型Service业务层处理
 *
 * @<NAME_EMAIL>
 * @date 2025-06-26
 */
@Service
@Slf4j
public class FzgjGoodsPackTypeService implements EciBaseService<FzgjGoodsPackTypeEntity> {

    @Autowired
    private FzgjGoodsPackTypeDao fzgjGoodsPackTypeDao;

    @Autowired
    private FzgjGoodsPackTypeVal fzgjGoodsPackTypeVal;


    @Override
    public TgPageInfo queryPageList(FzgjGoodsPackTypeEntity entity) {
        entity.setGroupCode(UserContext.getUserInfo().getCompanyCode());
        EciQuery<FzgjGoodsPackTypeEntity> eciQuery = EciQuery.buildQuery(entity);
        List<FzgjGoodsPackTypeEntity> entities = fzgjGoodsPackTypeDao.queryPageList(eciQuery);
        DictFieldUtils.handleDictFields(entities); // 将code转为name
        return EciQuery.getPageInfo(entities);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public FzgjGoodsPackTypeEntity save(FzgjGoodsPackTypeEntity entity) {
        // 返回实体对象
        FzgjGoodsPackTypeEntity fzgjGoodsPackTypeEntity = null;
        fzgjGoodsPackTypeVal.saveValidate(entity, BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {
            entity.setGuid(IdWorker.get32UUID());
            entity.setCreateDate(new java.util.Date());
            entity.setUpdateDate(new java.util.Date());
            entity.setCreateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setCreateUserName(UserContext.getUserInfo().getTrueName());
            entity.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
            entity.setGroupCode(UserContext.getUserInfo().getCompanyCode());
            entity.setGroupName(UserContext.getUserInfo().getCompanyName());
            entity.setNodeCode(UserContext.getUserInfo().getDeptCode());
            entity.setNodeName(UserContext.getUserInfo().getDeptName());
            entity.setCompanyCode(UserContext.getUserInfo().getCompanyCode());
            entity.setCompanyName(UserContext.getUserInfo().getCompanyName());
            fzgjGoodsPackTypeEntity = fzgjGoodsPackTypeDao.insertOne(entity);

        } else {
            entity.setUpdateDate(new java.util.Date());
            entity.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
            fzgjGoodsPackTypeEntity = fzgjGoodsPackTypeDao.updateByEntityId(entity);

        }
        return fzgjGoodsPackTypeEntity;
    }

    @Override
    public List<FzgjGoodsPackTypeEntity> selectList(FzgjGoodsPackTypeEntity entity) {
        return fzgjGoodsPackTypeDao.selectList(entity);
    }

    @Override
    public FzgjGoodsPackTypeEntity selectOneById(Serializable id) {
        return fzgjGoodsPackTypeDao.selectById(id);
    }


    @Override
    public void insertBatch(List<FzgjGoodsPackTypeEntity> list) {
        fzgjGoodsPackTypeDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return fzgjGoodsPackTypeDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return fzgjGoodsPackTypeDao.deleteById(id);
    }

}