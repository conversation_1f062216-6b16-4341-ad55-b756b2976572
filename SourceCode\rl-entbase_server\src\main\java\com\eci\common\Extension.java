package com.eci.common;

import com.eci.common.enums.OrderEnum;
import com.eci.exception.BaseException;
import com.eci.project.omsOrderFwxmWork.entity.OrderFwxmWorkResponse;

/**
 * @ClassName: Extension
 * @Author: guangyan.mei
 * @Date: 2025/4/30 16:49
 * @Description: TODO
 */
public class Extension {

    /*
     * 根据协作任务信息获取服务项目CODE
     * **/
    public static String getFwxmCodeByWorkInfo(OrderFwxmWorkResponse work) {
        if (work == null) {
            return "";
        }

        String[] workSplitArr = work.getWorkNo().split("-");
        int length = workSplitArr.length;
        return (length > 2) ? workSplitArr[length - 2] : work.getFwxmCode();
    }


    public static String getFeedbackDocStatus(String type) {
        switch (type) {
            case "ZYWC": //作业完成
                return OrderEnum.DocStatus.OP_COMPLETE_OK.getCode();//作业完成01
            case "ZYSJQQ": //作业数据齐全
                return OrderEnum.DocStatus.DATA_OK.getCode();//作业数据齐全02
            default:
                throw new BaseException("反馈操作类型不正确！");
        }
    }
}
