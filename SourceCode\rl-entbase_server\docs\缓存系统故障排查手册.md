# 缓存系统故障排查手册

## 🚨 常见问题快速诊断

### 问题分类索引
- [缓存策略问题](#缓存策略问题)
- [Redis 连接问题](#redis-连接问题)
- [内存相关问题](#内存相关问题)
- [性能问题](#性能问题)
- [配置问题](#配置问题)
- [数据一致性问题](#数据一致性问题)

## 🔍 缓存策略问题

### 问题1：配置了 Redis 但仍使用本地缓存

**症状**：
- 日志显示 "使用本地缓存策略"
- 缓存类型为 LOCAL 而非 REDIS

**诊断步骤**：
```bash
# 1. 检查配置文件
grep -A 10 "redis:" application-dev.yml

# 2. 检查启动日志
grep "CacheConfigDetector" application.log
grep "Redis 连接测试" application.log
```

**可能原因**：
1. Redis 配置不完整（host 为空或 port 为 0）
2. Redis 服务未启动
3. 网络连接问题
4. 认证失败

**解决方案**：
```yaml
# 确保配置完整
spring:
  redis:
    host: ***************  # 不能为空
    port: 6379             # 必须大于 0
    database: 13
    password: Redis_1qaz   # 如果需要认证
```

**验证方法**：
```java
// 通过接口检查
@GetMapping("/cache/config")
public String getCacheConfig() {
    return configDetector.getCacheConfig().toString();
}
```

### 问题2：缓存策略频繁切换

**症状**：
- 日志中频繁出现策略切换信息
- 应用性能不稳定

**诊断步骤**：
```bash
# 检查策略切换日志
grep "缓存策略" application.log | tail -20
```

**可能原因**：
1. Redis 服务不稳定
2. 网络连接不稳定
3. 连接超时设置过短

**解决方案**：
```yaml
cache:
  redis:
    connection-test-timeout: 5000  # 增加超时时间
    fallback:
      max-errors: 5               # 增加容错次数
      error-window-seconds: 120   # 延长错误窗口
```

## 🔌 Redis 连接问题

### 问题3：Redis 连接超时

**症状**：
- 日志显示 "Redis 连接测试失败"
- 错误信息包含 "timeout" 或 "connection refused"

**诊断步骤**：
```bash
# 1. 测试网络连通性
telnet 192.168.************

# 2. 检查 Redis 服务状态
redis-cli -h *************** -p 6379 ping

# 3. 检查防火墙设置
netstat -an | grep 6379
```

**解决方案**：
1. **网络问题**：
   ```bash
   # 检查路由和防火墙
   ping ***************
   traceroute ***************
   ```

2. **Redis 配置问题**：
   ```bash
   # 检查 Redis 配置
   grep "bind" /etc/redis/redis.conf
   grep "protected-mode" /etc/redis/redis.conf
   ```

3. **应用配置调整**：
   ```yaml
   spring:
     redis:
       timeout: 10000  # 增加超时时间
       lettuce:
         pool:
           max-active: 16
           max-wait: 3000
   ```

### 问题4：Redis 认证失败

**症状**：
- 错误信息包含 "NOAUTH" 或 "invalid password"

**解决方案**：
```yaml
spring:
  redis:
    password: ${REDIS_PASSWORD:Redis_1qaz}  # 使用环境变量
```

## 💾 内存相关问题

### 问题5：内存使用持续增长

**症状**：
- 应用内存使用率持续上升
- 可能出现 OutOfMemoryError

**诊断步骤**：
```java
// 1. 检查内存使用情况
String memoryStats = ExpiringMapCacheFixed.MemoryMonitor.getMemoryStats();
logger.info("内存使用情况: {}", memoryStats);

// 2. 检查缓存大小
String cacheStats = cacheFactory.getCacheStats();
logger.info("缓存统计: {}", cacheStats);

// 3. 检查是否有内存压力
boolean hasMemoryPressure = ExpiringMapCacheFixed.MemoryMonitor.isMemoryPressure();
if (hasMemoryPressure) {
    logger.warn("检测到内存压力");
}
```

**可能原因**：
1. 缓存大小配置过大
2. 缓存过期时间过长
3. 存在内存泄露

**解决方案**：
```yaml
cache:
  local:
    max-size: 500          # 减少缓存大小
    expire-minutes: 30     # 缩短过期时间
```

**紧急处理**：
```java
// 立即清理缓存
dictFieldUtils.clearCache();

// 强制垃圾回收
System.gc();
```

### 问题6：缓存命中率过低

**症状**：
- 缓存统计显示命中率 < 50%
- 应用响应时间较长

**诊断步骤**：
```java
CacheAdapter.CacheStats stats = cache.getStats();
double hitRate = stats.getHitRate();
logger.info("缓存命中率: {:.2f}%", hitRate * 100);
```

**可能原因**：
1. 缓存过期时间过短
2. 缓存大小不足
3. 数据访问模式不适合缓存

**解决方案**：
```yaml
cache:
  local:
    max-size: 2000         # 增加缓存大小
    expire-minutes: 120    # 延长过期时间
```

## ⚡ 性能问题

### 问题7：缓存操作响应慢

**症状**：
- 缓存操作耗时 > 100ms
- 应用整体响应时间增加

**诊断步骤**：
```java
// 性能测试代码
long startTime = System.currentTimeMillis();
cache.get("test_key");
long duration = System.currentTimeMillis() - startTime;
logger.info("缓存操作耗时: {}ms", duration);
```

**可能原因**：
1. Redis 网络延迟高
2. 序列化/反序列化耗时
3. 缓存数据过大

**解决方案**：
1. **网络优化**：
   ```yaml
   spring:
     redis:
       lettuce:
         pool:
           max-active: 32  # 增加连接池大小
   ```

2. **数据优化**：
   - 减少缓存对象的大小
   - 优化序列化方式
   - 使用压缩

### 问题8：并发访问异常

**症状**：
- 出现 ConcurrentModificationException
- 数据不一致

**解决方案**：
- 确保使用修复版的 ExpiringMapCacheFixed
- 检查是否正确使用了线程安全的缓存实现

## ⚙️ 配置问题

### 问题9：配置不生效

**症状**：
- 修改配置后缓存行为未改变

**诊断步骤**：
```bash
# 1. 检查配置文件语法
yaml-lint application-dev.yml

# 2. 检查配置加载
grep "cache.strategy" application.log
```

**解决方案**：
1. **重启应用**：配置更改需要重启
2. **检查配置文件路径**：确保使用正确的配置文件
3. **验证配置格式**：YAML 格式要求严格

### 问题10：环境变量未生效

**症状**：
- 使用环境变量的配置项显示默认值

**解决方案**：
```bash
# 设置环境变量
export REDIS_PASSWORD=your_password
export CACHE_STRATEGY=redis

# 验证环境变量
echo $REDIS_PASSWORD
```

## 🔄 数据一致性问题

### 问题11：缓存数据过期

**症状**：
- 显示的数据不是最新的
- 字典数据更新后未反映

**解决方案**：
```java
// 手动刷新缓存
dictFieldUtils.refreshCacheNow("specific_cache_key");

// 或清空所有缓存
dictFieldUtils.refreshCacheNow();
```

### 问题12：分布式环境数据不一致

**症状**：
- 不同服务器节点显示不同数据

**解决方案**：
- 确保使用 Redis 缓存策略
- 检查 Redis 配置是否指向同一实例

## 🛠️ 调试工具

### 日志配置
```yaml
logging:
  level:
    com.eci.common.cache: DEBUG
    com.eci.common.DictFieldUtilsV3: DEBUG
```

### 监控接口
```java
@RestController
public class CacheDebugController {
    
    @GetMapping("/debug/cache/stats")
    public Map<String, Object> getCacheDebugInfo() {
        Map<String, Object> info = new HashMap<>();
        info.put("strategy", configDetector.getCurrentStrategy());
        info.put("redisAvailable", configDetector.isRedisAvailable());
        info.put("cacheStats", cacheFactory.getCacheStats());
        info.put("memoryStats", ExpiringMapCacheFixed.MemoryMonitor.getMemoryStats());
        return info;
    }
    
    @PostMapping("/debug/cache/test")
    public String testCache() {
        CacheAdapter<String, String> cache = cacheFactory.createStringCache("debug_test");
        
        // 测试基本操作
        cache.put("test_key", "test_value");
        String value = cache.get("test_key");
        
        return "测试结果: " + (value != null ? "成功" : "失败");
    }
}
```

### 性能分析
```java
// 性能监控代码
@Component
public class CachePerformanceMonitor {
    
    private final MeterRegistry meterRegistry;
    
    @EventListener
    public void onCacheOperation(CacheOperationEvent event) {
        Timer.Sample sample = Timer.start(meterRegistry);
        sample.stop(Timer.builder("cache.operation")
                .tag("type", event.getType())
                .tag("cache", event.getCacheName())
                .register(meterRegistry));
    }
}
```

## 📞 紧急联系

### 紧急处理步骤
1. **立即降级到本地缓存**：
   ```yaml
   cache:
     strategy: local
   ```

2. **清理所有缓存**：
   ```java
   dictFieldUtils.clearCache();
   ```

3. **重启应用**：
   ```bash
   systemctl restart your-application
   ```

### 日志收集
```bash
# 收集相关日志
grep -E "(cache|redis|memory)" application.log > cache_debug.log
grep -E "(ERROR|WARN)" application.log | grep -E "(cache|redis)" >> cache_debug.log
```

### 健康检查
```bash
# 检查应用健康状态
curl http://localhost:8080/actuator/health
curl http://localhost:8080/debug/cache/stats
```

## 📋 故障处理清单

- [ ] 检查配置文件语法和内容
- [ ] 验证 Redis 服务状态和网络连通性
- [ ] 查看应用启动日志和错误日志
- [ ] 检查内存使用情况和缓存统计
- [ ] 测试缓存基本功能
- [ ] 验证配置策略是否生效
- [ ] 检查并发访问是否正常
- [ ] 确认数据一致性
- [ ] 收集性能指标
- [ ] 必要时执行紧急降级
