package com.eci.common;

import com.eci.common.cache.adapter.CacheAdapter;
import com.eci.common.cache.factory.CacheFactory;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.lang.reflect.Field;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * 字典处理工具类 V3：使用缓存适配器架构
 * 支持本地缓存和 Redis 缓存的自动切换
 * 
 * 优化功能：
 * 1. 统一的缓存适配器接口
 * 2. 自动的缓存策略选择
 * 3. 完善的资源管理
 * 4. 内存泄露修复
 */
@Component
public class DictFieldUtilsV3 {

    private static final Logger logger = LoggerFactory.getLogger(DictFieldUtilsV3.class);

    @Autowired
    private CacheFactory cacheFactory;

    // 缓存适配器实例
    private CacheAdapter<Class<?>, Map<Field, DictField>> fieldCache;
    private CacheAdapter<String, Map<String, CodeNameCommon>> dynamicCache;
    private CacheAdapter<String, Map<String, CodeNameCommon>> staticCache;
    private CacheAdapter<String, SqlTemplate> sqlTemplateCache;
    private CacheAdapter<String, List<Map<String, Object>>> sqlResultCache;

    // 定时器：用于定时刷新缓存
    private ScheduledExecutorService cacheRefreshExecutor;
    
    // 记录需要定时刷新的查询
    private final Set<String> refreshKeys = ConcurrentHashMap.newKeySet();
    
    // 记录定时任务，支持取消
    private final Map<String, ScheduledFuture<?>> scheduledTasks = new ConcurrentHashMap<>();
    
    // 记录最后刷新时间
    private final Map<String, Long> lastRefreshTime = new ConcurrentHashMap<>();

    // 数据库操作工具
    private final ZsrDBHelper zsrDBHelper = new ZsrDBHelper();

    @PostConstruct
    @SuppressWarnings({"unchecked", "rawtypes"})
    public void init() {
        logger.debug("开始初始化DictFieldUtilsV3");

        // 初始化缓存适配器
        logger.debug("创建字段缓存适配器");
        fieldCache = (CacheAdapter) cacheFactory.createCache("dict_field", Class.class, Map.class);
        logger.debug("字段缓存适配器类型: {}", fieldCache.getType());

        logger.debug("创建动态缓存适配器");
        dynamicCache = (CacheAdapter) cacheFactory.createCache("dict_dynamic", String.class, Map.class,
                100, TimeUnit.MINUTES, 100);
        logger.debug("动态缓存适配器类型: {}", dynamicCache.getType());

        logger.debug("创建静态缓存适配器");
        staticCache = (CacheAdapter) cacheFactory.createCache("dict_static", String.class, Map.class);
        logger.debug("静态缓存适配器类型: {}", staticCache.getType());

        logger.debug("创建SQL模板缓存适配器");
        sqlTemplateCache = cacheFactory.createCache("sql_template", String.class, SqlTemplate.class);
        logger.debug("SQL模板缓存适配器类型: {}", sqlTemplateCache.getType());

        logger.debug("创建SQL结果缓存适配器");
        sqlResultCache = (CacheAdapter) cacheFactory.createCache("sql_result", String.class, List.class,
                100, TimeUnit.MINUTES, 100);
        logger.debug("SQL结果缓存适配器类型: {}", sqlResultCache.getType());

        // 初始化定时器
        cacheRefreshExecutor = Executors.newScheduledThreadPool(2, r -> {
            Thread t = new Thread(r, "DictFieldUtils-CacheRefresh");
            t.setDaemon(true);
            return t;
        });

        // 设置关闭钩子
        Runtime.getRuntime().addShutdownHook(new Thread(this::shutdown));

        logger.info("DictFieldUtilsV3 初始化完成，缓存策略: {}",
                cacheFactory.getConfigDetector().getCurrentStrategy());
    }

    @PreDestroy
    public void shutdown() {
        try {
            // 取消所有定时任务
            for (ScheduledFuture<?> task : scheduledTasks.values()) {
                task.cancel(false);
            }
            scheduledTasks.clear();

            // 关闭定时器
            if (cacheRefreshExecutor != null) {
                cacheRefreshExecutor.shutdown();
                if (!cacheRefreshExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                    cacheRefreshExecutor.shutdownNow();
                }
            }

            logger.info("DictFieldUtilsV3 已关闭");
        } catch (InterruptedException e) {
            if (cacheRefreshExecutor != null) {
                cacheRefreshExecutor.shutdownNow();
            }
            Thread.currentThread().interrupt();
        }
    }

    /**
     * 处理实体列表的字典字段
     */
    public <T> void handleDictFields(List<T> entities) {
        if (CollectionUtils.isEmpty(entities)) {
            return;
        }

        Class<?> entityClass = entities.get(0).getClass();
        Map<Field, DictField> annotatedFields = getAnnotatedFields(entityClass);
        if (annotatedFields.isEmpty()) {
            return;
        }

        // 查询字典数据
        Map<String, Map<String, CodeNameCommon>> dictDataMap = fetchDictData(annotatedFields.values());

        // 使用并行流处理实体
        entities.parallelStream().forEach(entity -> {
            if (entity != null) {
                annotatedFields.forEach((field, dictField) -> {
                    try {
                        processField(entity, field, dictField, dictDataMap);
                    } catch (Exception e) {
                        logger.error("处理字典字段异常：{}.{}", entityClass.getSimpleName(), field.getName(), e);
                    }
                });
            }
        });
    }

    /**
     * 处理单个实体的字典字段
     */
    public <T> void handleDictFields(T entity) {
        if (entity == null) {
            return;
        }
        handleDictFields(Collections.singletonList(entity));
    }

    /**
     * 获取带有 DictField 注解的字段
     */
    private Map<Field, DictField> getAnnotatedFields(Class<?> entityClass) {
        return fieldCache.get(entityClass, clazz -> {
            Map<Field, DictField> annotatedFields = new HashMap<>();
            Field[] fields = clazz.getDeclaredFields();
            
            for (Field field : fields) {
                DictField dictField = field.getAnnotation(DictField.class);
                if (dictField != null) {
                    annotatedFields.put(field, dictField);
                }
            }
            
            logger.debug("缓存实体类字段映射：{} -> {} 个字段", clazz.getSimpleName(), annotatedFields.size());
            return annotatedFields;
        });
    }

    /**
     * 批量查询字典数据
     */
    private Map<String, Map<String, CodeNameCommon>> fetchDictData(Collection<DictField> fields) {
        Map<String, Map<String, CodeNameCommon>> result = new HashMap<>();

        for (DictField dictField : fields) {
            String cacheKey = generateCacheKey(dictField);
            Map<String, CodeNameCommon> codeMap = null;

            try {
                if (StringUtils.isNotBlank(dictField.queryKey())) {
                    // queryKey 使用动态缓存，并注册定时刷新
                    codeMap = dynamicCache.get(cacheKey, key -> {
                        Map<String, CodeNameCommon> data = DataDictUtils.queryCodeNameMap(dictField.queryKey());
                        // 验证返回的数据类型
                        data = validateAndConvertCodeMap(data);
                        registerQueryKeyRefresh(key, dictField);
                        return data;
                    });
                } else if (dictField.data().length > 0) {
                    // data 定义使用静态缓存，永不过期
                    codeMap = staticCache.get(cacheKey, key -> parseDataFromJsonArray(dictField.data()));
                } else if (StringUtils.isNotBlank(dictField.sql())) {
                    // SQL 查询使用动态缓存，并注册定时刷新
                    codeMap = dynamicCache.get(cacheKey, key -> {
                        SqlTemplate sqlTemplate = sqlTemplateCache.get(dictField.sql(), 
                                sql -> new SqlTemplate(sql));
                        Map<String, CodeNameCommon> data = new TreeMap<>(String.CASE_INSENSITIVE_ORDER);
                        data.put("SQL_TEMPLATE", new CodeNameCommon("SQL_TEMPLATE", "SQL_TEMPLATE", sqlTemplate));
                        registerSqlRefresh(key, dictField);
                        return data;
                    });
                }

                if (codeMap == null) {
                    codeMap = Collections.emptyMap();
                }
                result.put(cacheKey, codeMap);
            } catch (Exception e) {
                logger.warn("获取字典数据失败：{}", cacheKey, e);
                result.put(cacheKey, Collections.emptyMap());
            }
        }

        return result;
    }
    
    /**
     * 验证并转换代码映射数据
     */
    private Map<String, CodeNameCommon> validateAndConvertCodeMap(Map<String, CodeNameCommon> originalMap) {
        if (originalMap == null) {
            return Collections.emptyMap();
        }
        
        Map<String, CodeNameCommon> validatedMap = new TreeMap<>(String.CASE_INSENSITIVE_ORDER);
        
        for (Map.Entry<String, CodeNameCommon> entry : originalMap.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            
            if (value instanceof CodeNameCommon) {
                validatedMap.put(key, (CodeNameCommon) value);
            } else if (value instanceof com.alibaba.fastjson.JSONObject) {
                // 将JSONObject转换为CodeNameCommon
                com.alibaba.fastjson.JSONObject jsonObj = (com.alibaba.fastjson.JSONObject) value;
                String code = jsonObj.getString("code");
                String name = jsonObj.getString("name");
                if (code != null && name != null) {
                    validatedMap.put(key, new CodeNameCommon(code, name));
                    logger.debug("将JSONObject转换为CodeNameCommon：{} -> {}", code, name);
                }
            } else if (value != null) {
                // 尝试将其他类型转换为CodeNameCommon
                logger.warn("发现非CodeNameCommon类型的数据：{} -> {}", key, value.getClass().getName());
                String strValue = value.toString();
                validatedMap.put(key, new CodeNameCommon(key, strValue));
            }
        }
        
        return validatedMap;
    }

    /**
     * 注册 queryKey 定时刷新
     */
    private void registerQueryKeyRefresh(String cacheKey, DictField dictField) {
        registerDynamicCacheRefresh(cacheKey, dictField, RefreshType.QUERY_KEY);
    }

    /**
     * 注册 SQL 定时刷新
     */
    private void registerSqlRefresh(String cacheKey, DictField dictField) {
        registerDynamicCacheRefresh(cacheKey, dictField, RefreshType.SQL);
    }

    /**
     * 统一的动态缓存刷新注册方法
     */
    private void registerDynamicCacheRefresh(String cacheKey, DictField dictField, RefreshType refreshType) {
        if (refreshKeys.add(cacheKey)) {
            // 只有第一次注册时才启动定时任务
            ScheduledFuture<?> task = cacheRefreshExecutor.scheduleAtFixedRate(() -> {
                try {
                    switch (refreshType) {
                        case QUERY_KEY:
                            refreshQueryKeyCache(cacheKey, dictField);
                            break;
                        case SQL:
                            refreshSqlCache(cacheKey, dictField);
                            break;
                    }
                } catch (Exception e) {
                    logger.error("定时刷新 {} 缓存失败：{}", refreshType, cacheKey, e);
                }
            }, 1, 1, TimeUnit.MINUTES); // 1 分钟后开始，每 1 分钟执行一次

            scheduledTasks.put(cacheKey, task);
            logger.info("已注册 {} 定时刷新：{}", refreshType, cacheKey);
        }
    }

    /**
     * 刷新 queryKey 缓存
     */
    private void refreshQueryKeyCache(String cacheKey, DictField dictField) {
        try {
            // 重新查询 queryKey 对应的字典数据
            Map<String, CodeNameCommon> newCodeMap = DataDictUtils.queryCodeNameMap(dictField.queryKey());
            dynamicCache.put(cacheKey, newCodeMap);
            lastRefreshTime.put(cacheKey, System.currentTimeMillis());
            logger.debug("已刷新 queryKey 缓存：{}", cacheKey);
        } catch (Exception e) {
            logger.warn("刷新 queryKey 缓存失败：{}", cacheKey, e);
        }
    }

    /**
     * 刷新 SQL 缓存
     */
    private void refreshSqlCache(String cacheKey, DictField dictField) {
        try {
            // 清理 SQL 结果缓存，强制下次查询时重新执行 SQL
            sqlResultCache.clear();
            
            // 保持 SQL 模板缓存和字典缓存不变
            SqlTemplate sqlTemplate = sqlTemplateCache.get(dictField.sql(), sql -> new SqlTemplate(sql));
            Map<String, CodeNameCommon> newCodeMap = new TreeMap<>(String.CASE_INSENSITIVE_ORDER);
            newCodeMap.put("SQL_TEMPLATE", new CodeNameCommon("SQL_TEMPLATE", "SQL_TEMPLATE", sqlTemplate));
            dynamicCache.put(cacheKey, newCodeMap);
            
            lastRefreshTime.put(cacheKey, System.currentTimeMillis());
            logger.info("已刷新 SQL 缓存，清理所有SQL结果缓存：{}", cacheKey);
        } catch (Exception e) {
            logger.warn("刷新 SQL 缓存失败：{}", cacheKey, e);
        }
    }

    /**
     * 生成缓存 key
     */
    private String generateCacheKey(DictField dictField) {
        if (StringUtils.isNotBlank(dictField.queryKey())) {
            return "QK_" + dictField.queryKey();
        } else if (dictField.data().length > 0) {
            return "DS_" + Arrays.hashCode(dictField.data());
        } else if (StringUtils.isNotBlank(dictField.sql())) {
            return "SQL_" + dictField.sql().hashCode();
        }
        return "EMPTY";
    }

    /**
     * 解析 JSON 数组数据
     */
    private Map<String, CodeNameCommon> parseDataFromJsonArray(String[] dataArray) {
        if (dataArray == null || dataArray.length == 0) {
            return Collections.emptyMap();
        }

        return Arrays.stream(dataArray)
                .map(json -> {
                    try {
                        ZsrJson zsrJson = ZsrJson.parse(json);
                        return new CodeNameCommon(
                                zsrJson.getString("code"),
                                zsrJson.getString("name")
                        );
                    } catch (Exception e) {
                        logger.warn("解析 JSON 字段失败：{}", json, e);
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(
                        CodeNameCommon::getCode,
                        common -> common,
                        (v1, v2) -> v1,
                        () -> new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
                ));
    }

    /**
     * 处理单个字段
     */
    private <T> void processField(T entity, Field field, DictField dictField,
                                 Map<String, Map<String, CodeNameCommon>> dictDataMap) throws Exception {
        field.setAccessible(true);
        Object value = field.get(entity);
        if (value == null) {
            return;
        }

        if (dictField.useDateFormat()) {
            String formattedDate = formatDateValue(value, dictField.dateFormat());
            if (formattedDate != null) {
                setFieldValue(entity, field.getName() + "Display", formattedDate);
            }
        } else {
            String cacheKey = generateCacheKey(dictField);
            Map<String, CodeNameCommon> codeMap = dictDataMap.get(cacheKey);

            if (codeMap != null) {
                Object templateEntry = codeMap.get("SQL_TEMPLATE");
                
                // 类型安全检查
                if (templateEntry instanceof CodeNameCommon) {
                    CodeNameCommon common = (CodeNameCommon) templateEntry;
                    Object sqlTemplateObj = common.getSqlTemplate();

                    // 检查SqlTemplate对象的实际类型（可能因反序列化而变为JSONObject）
                    if (sqlTemplateObj != null) {
                        SqlTemplate sqlTemplate = null;

                        if (sqlTemplateObj instanceof SqlTemplate) {
                            // 正常的SqlTemplate对象
                            sqlTemplate = (SqlTemplate) sqlTemplateObj;
                        } else if (sqlTemplateObj instanceof com.alibaba.fastjson.JSONObject) {
                            // 反序列化导致的JSONObject，尝试转换
                            logger.debug("检测到SqlTemplate被反序列化为JSONObject，尝试转换");
                            sqlTemplate = convertJsonObjectToSqlTemplate((com.alibaba.fastjson.JSONObject) sqlTemplateObj);
                        } else {
                            logger.warn("SqlTemplate对象类型异常: {}", sqlTemplateObj.getClass().getName());
                        }

                        if (sqlTemplate != null) {
                            processSqlTemplate(entity, field, value, sqlTemplate, dictField);
                        } else {
                            logger.warn("SqlTemplate转换失败，使用普通字典处理");
                            processCodeName(entity, field, value, codeMap, dictField);
                        }
                    } else {
                        processCodeName(entity, field, value, codeMap, dictField);
                    }
                } else if (templateEntry instanceof com.alibaba.fastjson.JSONObject) {
                    // 处理JSONObject类型的SQL_TEMPLATE
                    logger.debug("SQL_TEMPLATE是JSONObject类型，尝试转换为SqlTemplate：{}", templateEntry);
                    SqlTemplate sqlTemplate = convertJsonObjectToSqlTemplate((com.alibaba.fastjson.JSONObject) templateEntry);
                    if (sqlTemplate != null) {
                        processSqlTemplate(entity, field, value, sqlTemplate, dictField);
                    } else {
                        logger.warn("JSONObject转换为SqlTemplate失败，使用普通字典处理");
                        processCodeName(entity, field, value, codeMap, dictField);
                    }
                } else {
                    // 没有SQL_TEMPLATE或类型不正确，使用普通字典处理
                    processCodeName(entity, field, value, codeMap, dictField);
                }
            }
        }
    }

    /**
     * 格式化日期值
     */
    private String formatDateValue(Object value, String dateFormat) {
        try {
            if (value instanceof LocalDateTime) {
                return ((LocalDateTime) value).format(DateTimeFormatter.ofPattern(dateFormat));
            } else if (value instanceof LocalDate) {
                return ((LocalDate) value).format(DateTimeFormatter.ofPattern(dateFormat));
            } else if (value instanceof Date) {
                return new java.text.SimpleDateFormat(dateFormat).format((Date) value);
            } else if (value instanceof String) {
                String str = (String) value;
                return str.length() >= 10 ? str.split("T| ")[0] : str;
            }
        } catch (Exception e) {
            logger.warn("时间格式化失败：{}", value, e);
        }
        return null;
    }

    /**
     * 设置字段值
     */
    private <T> void setFieldValue(T entity, String fieldName, Object value) {
        try {
            Field field = entity.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            field.set(entity, value);
        } catch (NoSuchFieldException e) {
            createAndSetField(entity, fieldName, value);
        } catch (Exception e) {
            logger.warn("设置字段值失败：{}.{}", entity.getClass().getSimpleName(), fieldName, e);
        }
    }

    /**
     * 动态创建字段并赋值
     */
    private <T> void createAndSetField(T entity, String fieldName, Object value) {
        try {
            if (entity instanceof ZsrBaseEntity) {
                ((ZsrBaseEntity) entity).push(fieldName, value);
                logger.debug("字段 {} 使用 ZsrBaseEntity 基类 put 值 {}", fieldName, value);
            } else {
                logger.warn("实体类 {} 不支持动态添加字段", entity.getClass().getName());
            }
        } catch (Exception e) {
            logger.warn("动态创建字段失败：{}.{}", entity.getClass().getSimpleName(), fieldName, e);
        }
    }

    /**
     * 在类层次结构中查找字段（包括父类）
     */
    private Field findFieldInClassHierarchy(Class<?> clazz, String fieldName) {
        Class<?> currentClass = clazz;
        while (currentClass != null && !currentClass.equals(Object.class)) {
            try {
                return currentClass.getDeclaredField(fieldName);
            } catch (NoSuchFieldException e) {
                currentClass = currentClass.getSuperclass();
            }
        }
        return null;
    }

    /**
     * 生成 SQL 结果缓存键
     */
    private String generateSqlResultCacheKey(String sql, String paramsKey) {
        // 使用更安全的哈希计算方式
        int sqlHash = sql != null ? sql.hashCode() : 0;
        int paramsHash = paramsKey != null ? paramsKey.hashCode() : 0;

        // 组合哈希值，避免哈希冲突
        long combinedHash = ((long) sqlHash << 32) | (paramsHash & 0xFFFFFFFFL);

        return "SQL_RESULT_" + Long.toHexString(combinedHash);
    }

    /**
     * 执行动态 SQL
     */
    private List<Map<String, Object>> executeDynamicSql(String sql, Map<String, Object> params) {
        try {
            return zsrDBHelper.queryForListWithNamedParams(sql, params);
        } catch (Exception e) {
            logger.error("执行动态 SQL 失败：{}", sql, e);
            return Collections.emptyList();
        }
    }

    /**
     * 根据 code 获取 name
     */
    private String getNameByCodeStreamFindFirst(List<Map<String, Object>> dataList, String code) {
        return dataList.stream()
                .filter(map -> {
                    Object value = Zsr.MapIgnoreCase.getIgnoreCase(map, "CODE");
                    return value != null && code.equals(value.toString());
                })
                .findFirst()
                .map(map -> (String) Zsr.MapIgnoreCase.getIgnoreCase(map, "NAME"))
                .orElse(null);
    }

    /**
     * 处理 SQL 模板
     */
    private <T> void processSqlTemplate(T entity, Field field, Object value,
                                       SqlTemplate sqlTemplate, DictField dictField) {
        try {
            Map<String, Object> params = new HashMap<>();

            // 优先使用注解中配置的 params，如果没有配置则使用 SQL 模板中解析出的参数
            String[] configuredParams = dictField.params();
            List<String> paramNames;

            if (configuredParams.length > 0) {
                // 使用注解中配置的参数
                paramNames = Arrays.asList(configuredParams);
                logger.debug("使用注解配置的参数：{}", Arrays.toString(configuredParams));
            } else {
                // 使用 SQL 模板中解析出的参数
                paramNames = sqlTemplate.getParamNames();
                logger.debug("使用 SQL 模板解析的参数：{}", paramNames);
            }

            // 从实体中获取参数值
            for (String paramName : paramNames) {
                Field paramField = findFieldInClassHierarchy(entity.getClass(), paramName);
                if (paramField != null) {
                    paramField.setAccessible(true);
                    Object paramValue = paramField.get(entity);
                    params.put(paramName, paramValue);
                    logger.debug("参数 {} = {}", paramName, paramValue);
                } else {
                    logger.warn("未找到参数字段：{} 在实体类 {}", paramName, entity.getClass().getSimpleName());
                }
            }

            // 构建缓存 key：SQL + 参数排序后的 hash
            String sql = sqlTemplate.getRenderedSql();
            String sortedParamsKey = params.entrySet().stream()
                    .sorted(Map.Entry.comparingByKey())
                    .map(e -> e.getKey() + "=" + (e.getValue() != null ? e.getValue().toString() : "null"))
                    .collect(Collectors.joining("&"));

            // 使用更精确的缓存键生成策略
            String cacheKey = generateSqlResultCacheKey(sql, sortedParamsKey);

            List<Map<String, Object>> rows = sqlResultCache.get(cacheKey, k -> executeDynamicSql(sql, params));

            if (!rows.isEmpty()) {
                String name = getNameByCodeStreamFindFirst(rows, value.toString());
                if (name != null) {
                    setFieldValue(entity, field.getName() + dictField.suffix(), name);
                }
            } else {
                logger.debug("SQL 查询无结果：{}", sql);
            }
        } catch (Exception e) {
            logger.error("处理 SQL 模板失败：{}.{}", entity.getClass().getSimpleName(), field.getName(), e);
        }
    }

    /**
     * 处理代码名称映射
     */
    private <T> void processCodeName(T entity, Field field, Object value,
                                    Map<String, CodeNameCommon> codeMap, DictField dictField) {
        try {
            String code = value.toString();
            Object cachedValue = codeMap.get(code);
            
            // 类型安全检查
            if (cachedValue instanceof CodeNameCommon) {
                CodeNameCommon common = (CodeNameCommon) cachedValue;
                setFieldValue(entity, field.getName() + dictField.suffix(), common.getName());
            } else if (cachedValue instanceof com.alibaba.fastjson.JSONObject) {
                // 处理JSONObject类型的数据
                com.alibaba.fastjson.JSONObject jsonObj = (com.alibaba.fastjson.JSONObject) cachedValue;
                String name = jsonObj.getString("name");
                if (name != null) {
                    setFieldValue(entity, field.getName() + dictField.suffix(), name);
                } else {
                    logger.warn("JSONObject中未找到name字段：{}", jsonObj);
                }
            } else if (cachedValue != null) {
                // 处理其他类型的数据
                logger.warn("缓存中的数据类型不正确，期望CodeNameCommon，实际：{}", cachedValue.getClass().getName());
                // 尝试转换为字符串
                String name = cachedValue.toString();
                setFieldValue(entity, field.getName() + dictField.suffix(), name);
            } else {
                logger.debug("未找到代码对应的名称：{}", code);
            }
        } catch (Exception e) {
            logger.warn("处理代码名称映射失败：{}.{}", entity.getClass().getSimpleName(), field.getName(), e);
        }
    }

    /**
     * 将JSONObject转换为SqlTemplate
     */
    private SqlTemplate convertJsonObjectToSqlTemplate(com.alibaba.fastjson.JSONObject jsonObj) {
        try {
            String originalSql = jsonObj.getString("originalSql");
            String renderedSql = jsonObj.getString("renderedSql");
            com.alibaba.fastjson.JSONArray paramNamesArray = jsonObj.getJSONArray("paramNames");

            if (originalSql != null) {
                // 如果有原始SQL，直接用它创建SqlTemplate（会重新解析）
                return new SqlTemplate(originalSql);
            } else if (renderedSql != null && paramNamesArray != null) {
                // 如果没有原始SQL但有渲染后的SQL和参数列表，尝试重构
                List<String> paramNames = new ArrayList<>();
                for (int i = 0; i < paramNamesArray.size(); i++) {
                    paramNames.add(paramNamesArray.getString(i));
                }
                // 注意：这里我们无法完美重构原始SQL，但可以创建一个功能等价的SqlTemplate
                // 为了简化，我们假设renderedSql就是我们需要的
                return new SqlTemplate(renderedSql, renderedSql, paramNames);
            }
        } catch (Exception e) {
            logger.warn("转换JSONObject为SqlTemplate失败", e);
        }
        return null;
    }

    /**
     * 清理缓存的公共方法
     */
    public void clearCache() {
        dynamicCache.clear();
        staticCache.clear();
        sqlResultCache.clear();
        sqlTemplateCache.clear();
        lastRefreshTime.clear();
        logger.info("已清理所有缓存");
    }
    
    /**
     * 验证并清理无效缓存
     */
    public void validateAndCleanCache() {
        try {
            // 验证动态缓存
            Map<String, Object> invalidDynamicEntries = new HashMap<>();
            Set<String> dynamicKeys = dynamicCache.keySet();
            
            for (String key : dynamicKeys) {
                Object value = dynamicCache.get(key);
                if (value != null && !(value instanceof Map)) {
                    invalidDynamicEntries.put(key, value);
                } else if (value instanceof Map) {
                    Map<?, ?> map = (Map<?, ?>) value;
                    for (Object mapValue : map.values()) {
                        if (mapValue != null && !(mapValue instanceof CodeNameCommon)) {
                            invalidDynamicEntries.put(key, value);
                            break;
                        }
                    }
                }
            }
            
            // 清理无效的动态缓存条目
            invalidDynamicEntries.keySet().forEach(key -> {
                dynamicCache.remove(key);
                logger.warn("清理无效的动态缓存条目：{}", key);
            });
            
            // 验证静态缓存
            Map<String, Object> invalidStaticEntries = new HashMap<>();
            Set<String> staticKeys = staticCache.keySet();
            
            for (String key : staticKeys) {
                Object value = staticCache.get(key);
                if (value != null && !(value instanceof Map)) {
                    invalidStaticEntries.put(key, value);
                } else if (value instanceof Map) {
                    Map<?, ?> map = (Map<?, ?>) value;
                    for (Object mapValue : map.values()) {
                        if (mapValue != null && !(mapValue instanceof CodeNameCommon)) {
                            invalidStaticEntries.put(key, value);
                            break;
                        }
                    }
                }
            }
            
            // 清理无效的静态缓存条目
            invalidStaticEntries.keySet().forEach(key -> {
                staticCache.remove(key);
                logger.warn("清理无效的静态缓存条目：{}", key);
            });
            
            logger.info("缓存验证完成，清理了 {} 个动态缓存条目，{} 个静态缓存条目", 
                       invalidDynamicEntries.size(), invalidStaticEntries.size());
        } catch (Exception e) {
            logger.error("验证缓存时发生错误", e);
        }
    }

    /**
     * 检查 SQL 缓存是否需要刷新（用于调试）
     */
    public boolean isSqlCacheRefreshed(String cacheKey) {
        Long lastRefreshTime = this.lastRefreshTime.get(cacheKey);
        return lastRefreshTime != null && (System.currentTimeMillis() - lastRefreshTime) < 70000; // 70 秒内算作已刷新
    }

    /**
     * 获取缓存统计信息
     */
    public String getCacheStats() {
        StringBuilder stats = new StringBuilder();
        stats.append("DictFieldUtilsV3 缓存统计:\n");
        stats.append("- 字段缓存: ").append(fieldCache.getStats()).append("\n");
        stats.append("- 动态缓存: ").append(dynamicCache.getStats()).append("\n");
        stats.append("- 静态缓存: ").append(staticCache.getStats()).append("\n");
        stats.append("- SQL模板缓存: ").append(sqlTemplateCache.getStats()).append("\n");
        stats.append("- SQL结果缓存: ").append(sqlResultCache.getStats()).append("\n");
        stats.append("- 定时任务数: ").append(scheduledTasks.size()).append("\n");
        return stats.toString();
    }

    /**
     * 立即刷新指定的缓存
     */
    public void refreshCacheNow(String cacheKey) {
        if (cacheKey.startsWith("QK_") || cacheKey.startsWith("SQL_")) {
            dynamicCache.remove(cacheKey);
        } else if (cacheKey.startsWith("DS_")) {
            staticCache.remove(cacheKey);
        }

        // 更新 SQL 刷新时间
        if (cacheKey.startsWith("SQL_")) {
            lastRefreshTime.put(cacheKey, System.currentTimeMillis());
        }

        logger.info("已立即刷新缓存：{}", cacheKey);
    }

    /**
     * 立即刷新所有缓存
     */
    public void refreshCacheNow() {
        clearCache();
        logger.info("已立即刷新所有缓存");
    }

    /**
     * 取消指定缓存的定时刷新
     */
    public void cancelRefreshTask(String cacheKey) {
        ScheduledFuture<?> task = scheduledTasks.remove(cacheKey);
        if (task != null) {
            task.cancel(false);
            refreshKeys.remove(cacheKey);
            logger.info("已取消定时刷新任务：{}", cacheKey);
        }
    }

    /**
     * 刷新类型枚举
     */
    private enum RefreshType {
        QUERY_KEY, SQL
    }
}
