package com.eci.common.cache.adapter;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.stats.CacheStats;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;

/**
 * 本地缓存适配器实现
 * 基于 Caffeine 缓存，支持动态和静态缓存分离策略
 */
public class LocalCacheAdapter<K, V> implements CacheAdapter<K, V> {

    private static final Logger logger = LoggerFactory.getLogger(LocalCacheAdapter.class);

    private final Cache<K, V> dynamicCache;
    private final Map<K, V> staticCache;
    private final long defaultExpireTime;
    private final TimeUnit defaultTimeUnit;
    private final boolean enableStats;

    /**
     * 构造函数
     * 
     * @param maxSize 最大缓存大小
     * @param defaultExpireTime 默认过期时间
     * @param defaultTimeUnit 默认时间单位
     * @param enableStats 是否启用统计
     */
    public LocalCacheAdapter(long maxSize, long defaultExpireTime, TimeUnit defaultTimeUnit, boolean enableStats) {
        this.defaultExpireTime = defaultExpireTime;
        this.defaultTimeUnit = defaultTimeUnit;
        this.enableStats = enableStats;

        // 构建动态缓存
        Caffeine<Object, Object> builder = Caffeine.newBuilder()
                .maximumSize(maxSize)
                .expireAfterWrite(defaultExpireTime, defaultTimeUnit);

        if (enableStats) {
            builder.recordStats();
        }

        this.dynamicCache = builder.build();
        
        // 静态缓存使用 ConcurrentHashMap，永不过期
        this.staticCache = new ConcurrentHashMap<>();

        logger.info("LocalCacheAdapter 初始化完成: maxSize={}, expireTime={}{}",
                maxSize, defaultExpireTime, defaultTimeUnit);
    }

    /**
     * 使用默认配置的构造函数
     */
    public LocalCacheAdapter() {
        this(1000, 60, TimeUnit.MINUTES, true);
    }

    @Override
    public V get(K key) {
        // 先检查静态缓存
        V value = staticCache.get(key);
        if (value != null) {
            return value;
        }
        
        // 再检查动态缓存
        return dynamicCache.getIfPresent(key);
    }

    @Override
    public V get(K key, Function<K, V> loader) {
        return get(key, loader, defaultExpireTime, defaultTimeUnit);
    }

    @Override
    public V get(K key, Function<K, V> loader, long expireTime, TimeUnit timeUnit) {
        // 先检查静态缓存
        V value = staticCache.get(key);
        if (value != null) {
            return value;
        }

        // 检查动态缓存
        value = dynamicCache.getIfPresent(key);
        if (value != null) {
            return value;
        }

        // 加载新值
        try {
            value = loader.apply(key);
            if (value != null) {
                put(key, value, expireTime, timeUnit);
            }
            return value;
        } catch (Exception e) {
            logger.error("加载缓存值失败: key={}", key, e);
            throw new CacheException("加载缓存值失败", e);
        }
    }

    @Override
    public void put(K key, V value) {
        put(key, value, defaultExpireTime, defaultTimeUnit);
    }

    @Override
    public void put(K key, V value, long expireTime, TimeUnit timeUnit) {
        if (key == null || value == null) {
            return;
        }

        // 如果过期时间为 0 或负数，视为永不过期，放入静态缓存
        if (expireTime <= 0) {
            putPermanent(key, value);
            return;
        }

        // 放入动态缓存
        dynamicCache.put(key, value);
        
        // 如果键已存在于静态缓存中，需要移除
        staticCache.remove(key);
    }

    @Override
    public void putPermanent(K key, V value) {
        if (key == null || value == null) {
            return;
        }

        staticCache.put(key, value);
        
        // 如果键已存在于动态缓存中，需要移除
        dynamicCache.invalidate(key);
    }

    @Override
    public void putAll(Map<K, V> map) {
        putAll(map, defaultExpireTime, defaultTimeUnit);
    }

    @Override
    public void putAll(Map<K, V> map, long expireTime, TimeUnit timeUnit) {
        if (map == null || map.isEmpty()) {
            return;
        }

        for (Map.Entry<K, V> entry : map.entrySet()) {
            put(entry.getKey(), entry.getValue(), expireTime, timeUnit);
        }
    }

    @Override
    public boolean containsKey(K key) {
        return staticCache.containsKey(key) || dynamicCache.getIfPresent(key) != null;
    }

    @Override
    public boolean remove(K key) {
        boolean removedFromStatic = staticCache.remove(key) != null;
        dynamicCache.invalidate(key);
        return removedFromStatic || dynamicCache.getIfPresent(key) == null;
    }

    @Override
    public long remove(Set<K> keys) {
        if (keys == null || keys.isEmpty()) {
            return 0;
        }

        long count = 0;
        for (K key : keys) {
            if (remove(key)) {
                count++;
            }
        }
        return count;
    }

    @Override
    public void clear() {
        staticCache.clear();
        dynamicCache.invalidateAll();
        logger.info("LocalCacheAdapter 缓存已清空");
    }

    @Override
    public long size() {
        return staticCache.size() + dynamicCache.estimatedSize();
    }

    @Override
    public Set<K> keySet() {
        Set<K> keys = ConcurrentHashMap.newKeySet();
        keys.addAll(staticCache.keySet());
        keys.addAll(dynamicCache.asMap().keySet());
        return keys;
    }

    @Override
    public boolean expire(K key, long expireTime, TimeUnit timeUnit) {
        // 本地缓存不支持动态设置过期时间
        // 需要重新设置值
        V value = get(key);
        if (value != null) {
            put(key, value, expireTime, timeUnit);
            return true;
        }
        return false;
    }

    @Override
    public long getExpire(K key, TimeUnit timeUnit) {
        // 本地缓存无法精确获取剩余过期时间
        if (staticCache.containsKey(key)) {
            return -1; // 永不过期
        }
        if (dynamicCache.getIfPresent(key) != null) {
            return 1; // 假设还有时间，具体时间无法获取
        }
        return -2; // 键不存在
    }

    @Override
    public boolean refresh(K key) {
        // 本地缓存的刷新就是重新设置过期时间
        return expire(key, defaultExpireTime, defaultTimeUnit);
    }

    @Override
    public CacheAdapter.CacheStats getStats() {
        if (!enableStats) {
            return new CacheAdapter.CacheStats(0, 0, 0, 0, size(), 1000);
        }

        com.github.benmanes.caffeine.cache.stats.CacheStats caffeineStats = dynamicCache.stats();
        return new CacheAdapter.CacheStats(
                caffeineStats.hitCount(),
                caffeineStats.missCount(),
                caffeineStats.loadCount(),
                caffeineStats.evictionCount(),
                size(),
                1000 // 假设最大大小
        );
    }

    @Override
    public CacheType getType() {
        return CacheType.LOCAL;
    }

    @Override
    public boolean isAvailable() {
        return true; // 本地缓存总是可用的
    }

    @Override
    public void shutdown() {
        clear();
        logger.info("LocalCacheAdapter 已关闭");
    }

    /**
     * 获取静态缓存大小
     */
    public long getStaticCacheSize() {
        return staticCache.size();
    }

    /**
     * 获取动态缓存大小
     */
    public long getDynamicCacheSize() {
        return dynamicCache.estimatedSize();
    }

    /**
     * 获取详细统计信息
     */
    public String getDetailedStats() {
        com.github.benmanes.caffeine.cache.stats.CacheStats stats = dynamicCache.stats();
        return String.format(
                "LocalCacheAdapter统计: 静态缓存=%d, 动态缓存=%d, 命中率=%.2f%%, 驱逐次数=%d",
                staticCache.size(),
                dynamicCache.estimatedSize(),
                stats.hitRate() * 100,
                stats.evictionCount()
        );
    }
}
