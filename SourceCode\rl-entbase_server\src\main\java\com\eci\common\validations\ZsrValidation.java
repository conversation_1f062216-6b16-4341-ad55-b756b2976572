package com.eci.common.validations;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target(ElementType.FIELD) // 只能用于字段
@Retention(RetentionPolicy.RUNTIME) // 在运行时保留
public @interface ZsrValidation {
    String errorMessage() default "";
    String displayName() default "";
    String name() default "";
    boolean required() default false;
    int length() default 50; // 定义一个 value 属性，用于指定字符串的最大长度
}
