package com.eci.common;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eci.project.omsGhSend.dao.OmsGhSendDao;
import com.eci.project.omsGhSend.entity.OmsGhSendEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @ClassName: Extensions
 * @Author: guangyan.mei
 * @Date: 2025/5/16 13:44
 * @Description: TODO
 */
@Service
public class Extensions {

    @Autowired
    private OmsGhSendDao omsGhSendDao;

    public void addOmsGh(String orderNo) {
        List<OmsGhSendEntity> ghSendEList = omsGhSendDao.select()
                .eq(OmsGhSendEntity::getOrderNo, orderNo)
                .eq(OmsGhSendEntity::getOpFlag, "0")
                .list();
        if (ghSendEList != null && ghSendEList.size() > 0) {
            OmsGhSendEntity ghSendE = ghSendEList.get(0);
            OmsGhSendEntity ghSend = new OmsGhSendEntity();
            ghSend.setGuid(IdWorker.get32UUID());
            ghSend.setOrderNo(orderNo);
            omsGhSendDao.insert(ghSend);
        }
    }

}
