# 缓存配置指南

## 📋 配置概述

DictFieldUtils 缓存系统支持本地缓存和 Redis 缓存的自动切换，通过配置文件可以灵活控制缓存策略。

## 🔧 配置项说明

### 1. Redis 基础配置

```yaml
spring:
  redis:
    host: ***************    # Redis 服务器地址
    port: 6379               # Redis 端口
    database: 13             # 数据库索引
    password: Redis_1qaz     # 密码
    timeout: 5000            # 连接超时时间(毫秒)
    lettuce:
      pool:
        max-active: 8        # 最大连接数
        max-wait: -1         # 最大等待时间
        max-idle: 8          # 最大空闲连接
        min-idle: 0          # 最小空闲连接
```

### 2. 缓存策略配置

```yaml
cache:
  # 缓存策略选择
  strategy: auto             # auto | redis | local
  
  # 本地缓存配置
  local:
    max-size: 1000          # 最大缓存条目数
    expire-minutes: 60      # 默认过期时间(分钟)
  
  # Redis 缓存配置
  redis:
    connection-test-timeout: 3000  # 连接测试超时(毫秒)
    
    # 降级策略配置
    fallback:
      enabled: true         # 是否启用降级
      max-errors: 5         # 最大错误次数
      error-window-seconds: 60  # 错误窗口时间(秒)
```

## 📚 配置策略详解

### 1. 自动策略 (auto)

**推荐用于生产环境**

```yaml
cache:
  strategy: auto
```

**行为说明**：
- 启动时自动检测 Redis 可用性
- Redis 可用 → 使用 Redis 缓存
- Redis 不可用 → 降级到本地缓存
- 运行时定期重新检测 Redis 状态

**适用场景**：
- 生产环境部署
- 需要高可用性的系统
- Redis 服务可能不稳定的环境

### 2. 强制 Redis 策略 (redis)

**适用于确保 Redis 可用的环境**

```yaml
cache:
  strategy: redis
```

**行为说明**：
- 强制使用 Redis 缓存
- Redis 不可用时仍尝试使用 Redis
- 失败时降级到本地缓存

**适用场景**：
- Redis 服务稳定的生产环境
- 需要分布式缓存的集群部署
- 对缓存一致性要求较高的场景

### 3. 强制本地策略 (local)

**适用于开发和测试环境**

```yaml
cache:
  strategy: local
```

**行为说明**：
- 强制使用本地内存缓存
- 不尝试连接 Redis
- 性能最佳，但不支持分布式

**适用场景**：
- 开发环境
- 单机部署
- 不需要分布式缓存的场景

## 🎯 环境配置示例

### 开发环境 (application-dev.yml)

```yaml
spring:
  redis:
    host: localhost
    port: 6379
    database: 0
    password: 
    timeout: 3000

cache:
  strategy: local           # 开发环境使用本地缓存
  local:
    max-size: 500
    expire-minutes: 30
```

### 测试环境 (application-test.yml)

```yaml
spring:
  redis:
    host: test-redis.company.com
    port: 6379
    database: 1
    password: test_password
    timeout: 5000

cache:
  strategy: auto            # 测试环境自动检测
  local:
    max-size: 800
    expire-minutes: 45
  redis:
    connection-test-timeout: 2000
```

### 生产环境 (application-prod.yml)

```yaml
spring:
  redis:
    host: prod-redis.company.com
    port: 6379
    database: 2
    password: ${REDIS_PASSWORD}  # 从环境变量读取
    timeout: 5000
    lettuce:
      pool:
        max-active: 16
        max-wait: 3000
        max-idle: 8
        min-idle: 2

cache:
  strategy: auto            # 生产环境自动检测
  local:
    max-size: 2000
    expire-minutes: 60
  redis:
    connection-test-timeout: 3000
    fallback:
      enabled: true
      max-errors: 3
      error-window-seconds: 30
```

## 🔍 配置验证

### 1. 启动日志检查

正常启动时应看到以下日志：

```
INFO  - CacheConfigDetector 初始化完成，当前策略: REDIS
INFO  - CacheFactory 初始化完成，当前策略: REDIS
INFO  - DictFieldUtilsV3 初始化完成，缓存策略: REDIS
```

### 2. 配置测试接口

可以通过以下方式测试配置：

```java
@RestController
public class CacheTestController {
    
    @Autowired
    private CacheFactory cacheFactory;
    
    @GetMapping("/cache/status")
    public String getCacheStatus() {
        return cacheFactory.getCacheStats();
    }
    
    @GetMapping("/cache/config")
    public String getCacheConfig() {
        return cacheFactory.getConfigDetector().getCacheConfig().toString();
    }
}
```

## ⚠️ 常见问题

### 1. Redis 连接失败

**现象**：日志显示 "Redis 连接测试失败"

**解决方案**：
- 检查 Redis 服务是否启动
- 验证网络连接和防火墙设置
- 确认用户名密码正确
- 检查 Redis 配置中的 `bind` 和 `protected-mode` 设置

### 2. 缓存策略不生效

**现象**：配置了 Redis 但仍使用本地缓存

**解决方案**：
- 检查配置文件语法是否正确
- 确认 `@ConfigurationProperties` 注解生效
- 查看启动日志中的策略检测结果

### 3. 内存使用过高

**现象**：应用内存持续增长

**解决方案**：
- 调整 `cache.local.max-size` 参数
- 缩短 `cache.local.expire-minutes` 时间
- 启用内存监控和告警

## 📊 性能调优建议

### 1. 本地缓存调优

```yaml
cache:
  local:
    max-size: 2000          # 根据内存大小调整
    expire-minutes: 30      # 根据数据更新频率调整
```

### 2. Redis 连接池调优

```yaml
spring:
  redis:
    lettuce:
      pool:
        max-active: 16      # 根据并发量调整
        max-wait: 3000      # 根据响应时间要求调整
        max-idle: 8         # 保持适量空闲连接
```

### 3. 降级策略调优

```yaml
cache:
  redis:
    fallback:
      max-errors: 3         # 降低以快速降级
      error-window-seconds: 30  # 缩短窗口时间
```

## 🔄 动态配置更新

支持运行时动态更新缓存策略：

```java
@Autowired
private CacheFactory cacheFactory;

// 强制重新检测缓存策略
cacheFactory.refreshCacheStrategy();

// 切换到指定策略
cacheFactory.switchToStrategy(CacheConfigDetector.CacheStrategy.LOCAL);
```

## 📈 监控和告警

建议监控以下指标：

1. **缓存命中率**：通过 `getCacheStats()` 获取
2. **内存使用率**：通过 `MemoryMonitor.getMemoryStats()` 获取
3. **Redis 连接状态**：通过 `isRedisAvailable()` 检查
4. **错误率**：监控缓存操作异常

设置告警阈值：
- 缓存命中率 < 80%
- 内存使用率 > 85%
- Redis 连接失败率 > 5%
