package com.eci.project.omsOrderFwxmWorkTrace.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @ClassName: OmsOrderFwxmWorkInfoEntity
 * @Author: guangyan.mei
 * @Date: 2025/7/9 11:18
 * @Description: TODO
 */
@Data
public class OmsOrderFwxmWorkInfoEntity {

    public String fwxmCodeName;

    private String actualOkDateStatus;
    private String rowFkStatus;
    private String rowSaveStatus;

    @TableField("UPDATE_DATE")
    @ApiModelProperty("更新时间")
    private Date updateDate;

    @TableField("UPDATE_USER_NAME")
    @ApiModelProperty("更新人姓名")
    private String updateUserName;

    @TableField("GUID")
    @ApiModelProperty("跟踪表GUID")
    private String guid;

    @TableField("TASK_SEQ")
    @ApiModelProperty("任务序号")
    private Integer taskSeq;

    @TableField("BIZ_REG_ID")
    @ApiModelProperty("业务登记ID")
    private String bizRegId;

    @TableField("WORK_NO")
    @ApiModelProperty("工作号")
    private String workNo;

    @TableField("FWXM_CODE")
    @ApiModelProperty("服务项目代码")
    private String fwxmCode;

    @TableField("DOC_NO")
    @ApiModelProperty("文档编号")
    private String docNo;

    @TableField("SYS_CODE")
    @ApiModelProperty("系统代码")
    private String sysCode;

    @TableField("SEND_USER_NAME")
    @ApiModelProperty("发送用户名称")
    private String sendUserName;

    @TableField("XZWT_NO")
    @ApiModelProperty("协转委托编号")
    private String xzwtNo;

    @TableField("ORDER_NO")
    @ApiModelProperty("订单号")
    private String orderNo;

    @TableField("FEEDBACK_URL")
    @ApiModelProperty("反馈URL")
    private String feedbackUrl;


    @TableField("LINK_CODE_COLOR")
    @ApiModelProperty("环节代码颜色")
    private String linkCodeColor;

    @TableField("FWXM_NAME")
    @ApiModelProperty("服务项目名称")
    private String fwxmName;

    @TableField("BILL_CODE_NAME")
    @ApiModelProperty("单据类型名称")
    private String billCodeName;

    @TableField("STATUS_NAME")
    @ApiModelProperty("状态名称")
    private String statusName;

    @TableField("TASK_PRE")
    @ApiModelProperty("前置任务")
    private String taskPre;

    @TableField("STAGE")
    @ApiModelProperty("阶段")
    private String stage;

    @TableField("STATUS")
    @ApiModelProperty("状态")
    private String status;

    @TableField("LINK_SEQ")
    @ApiModelProperty("环节序号")
    private Integer linkSeq;

    @TableField("LINK_CODE")
    @ApiModelProperty("环节代码")
    private String linkCode;



    @TableField("LINK_CODE_NAME")
    @ApiModelProperty("作业环节名称")
    private String linkCodeName;

    @TableField("PLAN_OK_DATE")
    @ApiModelProperty("计划完成日期")
    private String planOkDate;

    @TableField("ACTUAL_OK_DATE")
    @ApiModelProperty("实际完成日期")
    private String actualOkDate;

    @TableField("IS_EXCEPTION")
    @ApiModelProperty("是否有异常 (Y/N)")
    private String isException;

    @TableField("IS_HAS_FILE")
    @ApiModelProperty("是否有附件 (Y/N)")
    private String isHasFile;

    @TableField("IS_WB")
    @ApiModelProperty("是否委外")
    private String isWb;

    @TableField("GYS_CODE_NAME")
    @ApiModelProperty("供应商名称")
    private String gysCodeName;

    @TableField("NODE_CODE_NB_NAME")
    @ApiModelProperty("内部节点名称")
    private String nodeCodeNbName;

    @TableField("RESPONSE_CODE")
    @ApiModelProperty("响应代码")
    private String responseCode;

    @TableField("SEND_DATE")
    @ApiModelProperty("分发时间")
    private Date sendDate;

    @TableField("TEL")
    @ApiModelProperty("联系电话")
    private String tel;

    @TableField("E_MAIL")
    @ApiModelProperty("电子邮件")
    private String eMail;

    @TableField("FWLX_CODE")
    @ApiModelProperty("服务类型代码")
    private String fwlxCode;

    @TableField("RESPONSE_CODE_NAME")
    @ApiModelProperty("响应代码名称")
    private String responseCodeName;

    @TableField("FWLX_NAME")
    @ApiModelProperty("服务类型名称")
    private String fwlxName;

    @TableField("OP_COMPLETE_OK")
    @ApiModelProperty("作业完成 (√/null)")
    private String opCompleteOk;

    @TableField("DATA_OK")
    @ApiModelProperty("作业数据齐全 (√/null)")
    private String dataOk;

    @TableField("AR_OK")
    @ApiModelProperty("应收费用齐全 (√/null)")
    private String arOk;

    @TableField("ARAP_OK")
    @ApiModelProperty("应付费用齐全 (√/null)")
    private String arapOk;

    @TableField("NEXT_ORDER_SUM")
    @ApiModelProperty("后续订单总数")
    private Integer nextOrderSum;

    @TableField("PRE_WORK_NO")
    @ApiModelProperty("前置工作号")
    private String preWorkNo;

    @TableField("CONSIGNEE_CODE")
    @ApiModelProperty("委托方代码")
    private String consigneeCode;

    @TableField("COUNT")
    @ApiModelProperty("服务项目计数")
    private Integer count;

}
