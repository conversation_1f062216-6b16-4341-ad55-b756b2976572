package com.eci.common;

import java.io.*;
import java.net.URL;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.List;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.common.util.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

public class Zsr {


    /**
     * 实体类表名扫描工具类
     * 用于扫描指定包路径下所有带有@TableName注解的实体类，并将表名写入文件
     */
    public static class EntityScanner {

        // 私有构造器防止实例化
        private EntityScanner() {
        }

        /**
         * 扫描指定包路径下的实体类并输出表名到文件
         *
         * @param basePackage    基础包路径（例如：com.example.project）
         * @param outputFilePath 输出文件路径（例如：/output/tables.txt）
         * @throws IOException 当文件操作出现异常时抛出
         */
        public static void scanAndWriteTableNames(java.lang.String basePackage, java.lang.String outputFilePath) throws IOException {
            PrintUtil.customPrint(">>> 1.获取第一级子包,扫描路径 {}", basePackage);
            // 获取第一级子包
            List<java.lang.String> firstLevelPackages = getFirstLevelPackages(basePackage);

            PrintUtil.customPrint(">>> 2.构建entity包路径,一级子包一共 {} 个", firstLevelPackages.size());
            // 构建entity包路径
            List<java.lang.String> entityPackages = buildEntityPackages(firstLevelPackages);

            PrintUtil.customPrint(">>> 3.扫描所有entity包获取表名,entity包路径一共 {} 个", entityPackages.size());
            // 扫描所有entity包获取表名
            List<java.lang.String> tableNames = scanEntityPackages(entityPackages);

            PrintUtil.customPrint(">>> 4.将结果写入文件:表名一共 {} 个", tableNames.size());
            // 将结果写入文件
            writeTableNamesToFile(tableNames, outputFilePath);

            PrintUtil.customPrint(">>> 5.扫描结束,文件保存在 {}", outputFilePath);
        }

        /**
         * 获取指定包的第一级子包列表
         *
         * @param basePackage 基础包路径
         * @return 第一级子包全限定名列表
         * @throws IOException 当资源读取失败时抛出
         */
        private static List<java.lang.String> getFirstLevelPackages(java.lang.String basePackage) throws IOException {
            List<java.lang.String> packages = new ArrayList<>();
            ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
            java.lang.String path = basePackage.replace('.', '/');

            try {
                Enumeration<URL> resources = classLoader.getResources(path);
                while (resources.hasMoreElements()) {
                    URL resource = resources.nextElement();
                    File directory = new File(resource.getFile());

                    if (directory.exists()) {
                        for (File file : directory.listFiles()) {
                            if (file.isDirectory()) {
                                packages.add(basePackage + "." + file.getName());
                            }
                        }
                    }
                }
            } catch (IOException e) {
                throw new RuntimeException("读取资源时发生IO异常", e);
            }
            return packages;
        }

        /**
         * 构建entity包路径列表
         *
         * @param packages 原始包列表
         * @return 拼接后的entity包路径列表
         */
        private static List<java.lang.String> buildEntityPackages(List<java.lang.String> packages) {
            List<java.lang.String> entityPackages = new ArrayList<>();
            for (java.lang.String pkg : packages) {
                entityPackages.add(pkg + ".entity");
            }
            return entityPackages;
        }

        /**
         * 扫描多个包路径下的所有实体类
         *
         * @param packages 需要扫描的包列表
         * @return 包含所有表名的列表
         */
        private static List<java.lang.String> scanEntityPackages(List<java.lang.String> packages) {
            List<java.lang.String> tableNames = new ArrayList<>();
            for (java.lang.String pkg : packages) {
                tableNames.addAll(scanPackage(pkg));
            }
            return tableNames;
        }

        /**
         * 扫描单个包路径下的实体类
         *
         * @param basePackage 包路径
         * @return 包含表名的列表
         */
        private static List<java.lang.String> scanPackage(java.lang.String basePackage) {
            List<java.lang.String> tableNames = new ArrayList<>();
            ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
            java.lang.String path = basePackage.replace('.', '/');

            try {
                Enumeration<URL> resources = classLoader.getResources(path);
                while (resources.hasMoreElements()) {
                    URL resource = resources.nextElement();
                    scanDirectory(new File(resource.getFile()), basePackage, tableNames);
                }
            } catch (IOException e) {
                throw new RuntimeException("扫描包时发生IO异常", e);
            }
            return tableNames;
        }

        /**
         * 递归扫描目录中的类文件
         *
         * @param directory   当前扫描的目录
         * @param packageName 当前包名
         * @param tableNames  存储结果的列表
         */
        private static void scanDirectory(File directory, java.lang.String packageName, List<java.lang.String> tableNames) {
            if (!directory.exists()) {
                return;
            }

            for (File file : directory.listFiles()) {
                if (file.isDirectory()) {
                    scanDirectory(file, packageName + "." + file.getName(), tableNames);
                } else if (file.getName().endsWith(".class")) {
                    processClassFile(packageName, file.getName(), tableNames);
                }
            }
        }

        /**
         * 处理单个类文件
         *
         * @param packageName 包名
         * @param fileName    文件名
         * @param tableNames  结果列表
         */
        private static void processClassFile(java.lang.String packageName, java.lang.String fileName, List<java.lang.String> tableNames) {
            java.lang.String className = packageName + '.' + fileName.substring(0, fileName.length() - 6);

            try {
                Class<?> clazz = Class.forName(className);
                TableName annotation = clazz.getAnnotation(TableName.class);

                if (annotation != null) {
                    tableNames.add(annotation.value());
                }
            } catch (ClassNotFoundException e) {
                throw new RuntimeException("类加载失败: " + className, e);
            }
        }

        /**
         * 将表名列表写入文件
         *
         * @param tableNames 表名列表
         * @param filePath   文件路径
         * @throws IOException 当文件写入失败时抛出
         */
        private static void writeTableNamesToFile(List<java.lang.String> tableNames, java.lang.String filePath) throws IOException {
            try (BufferedWriter writer = new BufferedWriter(new FileWriter(filePath))) {
                for (java.lang.String name : tableNames) {
                    writer.write(",\"" + name + "\"");
                    writer.newLine();
                }
            }
        }
    }

    /**
     * 字段扩展类，支持获取字段描述
     */
    public static class Fields {
        /**
         * 获取类及其所有父类的字段
         *
         * @param cls 类
         * @return 包含所有字段的列表
         */
        public static List<Field> getAllFields(Class<?> cls) {
            List<Field> fields = new ArrayList<>();
            while (cls != null && cls != Object.class) { // 遍历父类，直到 Object 类
                fields.addAll(Arrays.asList(cls.getDeclaredFields()));
                cls = cls.getSuperclass(); // 获取父类
            }
            return fields;
        }
    }

    /**
     * StringBuilder的扩展类，支持C#风格的格式化追加字符串
     */
    public static class StringBuilder {
        private java.lang.StringBuilder stringBuilder;

        public StringBuilder() {
            this.stringBuilder = new java.lang.StringBuilder();
        }

        public StringBuilder(java.lang.String str) {
            this.stringBuilder = new java.lang.StringBuilder(str);
        }

        /**
         * 实现C#风格的AppendFormat功能，使用{0} {1} {2}这样的格式
         *
         * @param format 格式化字符串
         * @param args   参数
         * @return 当前对象，支持链式调用
         */
        public StringBuilder AppendFormat(java.lang.String format, Object... args) {
            java.lang.String result = format;
            for (int i = 0; i < args.length; i++) {
                result = result.replace("{" + i + "}", java.lang.String.valueOf(args[i]));
            }
            stringBuilder.append(result);
            return this;
        }

        /**
         * 实现C#风格的AppendFormat功能，使用{0} {1} {2}这样的格式
         *
         * @param format 格式化字符串
         * @param args   参数
         * @return 当前对象，支持链式调用
         */
        public StringBuilder appendFormat(java.lang.String format, Object... args) {
            java.lang.String result = format;
            for (int i = 0; i < args.length; i++) {
                result = result.replace("{" + i + "}", java.lang.String.valueOf(args[i]));
            }
            stringBuilder.append(result);
            return this;
        }

        /**
         * 普通追加字符串
         *
         * @param str 要追加的字符串
         * @return 当前对象，支持链式调用
         */
        public StringBuilder Append(java.lang.String str) {
            stringBuilder.append(str);
            return this;
        }

        /**
         * 普通追加字符串
         *
         * @param str 要追加的字符串
         * @return 当前对象，支持链式调用
         */
        public StringBuilder append(java.lang.String str) {
            stringBuilder.append(str);
            return this;
        }


        /**
         * 实现类似C#的string.Format功能
         *
         * @param format 格式化字符串，使用{0} {1} {2}这样的格式
         * @param args   要替换的参数
         * @return 格式化后的字符串
         */
        public static java.lang.String Format(java.lang.String format, Object... args) {
            if (format == null) {
                return "";
            }
            java.lang.String result = format;
            for (int i = 0; i < args.length; i++) {
                java.lang.String value = args[i] == null ? "" : java.lang.String.valueOf(args[i]);
                result = result.replace("{" + i + "}", value);
            }
            return result;
        }


        @Override
        public java.lang.String toString() {
            return stringBuilder.toString();
        }
    }

    /**
     * 字符串扩展类，支持C#风格的格式化
     */
    public static class String {

        /**
         * 安全地截取字符串的前 N 位。
         * 如果字符串为 null 或长度不足 N，则返回整个字符串。
         *
         * @param str    要截取的字符串
         * @param length 要截取的长度
         * @return 截取后的字符串
         */
        public static java.lang.String safeSubstring(java.lang.String str, int length) {
            if (str == null || str.isEmpty()) {
                return str; // 或者返回 ""
            }
            // 如果字符串长度小于等于要截取的长度，则返回整个字符串
            if (str.length() <= length) {
                return str;
            } else {
                // 否则，截取前 length 位
                return str.substring(0, length);
            }
        }

        /**
         * 空字符串
         */
        public static final java.lang.String Empty = "";

        /**
         * 实现类似C#的string.Format功能
         *
         * @param format 格式化字符串，使用{0} {1} {2}这样的格式
         * @param args   要替换的参数
         * @return 格式化后的字符串
         */
        public static java.lang.String format(java.lang.String format, Object... args) {
            if (format == null) {
                return "";
            }
            java.lang.String result = format;
            for (int i = 0; i < args.length; i++) {
                java.lang.String value = args[i] == null ? "" : java.lang.String.valueOf(args[i]);
                result = result.replace("{" + i + "}", value);
            }
            return result;
        }

        /**
         * 实现类似C#的string.Format功能
         *
         * @param format 格式化字符串，使用{0} {1} {2}这样的格式
         * @param args   要替换的参数
         * @return 格式化后的字符串
         */
        public static java.lang.String Format(java.lang.String format, Object... args) {
            if (format == null) {
                return "";
            }
            java.lang.String result = format;
            for (int i = 0; i < args.length; i++) {
                java.lang.String value = args[i] == null ? "" : java.lang.String.valueOf(args[i]);
                result = result.replace("{" + i + "}", value);
            }
            return result;
        }

        /**
         * 判断字符串是否为空或空格
         *
         * @param str 字符串
         * @return 如果为空则为True 反之为 False
         */
        public static boolean IsNullOrWhiteSpace(java.lang.String str) {
            if (str == null) {
                return true;
            }

            return StringUtils.isEmpty(str);
        }

        /**
         * 判断时间是否为空
         *
         * @param date 时间
         * @return 如果为空则为True 反之为 False
         */
        public static boolean IsNullOrWhiteSpace(Date date) {
            return date == null; // 直接返回条件判断结
        }

        /**
         * 判断字符串是否为空或空格
         *
         * @param str 字符串
         * @return 如果为空则为True 反之为 False
         */
        public static boolean IsNullOrEmpty(java.lang.String str) {
            if (str == null) {
                return true;
            }

            return StringUtils.isEmpty(str);
        }

        /**
         * 判断字符串是否为空或空格
         *
         * @param str 时间
         * @return 如果为空则为True 反之为 False
         */
        public static boolean IsNullOrEmpty(Date str) {
            if (str == null) {
                return true;
            }
            return false;
        }

        /**
         * 将逗号分隔的字符串转换为List<String>，处理null、空字符串以及分割产生的空元素。
         *
         * @param str 逗号分隔的字符串
         * @return 包含分割后非空元素的List<String>，如果输入为null或空字符串则返回空List
         */
        public static List<java.lang.String> string2List(java.lang.String str) {
            if (str == null || StringUtils.isEmpty(str)) {
                // 如果是 null 或空，直接返回一个不可变的空 List
                // Collections.emptyList() 是返回空 List 的标准、高效的方式
                return Collections.emptyList();
            }

            // 步骤 2: 如果输入非空，则进行分割、流化、过滤和收集
            return Arrays.stream(str.split(",")) // 将字符串按逗号分割成数组，然后转换为 Stream
                    .filter(s -> !s.trim().isEmpty()) // 步骤 3: 过滤掉空字符串或只包含空白字符的字符串
                    // trim() 去除首尾空白，isEmpty() 检查是否为空
                    .collect(Collectors.toList());    // 步骤 4: 将过滤后的元素收集到 List 中
        }


        /**
         * 将逗号分隔的字符串转换为 SQL IN 子句格式 ('value1','value2')
         * 处理 null、空字符串以及分割后可能产生的空元素。
         * 假设需要对值进行 SQL 转义和加引号。
         *
         * @param key 逗号分隔的字符串
         * @return 格式化后的 SQL IN 子句字符串，如果输入为 null 或空则返回 "()"
         */
        public static java.lang.String toInCondition(java.lang.String key) {
            // 步骤 1: 处理 null 或空字符串输入
            // 如果输入为 null 或空（包括只包含空白字符），返回空的 IN 子句 "()"
            if (key == null || key.trim().isEmpty()) {
                return "()";
            }

            // 步骤 2: 按逗号分割字符串
            java.lang.String[] parts = key.split(",");

            // 步骤 3: 使用 Stream API 处理分割后的数组
            List<java.lang.String> quotedParts = Arrays.stream(parts)
                    .map(java.lang.String::trim)          // 对每个分割出来的字符串去除首尾空白
                    .filter(s -> !s.isEmpty())  // 过滤掉空字符串（例如输入 ",," 或 ",a," 会产生空字符串）
                    .map(d -> Zsr.String.sqlQuote(d))    // 对每个非空字符串进行 SQL 加引号和转义
                    .collect(Collectors.toList()); // 将处理后的字符串收集到 List

            // 步骤 4: 如果过滤和处理后列表为空（例如输入是 "," 或 " "），返回 "()"
            if (quotedParts.isEmpty()) {
                return "()";
            }

            // 步骤 5: 使用 String.join 将所有处理后的字符串用逗号连接起来
            // 步骤 6: 在结果字符串前后加上括号
            return "(" + java.lang.String.join(",", quotedParts) + ")";
        }

        /**
         * 模拟 C# cmn.SQLQ 方法: 为字符串加单引号并转义内部的单引号
         *
         * @param value 需要处理的字符串
         * @return SQL 安全的带单引号的字符串
         */
        private static java.lang.String sqlQuote(java.lang.String value) {
            // 假设 value 在进入此方法前已经处理过 null 和 trim
            if (value == null) {
                return "NULL"; // 或者根据需要返回 "'NULL'"
            }
            // 将内部的单引号 ' 替换为两个单引号 '' 进行转义
            java.lang.String escapedValue = value.replace("'", "''");
            // 在前后加上单引号
            return "'" + escapedValue + "'";
        }

        /**
         * 将布尔值转换为 Y/N 字符串
         *
         * @param value 布尔值
         * @return "Y" 或 "N"
         */
        public static java.lang.String toYN(boolean value) {
            return value ? "Y" : "N";
        }

    }


    /**
     * 季度日期对象，包含开始和结束月份
     */
    @Data
    public static class QuarterDate {
        private java.lang.String startDate;
        private java.lang.String endDate;

        public QuarterDate(java.lang.String startDate, java.lang.String endDate) {
            this.startDate = startDate;
            this.endDate = endDate;
        }
    }

    /**
     * 将季度字符串转换为开始和结束月份
     * 支持两种格式：
     * 1. "2025年01季度"
     * 2. "202501"
     *
     * @param quarterStr 季度字符串
     * @return QuarterDate 包含开始月份和结束月份，如果输入格式不正确返回null
     */
    public static QuarterDate parseQuarter(java.lang.String quarterStr) {
        if (StringUtils.isEmpty(quarterStr)) {
            return null;
        }

        try {
            java.lang.String year;
            java.lang.String quarter;

            // 判断输入格式并解析
            if (quarterStr.matches("\\d{4}年0[1-4]季度")) {
                // 处理 "2025年01季度" 格式
                year = quarterStr.substring(0, 4);
                quarter = quarterStr.substring(6, 7);
            } else if (quarterStr.matches("\\d{4}0[1-4]")) {
                // 处理 "202501" 格式
                year = quarterStr.substring(0, 4);
                quarter = quarterStr.substring(5, 6);
            } else {
                return null;
            }

            // 验证年份的合理性（可选）
            int yearNum = Integer.parseInt(year);
            if (yearNum < 1900 || yearNum > 2100) {
                return null;
            }

            java.lang.String startDate = StringUtils.EMPTY;
            java.lang.String endDate = StringUtils.EMPTY;

            switch (quarter) {
                case "1":
                    startDate = year + "01";
                    endDate = year + "03";
                    break;
                case "2":
                    startDate = year + "04";
                    endDate = year + "06";
                    break;
                case "3":
                    startDate = year + "07";
                    endDate = year + "09";
                    break;
                case "4":
                    startDate = year + "10";
                    endDate = year + "12";
                    break;
                default:
                    return null;
            }

            return new QuarterDate(startDate, endDate);

        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 获取指定类中所有带有 {@link ApiModelProperty} 注解的字段及其描述信息。
     *
     * @param clazz 要解析的类
     * @return 字段名与描述信息的 Map，如果字段没有 {@link ApiModelProperty} 注解，则不会包含在 Map 中。
     * 如果解析过程中发生异常，则返回空的 Map。
     */
    public static Map<java.lang.String, java.lang.String> getFieldDescriptions(Class<?> clazz) {
        Map<java.lang.String, java.lang.String> descriptions = new LinkedHashMap<>();
        if (clazz == null) {
            return descriptions; // 类为空，直接返回空 Map
        }
        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            ApiModelProperty annotation = field.getAnnotation(ApiModelProperty.class);
            if (annotation != null) {
                descriptions.put(field.getName(), annotation.value());
            } else {
                descriptions.put(field.getName(), field.getName());
            }
        }
        return descriptions;
    }

    /**
     * 创建 DTO 到 Map 的转换方法
     * <remark>于将 DataReportExportDTO 对象转换为 Map<String, Object></remark>
     */
    public static class DTOConverter {
        public static Map<java.lang.String, Object> convertDTOToMap(Object dto) {
            if (dto == null) {
                return null;
            }
            Map<java.lang.String, Object> map = new HashMap<>();
            Class<?> dtoClass = dto.getClass();
            Field[] fields = dtoClass.getDeclaredFields(); // 获取所有声明的字段，包括私有字段

            for (Field field : fields) {
                field.setAccessible(true); // 允许访问私有字段
                java.lang.String fieldName = field.getName();
                try {
                    Object fieldValue = field.get(dto);
                    map.put(fieldName, fieldValue);
                } catch (IllegalAccessException e) {
                    // 理论上 field.setAccessible(true) 已经允许访问，除非有安全管理器限制
                    e.printStackTrace(); // 实际应用中需要更完善的错误处理
                    // 可以选择忽略该字段或抛出异常，这里选择打印堆栈信息
                }
            }
            return map;
        }
    }


    public static class JsonUtil {
        /**
         * 将 JSON 字符串反序列化为 Java 对象，忽略字段大小写
         *
         * @param jsonString JSON 字符串
         * @param clazz      目标 Java 对象的 Class 类型
         * @param <T>        泛型类型，表示目标 Java 对象类型
         * @return 反序列化后的 Java 对象，如果反序列化失败则返回 null
         */
        public static <T> T deserializeIgnoreCase(java.lang.String jsonString, Class<T> clazz) {
            if (jsonString == null || jsonString.equals("") || clazz == null) {
                return null; // 或者抛出 IllegalArgumentException 等异常，根据您的需求决定
            }
            try {
                return JSON.parseObject(jsonString, clazz, Feature.SupportNonPublicField);
            } catch (Exception e) {
                // 在这里可以添加日志记录，例如使用 slf4j
                PrintUtil.customErrorPrint("JSON 反序列化失败: {}, JSON String: {},Target Class:{}", e.getMessage(), jsonString, clazz.getName());
                return null; // 或者抛出更具体的自定义异常，根据您的需求决定
            }
        }
    }

    /**
     * 忽略大小写的 Map 工具类
     */
    public static class MapIgnoreCase {
        public static Object getIgnoreCase(Map<java.lang.String, Object> map, java.lang.String key) {
            return map.entrySet().stream()
                    .filter(e -> e.getKey().equalsIgnoreCase(key))
                    .map(Map.Entry::getValue)
                    .findFirst()
                    .orElse(null);
        }
    }
}

