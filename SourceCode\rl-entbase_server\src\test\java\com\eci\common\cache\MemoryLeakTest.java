package com.eci.common.cache;

import com.eci.common.ExpiringMapCache;
import com.eci.common.ExpiringMapCacheFixed;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Timeout;

import java.lang.management.ManagementFactory;
import java.lang.management.ThreadMXBean;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 内存泄露测试
 */
public class MemoryLeakTest {

    @Test
    @Timeout(value = 30, unit = TimeUnit.SECONDS)
    void testExpiringMapCacheThreadLeak() {
        ThreadMXBean threadBean = ManagementFactory.getThreadMXBean();
        int initialThreadCount = threadBean.getThreadCount();
        
        List<ExpiringMapCache<String, String>> caches = new ArrayList<>();
        
        // 创建多个缓存实例
        for (int i = 0; i < 10; i++) {
            caches.add(new ExpiringMapCache<>(1000));
        }
        
        // 等待线程启动
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        int afterCreationThreadCount = threadBean.getThreadCount();
        
        // 验证线程数量增加（说明有线程泄露）
        assertTrue(afterCreationThreadCount > initialThreadCount, 
                "应该有新线程被创建，表明存在线程泄露问题");
        
        // 清理缓存引用
        caches.clear();
        
        // 强制垃圾回收
        System.gc();
        System.runFinalization();
        
        // 等待一段时间
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        int afterGcThreadCount = threadBean.getThreadCount();
        
        // 验证线程没有被清理（说明线程泄露）
        assertTrue(afterGcThreadCount >= afterCreationThreadCount, 
                "线程应该仍然存在，说明存在线程泄露");
    }

    @Test
    @Timeout(value = 30, unit = TimeUnit.SECONDS)
    void testExpiringMapCacheFixedNoThreadLeak() {
        ThreadMXBean threadBean = ManagementFactory.getThreadMXBean();
        int initialThreadCount = threadBean.getThreadCount();
        
        List<ExpiringMapCacheFixed<String, String>> caches = new ArrayList<>();
        
        // 创建多个缓存实例
        for (int i = 0; i < 10; i++) {
            caches.add(new ExpiringMapCacheFixed<>(1000));
        }
        
        // 等待线程启动
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        int afterCreationThreadCount = threadBean.getThreadCount();
        
        // 验证线程数量增加
        assertTrue(afterCreationThreadCount > initialThreadCount, 
                "应该有新线程被创建");
        
        // 正确关闭缓存
        for (ExpiringMapCacheFixed<String, String> cache : caches) {
            cache.shutdown();
        }
        caches.clear();
        
        // 等待线程关闭
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        int afterShutdownThreadCount = threadBean.getThreadCount();
        
        // 验证线程被正确清理
        assertTrue(afterShutdownThreadCount <= initialThreadCount + 2, 
                "线程应该被正确清理，允许少量系统线程差异");
    }

    @Test
    void testMemoryUsageGrowth() {
        Runtime runtime = Runtime.getRuntime();
        
        // 记录初始内存使用
        runtime.gc();
        long initialMemory = runtime.totalMemory() - runtime.freeMemory();
        
        List<ExpiringMapCache<String, String>> caches = new ArrayList<>();
        
        // 创建大量缓存实例并添加数据
        for (int i = 0; i < 100; i++) {
            ExpiringMapCache<String, String> cache = new ExpiringMapCache<>(10000);
            
            // 向每个缓存添加数据
            for (int j = 0; j < 100; j++) {
                cache.put("key_" + i + "_" + j, "value_" + i + "_" + j);
            }
            
            caches.add(cache);
        }
        
        // 记录使用后的内存
        runtime.gc();
        long afterCreationMemory = runtime.totalMemory() - runtime.freeMemory();
        
        // 验证内存使用增加
        assertTrue(afterCreationMemory > initialMemory, 
                "内存使用应该增加");
        
        // 清理缓存
        caches.clear();
        
        // 强制垃圾回收
        for (int i = 0; i < 3; i++) {
            runtime.gc();
            System.runFinalization();
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
        
        long afterCleanupMemory = runtime.totalMemory() - runtime.freeMemory();
        
        // 验证内存是否被释放（允许一定的误差）
        long memoryDifference = afterCleanupMemory - initialMemory;
        long memoryGrowth = afterCreationMemory - initialMemory;
        
        // 内存释放应该至少释放 50% 的增长量
        assertTrue(memoryDifference < memoryGrowth * 0.5, 
                String.format("内存应该被大部分释放。初始: %d, 创建后: %d, 清理后: %d", 
                        initialMemory, afterCreationMemory, afterCleanupMemory));
    }

    @Test
    void testCacheGrowthWithoutLimit() {
        ExpiringMapCache<String, String> cache = new ExpiringMapCache<>(60000); // 1分钟过期
        
        // 添加大量数据
        for (int i = 0; i < 10000; i++) {
            cache.put("key_" + i, "value_" + i + "_".repeat(100)); // 每个值约100字节
        }
        
        // 验证缓存大小
        assertEquals(10000, cache.size());
        
        // 继续添加数据
        for (int i = 10000; i < 20000; i++) {
            cache.put("key_" + i, "value_" + i + "_".repeat(100));
        }
        
        // 验证缓存继续增长（没有大小限制）
        assertEquals(20000, cache.size());
        
        // 这展示了原版本缓存可能无限增长的问题
    }

    @Test
    void testFixedCacheWithProperCleanup() {
        ExpiringMapCacheFixed<String, String> cache = new ExpiringMapCacheFixed<>(1000); // 1秒过期
        
        try {
            // 添加数据
            for (int i = 0; i < 100; i++) {
                cache.put("key_" + i, "value_" + i);
            }
            
            assertEquals(100, cache.size());
            
            // 等待过期
            Thread.sleep(1500);
            
            // 手动清理过期项
            int removedCount = cache.cleanupExpired();
            assertTrue(removedCount > 0, "应该有过期项被清理");
            
            // 验证缓存大小减少
            assertTrue(cache.size() < 100, "缓存大小应该减少");
            
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            fail("测试被中断");
        } finally {
            cache.shutdown();
        }
    }

    @Test
    void testConcurrentAccessMemoryStability() throws InterruptedException {
        ExpiringMapCacheFixed<String, String> cache = new ExpiringMapCacheFixed<>(5000);
        
        try {
            int threadCount = 10;
            int operationsPerThread = 1000;
            Thread[] threads = new Thread[threadCount];
            
            // 创建多个线程并发访问缓存
            for (int i = 0; i < threadCount; i++) {
                final int threadId = i;
                threads[i] = new Thread(() -> {
                    for (int j = 0; j < operationsPerThread; j++) {
                        String key = "thread_" + threadId + "_key_" + j;
                        String value = "thread_" + threadId + "_value_" + j;
                        
                        cache.put(key, value);
                        cache.get(key);
                        
                        if (j % 100 == 0) {
                            cache.cleanupExpired();
                        }
                    }
                });
            }
            
            // 启动所有线程
            for (Thread thread : threads) {
                thread.start();
            }
            
            // 等待所有线程完成
            for (Thread thread : threads) {
                thread.join();
            }
            
            // 验证缓存状态
            assertTrue(cache.size() > 0, "缓存应该包含数据");
            assertFalse(cache.isShutdown(), "缓存不应该被关闭");
            
        } finally {
            cache.shutdown();
            assertTrue(cache.isShutdown(), "缓存应该被正确关闭");
        }
    }

    @Test
    void testMemoryMonitoring() {
        // 测试内存监控功能
        String memoryStats = ExpiringMapCacheFixed.MemoryMonitor.getMemoryStats();
        assertNotNull(memoryStats);
        assertTrue(memoryStats.contains("MB"));
        assertTrue(memoryStats.contains("%"));
        
        // 测试内存压力检测
        boolean hasMemoryPressure = ExpiringMapCacheFixed.MemoryMonitor.isMemoryPressure();
        // 这个值取决于当前系统状态，我们只验证方法能正常执行
        assertNotNull(hasMemoryPressure);
    }

    @Test
    void testResourceCleanupOnShutdown() {
        ExpiringMapCacheFixed<String, String> cache = new ExpiringMapCacheFixed<>(1000);
        
        // 添加一些数据
        cache.put("test1", "value1");
        cache.put("test2", "value2");
        assertEquals(2, cache.size());
        
        // 关闭缓存
        cache.shutdown();
        
        // 验证资源被清理
        assertTrue(cache.isShutdown());
        assertEquals(0, cache.size());
        
        // 验证关闭后的操作被忽略
        cache.put("test3", "value3");
        assertEquals(0, cache.size());
        assertNull(cache.get("test3"));
    }
}
