package com.eci.common;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 带自动过期时间的本地缓存工具类（基于 ConcurrentHashMap）
 */
public class ExpiringMapCache<K, V> {
    private final Map<K, CacheEntry<V>> cacheMap = new ConcurrentHashMap<>();
    private final long defaultTtlMillis;

    public ExpiringMapCache(long defaultTtlMillis) {
        this.defaultTtlMillis = defaultTtlMillis;
        startCleanupTask();
    }

    // 添加缓存，默认过期时间
    public void put(K key, V value) {
        cacheMap.put(key, new CacheEntry<>(value, defaultTtlMillis));
    }

    // 获取缓存，如果已过期则返回 null
    public V get(K key) {
        CacheEntry<V> entry = cacheMap.get(key);
        if (entry == null || entry.isExpired()) {
            cacheMap.remove(key); // 清理过期数据
            return null;
        }
        return entry.getValue();
    }

    // 删除缓存
    public void remove(K key) {
        cacheMap.remove(key);
    }

    // 清空所有缓存
    public void clear() {
        cacheMap.clear();
    }

    // 定时清理任务：每分钟检查一次
    private void startCleanupTask() {
        new Thread(() -> {
            while (true) {
                try {
                    Thread.sleep(60_000); // 每分钟执行一次
                    cacheMap.forEach((key, entry) -> {
                        if (entry.isExpired()) {
                            cacheMap.remove(key);
                        }
                    });
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }).start();
    }

    // 缓存条目结构：包含值和过期时间
    private static class CacheEntry<V> {
        private final V value;
        private final long expireAt; // 过期时间戳（毫秒）

        public CacheEntry(V value, long ttlMillis) {
            this.value = value;
            this.expireAt = System.currentTimeMillis() + ttlMillis;
        }

        public V getValue() {
            return value;
        }

        public boolean isExpired() {
            return System.currentTimeMillis() > expireAt;
        }
    }
}