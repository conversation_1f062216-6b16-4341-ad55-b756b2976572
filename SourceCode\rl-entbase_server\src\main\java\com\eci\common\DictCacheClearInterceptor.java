package com.eci.common;


import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 拦截器,清理 DictFieldUtils 的 Cache缓存
 */
@Component
public class DictCacheClearInterceptor implements HandlerInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 获取当前请求路径
        String uri = request.getRequestURI();

        // 判断是否包含 "login"（不区分大小写）
        if (uri.toLowerCase().contains("login")) {
            // 在这里执行清理缓存的操作
            clearCache();
        } else if (uri.toLowerCase().contains("sysenterpriseinfo/selectbycompanycode")) {
            // 在这里执行清理缓存的操作
            clearCache();
        }

//        // 判断是否匹配目标 URL
//        if ("/loginByPwd".equalsIgnoreCase( uri) ) {
//            // 在这里执行清理缓存的操作
//            clearCache();
//        }

        // 继续执行后续流程
        return true;
    }

    private void clearCache() {
        // 实际清理缓存的逻辑
        PrintUtil.customPrint("正在清理缓存...");
        // 例如清除本地缓存、Redis 缓存等
        // 刷新所有缓存
        DictFieldUtils.refreshCacheNow();

    }
}