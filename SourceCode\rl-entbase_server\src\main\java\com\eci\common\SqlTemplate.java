package com.eci.common;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * SQL 模板解析器，用于将 ${name} 形式的命名参数替换为 ? 占位符，并提取参数名。
 */
public class SqlTemplate {

    private static final Pattern PARAM_PATTERN = Pattern.compile("\\$\\{([^}]*)\\}");

    private final String originalSql;
    private final String renderedSql;
    private final List<String> paramNames;

    /**
     * 构造方法
     *
     * @param originalSql 原始 SQL 模板，包含 ${name} 格式的参数
     */
    public SqlTemplate(String originalSql) {
        if (originalSql == null) {
            throw new IllegalArgumentException("SQL 模板不能为空");
        }

        this.originalSql = originalSql;
        List<String> extractedParams = new ArrayList<>();
        Matcher matcher = PARAM_PATTERN.matcher(originalSql);

        // 使用 StringBuffer 进行安全的字符串替换
        StringBuffer sb = new StringBuffer();

        while (matcher.find()) {
            String paramName = matcher.group(1).trim();
            extractedParams.add(paramName);
            matcher.appendReplacement(sb, "?");
        }
        matcher.appendTail(sb);

        this.renderedSql = sb.toString();
        this.paramNames = Collections.unmodifiableList(new ArrayList<>(extractedParams));
    }

    /**
     * 用于反序列化的构造方法
     */
    public SqlTemplate(String originalSql, String renderedSql, List<String> paramNames) {
        this.originalSql = originalSql;
        this.renderedSql = renderedSql;
        this.paramNames = paramNames != null ? Collections.unmodifiableList(new ArrayList<>(paramNames)) : Collections.emptyList();
    }

    /**
     * 获取原始 SQL 模板
     */
    public String getOriginalSql() {
        return originalSql;
    }

    /**
     * 获取渲染后的 SQL（${xxx} 被替换为 ?）
     */
    public String getRenderedSql() {
        return renderedSql;
    }

    /**
     * 获取提取出的参数名列表（保持出现顺序）
     */
    public List<String> getParamNames() {
        return paramNames;
    }

    /**
     * 获取去重后的参数名集合
     */
    public Set<String> getUniqueParamNames() {
        return new LinkedHashSet<>(paramNames); // 保持顺序
    }

    /**
     * 静态工厂方法：快速构建
     */
    public static SqlTemplate of(String sql) {
        return new SqlTemplate(sql);
    }

    /**
     * 输出调试信息
     */
    @Override
    public String toString() {
        return "SqlTemplate{" +
                "originalSql='" + originalSql + '\'' +
                ", renderedSql='" + renderedSql + '\'' +
                ", paramNames=" + paramNames +
                '}';
    }
}