package com.eci.common;

import com.eci.common.enums.OrderEnum;
import com.eci.exception.BaseException;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName: OmsHelper
 * @Author: guangyan.mei
 * @Date: 2025/4/21 15:56
 * @Description: TODO
 */
public class OmsHelper {

    /// <summary>
    /// 报关单服务项目
    /// </summary>
    /// <param name="fwxmCode"></param>
    /// <returns></returns>
    public static boolean isBgdFwxm(String fwxmCode) {
        List<String> listFwxm = getBgdFwxm();
        return listFwxm.contains(fwxmCode);
    }

    /// <summary>
    /// 报关单服务项目
    /// </summary>
    /// <returns></returns>
    public static List<String> getBgdFwxm() {
        List<String> list = new ArrayList<>();

        // 原有
        list.add(OrderEnum.ServiceItem.BGD_I_SW.getCode());
        list.add(OrderEnum.ServiceItem.BGD_E_SW.getCode());
        list.add(OrderEnum.ServiceItem.BGD_I.getCode());
        list.add(OrderEnum.ServiceItem.BGD_E.getCode());

        // 202106新增
        list.add(OrderEnum.ServiceItem.BGD_I_YX_P.getCode());
        list.add(OrderEnum.ServiceItem.BGD_E_YX_P.getCode());
        list.add(OrderEnum.ServiceItem.BGD_I_EX_P.getCode());
        list.add(OrderEnum.ServiceItem.BGD_E_EX_P.getCode());
        list.add(OrderEnum.ServiceItem.BGD_I_YX_D.getCode());
        list.add(OrderEnum.ServiceItem.BGD_E_YX_D.getCode());
        list.add(OrderEnum.ServiceItem.BGD_I_EX_D.getCode());
        list.add(OrderEnum.ServiceItem.BGD_E_EX_D.getCode());
        list.add(OrderEnum.ServiceItem.BGD_I_QN_P.getCode());
        list.add(OrderEnum.ServiceItem.BGD_I_QN_D.getCode());
        list.add(OrderEnum.ServiceItem.KACHD.getCode());
        list.add(OrderEnum.ServiceItem.GWDDCC.getCode());

        return list;
    }

    /// <summary>
    /// 核注清单服务项目
    /// </summary>
    /// <param name="fwxmCode"></param>
    /// <returns></returns>
    public static boolean isHzqdFwxm(String fwxmCode) {
        List<String> listFwxm = getHzqdFwxm();
        return listFwxm.contains(fwxmCode);
    }

    /**
     * 核注清单服务项目
     *
     * @return 包含核注清单服务项目的列表
     */
    public static List<String> getHzqdFwxm() {
        List<String> list = new ArrayList<>();

        // 原有
        list.add(OrderEnum.ServiceItem.HZQD_I_SW.getCode());
        list.add(OrderEnum.ServiceItem.HZQD_E_SW.getCode());
        list.add(OrderEnum.ServiceItem.HZQD_I.getCode());
        list.add(OrderEnum.ServiceItem.HZQD_E.getCode());

        // 202106新增
        list.add(OrderEnum.ServiceItem.HZQD_I_QN_EX_P.getCode());
        list.add(OrderEnum.ServiceItem.HZQD_I_QN_EX_D.getCode());
        list.add(OrderEnum.ServiceItem.HZQD_E_QN_EX_P.getCode());
        list.add(OrderEnum.ServiceItem.HZQD_E_QN_EX_D.getCode());
        list.add(OrderEnum.ServiceItem.HZQD_I_QN_P.getCode());
        list.add(OrderEnum.ServiceItem.HZQD_I_QN_D.getCode());
        list.add(OrderEnum.ServiceItem.HZQD_E_QN_P.getCode());
        list.add(OrderEnum.ServiceItem.HZQD_E_QN_D.getCode());
        list.add(OrderEnum.ServiceItem.HZQD_E_EX_P.getCode());
        list.add(OrderEnum.ServiceItem.HZQD_E_EX_D.getCode());
        list.add(OrderEnum.ServiceItem.HZQD_I_EX_P.getCode());
        list.add(OrderEnum.ServiceItem.HZQD_I_EX_D.getCode());
        list.add(OrderEnum.ServiceItem.HZQD_E_YX_D.getCode());
        list.add(OrderEnum.ServiceItem.HZQD_E_YX_P.getCode());
        list.add(OrderEnum.ServiceItem.HZQD_I_YX_P.getCode());
        list.add(OrderEnum.ServiceItem.HZQD_I_YX_D.getCode());

        return list;
    }

    /// <summary>
    /// 出入库单
    /// </summary>
    /// <param name="fwxmCode"></param>
    /// <returns></returns>
    public static boolean isBgbjCrkdFwxm(String fwxmCode) {
        List<String> listFwxm = getBgbjCrkdFwxm();
        return listFwxm.contains(fwxmCode);
    }


    /**
     * 报关单核注清单服务项目
     *
     * @return 包含报关单核注清单服务项目的列表
     */
    public static List<String> getBgbjCrkdFwxm() {
        List<String> list = new ArrayList<>();

        // 原有
        list.add(OrderEnum.ServiceItem.CRKD_I.getCode());
        list.add(OrderEnum.ServiceItem.CRKD_SW.getCode());
        list.add(OrderEnum.ServiceItem.CRKD_E.getCode());
        list.add(OrderEnum.ServiceItem.CRKD_E_SW.getCode());

        // 新
        list.add(OrderEnum.ServiceItem.CRKD_I_EX_P.getCode());
        list.add(OrderEnum.ServiceItem.CRKD_I_EX_D.getCode());
        list.add(OrderEnum.ServiceItem.CRKD_E_EX_P.getCode());
        list.add(OrderEnum.ServiceItem.CRKD_E_EX_D.getCode());

        return list;
    }

    /// <summary>
    /// 报关单核注清单服务项目
    /// </summary>
    /// <param name="fwxmCode"></param>
    /// <returns></returns>
    public static boolean isBgdHzqdFwxm(String fwxmCode) {
        List<String> listFwxm = getHzqdBgdFwxm();
        return listFwxm.contains(fwxmCode);
    }


    /**
     * 报关单核注清单服务项目
     *
     * @return 包含报关单核注清单服务项目的列表
     */
    public static List<String> getHzqdBgdFwxm() {
        List<String> list = new ArrayList<>();

        // 原有
        list.add(OrderEnum.ServiceItem.HZQD_BGD_I_SW.getCode());
        list.add(OrderEnum.ServiceItem.HZQD_BGD_E_SW.getCode());
        list.add(OrderEnum.ServiceItem.HZQD_BGD_I.getCode());
        list.add(OrderEnum.ServiceItem.HZQD_BGD_E.getCode());

        // 202106新增
        list.add(OrderEnum.ServiceItem.HZQD_BGD_JKJQ_DP.getCode());
        list.add(OrderEnum.ServiceItem.HZQD_BGD_JKJQ_PD.getCode());
        list.add(OrderEnum.ServiceItem.HZQD_BGD_I_YX_PP.getCode());
        list.add(OrderEnum.ServiceItem.HZQD_BGD_I_YX_DD.getCode());
        list.add(OrderEnum.ServiceItem.HZQD_BGD_I_YX_DP.getCode());
        list.add(OrderEnum.ServiceItem.HZQD_BGD_I_YX_PD.getCode());
        list.add(OrderEnum.ServiceItem.HZQD_BGD_I_EX_PP.getCode());
        list.add(OrderEnum.ServiceItem.HZQD_BGD_I_EX_DD.getCode());
        list.add(OrderEnum.ServiceItem.HZQD_BGD_I_EX_DP.getCode());
        list.add(OrderEnum.ServiceItem.HZQD_BGD_I_EX_PD.getCode());
        list.add(OrderEnum.ServiceItem.HZQD_BGD_I_QN_PP.getCode());
        list.add(OrderEnum.ServiceItem.HZQD_BGD_I_QN_DD.getCode());
        list.add(OrderEnum.ServiceItem.HZQD_BGD_I_QN_PD.getCode());
        list.add(OrderEnum.ServiceItem.HZQD_BGD_I_QN_DP.getCode());
        list.add(OrderEnum.ServiceItem.HZQD_BGD_CKCQ_DP.getCode());
        list.add(OrderEnum.ServiceItem.HZQD_BGD_CKCQ_PD.getCode());
        list.add(OrderEnum.ServiceItem.HZQD_BGD_E_YX_PP.getCode());
        list.add(OrderEnum.ServiceItem.HZQD_BGD_E_YX_DD.getCode());
        list.add(OrderEnum.ServiceItem.HZQD_BGD_E_YX_DP.getCode());
        list.add(OrderEnum.ServiceItem.HZQD_BGD_E_YX_PD.getCode());
        list.add(OrderEnum.ServiceItem.HZQD_BGD_E_EX_PP.getCode());
        list.add(OrderEnum.ServiceItem.HZQD_BGD_E_EX_DD.getCode());
        list.add(OrderEnum.ServiceItem.HZQD_BGD_E_EX_DP.getCode());
        list.add(OrderEnum.ServiceItem.HZQD_BGD_E_EX_PD.getCode());

        return list;
    }

    /// <summary>
    /// 海运服务项目
    /// </summary>
    /// <param name="fwxmCode"></param>
    /// <returns></returns>
    public static boolean isTmsHyFwxm(String fwxmCode) {
        List<String> listFwxm = new ArrayList<>();
        listFwxm.add("100200320");
        listFwxm.add("100200310");
        return listFwxm.contains(fwxmCode);
    }

    /// <summary>
    /// 路运服务项目
    /// </summary>
    /// <param name="fwxmCode"></param>
    /// <returns></returns>
    public static boolean isTmsLyFwxm(String fwxmCode) {
        List<String> listFwxm = new ArrayList<>();
        listFwxm.add("100200120");
        listFwxm.add("100200120");
        return listFwxm.contains(fwxmCode);
    }

    /// <summary>
    /// 空运服务项目
    /// </summary>
    /// <param name="fwxmCode"></param>
    /// <returns></returns>
    public static boolean isTmsKyFwxm(String fwxmCode) {
        List<String> listFwxm = new ArrayList<>();
        listFwxm.add("100200420");
        listFwxm.add("100200410");
        return listFwxm.contains(fwxmCode);
    }

    /// <summary>
    /// 铁路服务项目
    /// </summary>
    /// <param name="fwxmCode"></param>
    /// <returns></returns>
    public static boolean isTmsTlFwxm(String fwxmCode) {
        List<String> listFwxm = new ArrayList<>();
        return listFwxm.contains(fwxmCode);
    }


    public static String getFeedbackDocStatus(String type) {
        switch (type) {
            case "ZYWC": // 作业完成
                return OrderEnum.DocStatus.OP_COMPLETE_OK.getCode();// 作业完成01
            case "ZYSJQQ": //作业数据齐全
                return OrderEnum.DocStatus.DATA_OK.getCode();// 作业数据齐全02
            default:
                throw new BaseException("反馈操作类型不正确！");
        }
    }
}
