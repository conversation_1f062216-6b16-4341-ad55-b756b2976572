package com.eci.common.cache.adapter;

import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;

/**
 * 统一缓存适配器接口
 * 支持本地缓存和 Redis 缓存的无缝切换
 * 
 * @param <K> 缓存键类型
 * @param <V> 缓存值类型
 */
public interface CacheAdapter<K, V> {

    /**
     * 获取缓存值
     * 
     * @param key 缓存键
     * @return 缓存值，如果不存在或已过期则返回 null
     */
    V get(K key);

    /**
     * 获取缓存值，如果不存在则使用 loader 加载并缓存
     * 
     * @param key 缓存键
     * @param loader 值加载器
     * @return 缓存值
     */
    V get(K key, Function<K, V> loader);

    /**
     * 获取缓存值，如果不存在则使用 loader 加载并缓存，支持自定义过期时间
     * 
     * @param key 缓存键
     * @param loader 值加载器
     * @param expireTime 过期时间
     * @param timeUnit 时间单位
     * @return 缓存值
     */
    V get(K key, Function<K, V> loader, long expireTime, TimeUnit timeUnit);

    /**
     * 设置缓存值，使用默认过期时间
     * 
     * @param key 缓存键
     * @param value 缓存值
     */
    void put(K key, V value);

    /**
     * 设置缓存值，指定过期时间
     * 
     * @param key 缓存键
     * @param value 缓存值
     * @param expireTime 过期时间
     * @param timeUnit 时间单位
     */
    void put(K key, V value, long expireTime, TimeUnit timeUnit);

    /**
     * 设置缓存值，永不过期
     * 
     * @param key 缓存键
     * @param value 缓存值
     */
    void putPermanent(K key, V value);

    /**
     * 批量设置缓存值
     * 
     * @param map 键值对映射
     */
    void putAll(Map<K, V> map);

    /**
     * 批量设置缓存值，指定过期时间
     * 
     * @param map 键值对映射
     * @param expireTime 过期时间
     * @param timeUnit 时间单位
     */
    void putAll(Map<K, V> map, long expireTime, TimeUnit timeUnit);

    /**
     * 检查缓存键是否存在
     * 
     * @param key 缓存键
     * @return 是否存在
     */
    boolean containsKey(K key);

    /**
     * 删除缓存项
     * 
     * @param key 缓存键
     * @return 是否删除成功
     */
    boolean remove(K key);

    /**
     * 批量删除缓存项
     * 
     * @param keys 缓存键集合
     * @return 实际删除的数量
     */
    long remove(Set<K> keys);

    /**
     * 清空所有缓存
     */
    void clear();

    /**
     * 获取缓存大小
     * 
     * @return 缓存项数量
     */
    long size();

    /**
     * 获取所有缓存键
     * 
     * @return 缓存键集合
     */
    Set<K> keySet();

    /**
     * 设置缓存项的过期时间
     * 
     * @param key 缓存键
     * @param expireTime 过期时间
     * @param timeUnit 时间单位
     * @return 是否设置成功
     */
    boolean expire(K key, long expireTime, TimeUnit timeUnit);

    /**
     * 获取缓存项的剩余过期时间
     * 
     * @param key 缓存键
     * @param timeUnit 时间单位
     * @return 剩余过期时间，-1 表示永不过期，-2 表示键不存在
     */
    long getExpire(K key, TimeUnit timeUnit);

    /**
     * 刷新缓存项（重新设置过期时间）
     * 
     * @param key 缓存键
     * @return 是否刷新成功
     */
    boolean refresh(K key);

    /**
     * 获取缓存统计信息
     * 
     * @return 统计信息
     */
    CacheStats getStats();

    /**
     * 获取缓存类型
     * 
     * @return 缓存类型
     */
    CacheType getType();

    /**
     * 检查缓存是否可用
     * 
     * @return 是否可用
     */
    boolean isAvailable();

    /**
     * 关闭缓存适配器，释放资源
     */
    void shutdown();

    /**
     * 缓存类型枚举
     */
    enum CacheType {
        LOCAL("本地缓存"),
        REDIS("Redis缓存"),
        HYBRID("混合缓存");

        private final String description;

        CacheType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 缓存统计信息
     */
    class CacheStats {
        private final long hitCount;
        private final long missCount;
        private final long loadCount;
        private final long evictionCount;
        private final double hitRate;
        private final double missRate;
        private final long size;
        private final long maxSize;

        public CacheStats(long hitCount, long missCount, long loadCount, 
                         long evictionCount, long size, long maxSize) {
            this.hitCount = hitCount;
            this.missCount = missCount;
            this.loadCount = loadCount;
            this.evictionCount = evictionCount;
            this.size = size;
            this.maxSize = maxSize;
            
            long totalRequests = hitCount + missCount;
            this.hitRate = totalRequests == 0 ? 0.0 : (double) hitCount / totalRequests;
            this.missRate = totalRequests == 0 ? 0.0 : (double) missCount / totalRequests;
        }

        public long getHitCount() { return hitCount; }
        public long getMissCount() { return missCount; }
        public long getLoadCount() { return loadCount; }
        public long getEvictionCount() { return evictionCount; }
        public double getHitRate() { return hitRate; }
        public double getMissRate() { return missRate; }
        public long getSize() { return size; }
        public long getMaxSize() { return maxSize; }

        @Override
        public String toString() {
            return String.format(
                "CacheStats{hitCount=%d, missCount=%d, hitRate=%.2f%%, size=%d/%d, evictions=%d}",
                hitCount, missCount, hitRate * 100, size, maxSize, evictionCount
            );
        }
    }

    /**
     * 缓存异常
     */
    class CacheException extends RuntimeException {
        public CacheException(String message) {
            super(message);
        }

        public CacheException(String message, Throwable cause) {
            super(message, cause);
        }
    }

    /**
     * 缓存不可用异常
     */
    class CacheUnavailableException extends CacheException {
        public CacheUnavailableException(String message) {
            super(message);
        }

        public CacheUnavailableException(String message, Throwable cause) {
            super(message, cause);
        }
    }
}
