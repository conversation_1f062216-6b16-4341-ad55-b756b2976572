CREATE TABLE "sys_oper_log"
(
    "id"             int8 NOT NULL,
    "title"          varchar(100),
    "business_type"  int4,
    "oper_url"       varchar(255),
    "method"         varchar(255),
    "request_method" varchar(10),
    "oper_ip"        varchar(15),
    "oper_location"  varchar(100),
    "oper_status"    int4,
    "error_msg"      text,
    "oper_time"      timestamp(6),
    "oper_param"     text,
    "json_result"    text,
    "user_id"        varchar(64),
    "login_name"     varchar(64),
    "oper_os"        varchar(20),
    "browser"        varchar(50),
    "server"         varchar(20),
    CONSTRAINT "sys_oper_log_pkey" PRIMARY KEY ("id")
)
;
COMMENT ON COLUMN "sys_oper_log"."id" IS '主键id';
COMMENT ON COLUMN "sys_oper_log"."title" IS '操作模块';
COMMENT ON COLUMN "sys_oper_log"."business_type" IS '业务类型|0其它 1新增 2修改 3删除';
COMMENT ON COLUMN "sys_oper_log"."oper_url" IS '请求url';
COMMENT ON COLUMN "sys_oper_log"."method" IS '请求后端方法';
COMMENT ON COLUMN "sys_oper_log"."request_method" IS '请求方式';
COMMENT ON COLUMN "sys_oper_log"."oper_ip" IS '操作地址';
COMMENT ON COLUMN "sys_oper_log"."oper_location" IS '操作地点';
COMMENT ON COLUMN "sys_oper_log"."oper_status" IS '操作状态|0正常 1异常';
COMMENT ON COLUMN "sys_oper_log"."error_msg" IS '错误消息';
COMMENT ON COLUMN "sys_oper_log"."oper_time" IS '操作时间';
COMMENT ON COLUMN "sys_oper_log"."oper_param" IS '请求参数';
COMMENT ON COLUMN "sys_oper_log"."json_result" IS '返回响应体';
COMMENT ON COLUMN "sys_oper_log"."user_id" IS '用户id';
COMMENT ON COLUMN "sys_oper_log"."login_name" IS '用户名';
COMMENT ON COLUMN "sys_oper_log"."oper_os" IS '操作系统';
COMMENT ON COLUMN "sys_oper_log"."browser" IS '浏览器';
COMMENT ON COLUMN "sys_oper_log"."server" IS '本地服务器';
COMMENT ON TABLE "sys_oper_log" IS '操作日志记录表';

CREATE TABLE "SYS_DATA_HELP"(
    "ID" NUMBER(20),
    "QUERY_KEY" VARCHAR2(200) DEFAULT NULL,
    "QUERY_TYPE" VARCHAR2(1) DEFAULT NULL,
    "SQL_COMMAND" VARCHAR2(2000) DEFAULT NULL,
    "BASE_COMMENT" VARCHAR2(800) DEFAULT NULL,
    "SYS" VARCHAR2(30) DEFAULT NULL,
    "CREATE_TIME" date DEFAULT NULL,
    "UPDATE_TIME" date DEFAULT NULL,
    "USE_CACHE" VARCHAR2(10) DEFAULT 'N',
    "LANGUAGE_TYPE" VARCHAR2(10) DEFAULT NULL,
    "DATA_CHANGE_CODE" VARCHAR2(50) DEFAULT NULL,
    "DATA_CHANGE_NAME" VARCHAR2(50) DEFAULT NULL,
    "TG_SORT_FIELD" VARCHAR2(50) DEFAULT NULL,
    "CREATE_USER" VARCHAR2(50) DEFAULT NULL,
    "UPDATE_USER" VARCHAR2(50) DEFAULT NULL,
    "CODE_MEMO" VARCHAR2(50) DEFAULT NULL,
    "NAME_MEMO" VARCHAR2(50) DEFAULT NULL,
    "COMPARE_TYPE" VARCHAR2(20) DEFAULT NULL,
    "CHOOSE_SHOW" VARCHAR2(2000) DEFAULT NULL,
    "TG_QUERY_FIELD" VARCHAR2(100) DEFAULT NULL,
    "AUTO_UPPER" VARCHAR2(1) DEFAULT NULL,
    "QUERY_MODE" VARCHAR2(20) DEFAULT NULL,
    "STATUS" VARCHAR2(2) DEFAULT NULL,
    "ECI_LOCK" VARCHAR2(1) DEFAULT NULL,
    "CONVERT_SQL" VARCHAR2(2000) DEFAULT NULL,
    "PL_VERSION" VARCHAR2(200) DEFAULT NULL,
    "NEED_DOWNLOAD" VARCHAR2(1) DEFAULT NULL,
    "MEMO_DETAIL" VARCHAR2(800) DEFAULT NULL,
    "ADMIN_LOCK" VARCHAR2(1) DEFAULT NULL,
    "ASSEMBLY_NAME" VARCHAR2(400) DEFAULT NULL,
    "FILTER" VARCHAR2(1) DEFAULT NULL,
    "LAYOUT" VARCHAR2(200) DEFAULT NULL,
    "EDIT_URL" VARCHAR2(255) DEFAULT NULL,
    "CONFIG" VARCHAR2(255) DEFAULT NULL,
    "WIDTH" VARCHAR2(20) DEFAULT NULL,
    "HEIGHT" VARCHAR2(20) DEFAULT NULL,
    "QUERY_DATA" VARCHAR2(255) DEFAULT NULL,
    "TG_PAGE_SIZE" VARCHAR2(20) DEFAULT NULL,
    PRIMARY KEY ("ID")
);
COMMENT ON TABLE SYS_DATA_HELP is '基础参数维护';
COMMENT ON COLUMN SYS_DATA_HELP.ID is '自增主键';
COMMENT ON COLUMN SYS_DATA_HELP.QUERY_KEY is '查询key';
COMMENT ON COLUMN SYS_DATA_HELP.QUERY_TYPE is '查询类型1:下拉框 2:放大镜';
COMMENT ON COLUMN SYS_DATA_HELP.SQL_COMMAND is 'SQL';
COMMENT ON COLUMN SYS_DATA_HELP.BASE_COMMENT is '说明';
COMMENT ON COLUMN SYS_DATA_HELP.SYS is '使用系统';
COMMENT ON COLUMN SYS_DATA_HELP.CREATE_TIME is '创建时间';
COMMENT ON COLUMN SYS_DATA_HELP.UPDATE_TIME is '更新时间';
COMMENT ON COLUMN SYS_DATA_HELP.USE_CACHE is '缓存启用状态0不用1用';
COMMENT ON COLUMN SYS_DATA_HELP.LANGUAGE_TYPE is '语言类型';
COMMENT ON COLUMN SYS_DATA_HELP.DATA_CHANGE_CODE is '数据转换代码';
COMMENT ON COLUMN SYS_DATA_HELP.DATA_CHANGE_NAME is '数据转换名称';
COMMENT ON COLUMN SYS_DATA_HELP.TG_SORT_FIELD is '排序';
COMMENT ON COLUMN SYS_DATA_HELP.CREATE_USER is '创建人';
COMMENT ON COLUMN SYS_DATA_HELP.UPDATE_USER is '修改人';
COMMENT ON COLUMN SYS_DATA_HELP.CODE_MEMO is '代码描述';
COMMENT ON COLUMN SYS_DATA_HELP.NAME_MEMO is '名称描述';
COMMENT ON COLUMN SYS_DATA_HELP.COMPARE_TYPE is '查询方式 like eq';
COMMENT ON COLUMN SYS_DATA_HELP.CHOOSE_SHOW is '放大镜显示-----功能暂不实现';
COMMENT ON COLUMN SYS_DATA_HELP.TG_QUERY_FIELD is '查询列和conn配合使用-----功能暂不实现';
COMMENT ON COLUMN SYS_DATA_HELP.AUTO_UPPER is '自动转大写-----功能暂不实现';
COMMENT ON COLUMN SYS_DATA_HELP.QUERY_MODE is '查询模式-----功能暂不实现';
COMMENT ON COLUMN SYS_DATA_HELP.STATUS is '状态-----保留';
COMMENT ON COLUMN SYS_DATA_HELP.ECI_LOCK is '锁定状态';
COMMENT ON COLUMN SYS_DATA_HELP.CONVERT_SQL is '转换SQL';
COMMENT ON COLUMN SYS_DATA_HELP.PL_VERSION is '当前版本';
COMMENT ON COLUMN SYS_DATA_HELP.NEED_DOWNLOAD is '是否需要下载';
COMMENT ON COLUMN SYS_DATA_HELP.ADMIN_LOCK is '管理员锁定';
COMMENT ON COLUMN SYS_DATA_HELP.ASSEMBLY_NAME is '程序集扩展';
COMMENT ON COLUMN SYS_DATA_HELP.FILTER is '打开页面是否条件过滤';
COMMENT ON COLUMN SYS_DATA_HELP.LAYOUT is '布局';
COMMENT ON COLUMN SYS_DATA_HELP.EDIT_URL is '编辑地址';
COMMENT ON COLUMN SYS_DATA_HELP.CONFIG is '配置信息';
COMMENT ON COLUMN SYS_DATA_HELP.WIDTH is '宽度';
COMMENT ON COLUMN SYS_DATA_HELP.HEIGHT is '高度';
COMMENT ON COLUMN SYS_DATA_HELP.QUERY_DATA is '查询数据';
COMMENT ON COLUMN SYS_DATA_HELP.TG_PAGE_SIZE is '分页大小';

CREATE TABLE "sys_user_info"
(
    "id"                   int8         NOT NULL,
    "user_id"              varchar(50)  NOT NULL,
    "login_name"           varchar(30)  NOT NULL,
    "true_name"            varchar(30),
    "company_code"         varchar(10)  NOT NULL,
    "company_name"         varchar(255) NOT NULL,
    "cust_code"            varchar(4),
    "cust_name"            varchar(20),
    "user_nickname"        varchar(30),
    "user_img"             varchar(255) DEFAULT NULL::varchar,
    "user_sex"             varchar(1)   DEFAULT '0'::varchar,
    "login_count"          int4         DEFAULT 0,
    "login_time"           timestamp(6),
    "login_last_time"      timestamp(6),
    "client_ip"            varchar(30),
    "client_province"      varchar(30),
    "client_city"          varchar(30),
    "client_browser"       varchar(30),
    "client_os"            varchar(30),
    "user_token"           varchar(255),
    "bind_phone_no"        varchar(20),
    "web_side_type"        varchar(1)   DEFAULT '2'::varchar,
    "web_head_type"        varchar(1)   DEFAULT '3'::varchar,
    "web_theme"            varchar(10)  DEFAULT '#409eff'::varchar,
    "web_layout"           varchar(1)   DEFAULT '3'::varchar,
    "web_side_is_icon"     varchar(1)   DEFAULT '1'::varchar,
    "web_side_is_open"     varchar(1)   DEFAULT '1'::varchar,
    "web_is_tab"           varchar(1)   DEFAULT '1'::varchar,
    "web_tab_type"         varchar(1)   DEFAULT '1'::varchar,
    "mail_address"         varchar(255),
    "pwd_last_update_time" timestamp(6) DEFAULT CURRENT_TIMESTAMP,
    "dev"                  varchar(1)   DEFAULT '0'::varchar,
    CONSTRAINT "sys_user_info_pkey" PRIMARY KEY ("id")
);
COMMENT ON COLUMN "sys_user_info"."id" IS '自增列';
COMMENT ON COLUMN "sys_user_info"."user_id" IS '权限平台用户ID';
COMMENT ON COLUMN "sys_user_info"."login_name" IS '权限平台登录用户名';
COMMENT ON COLUMN "sys_user_info"."true_name" IS '权限平台真实姓名';
COMMENT ON COLUMN "sys_user_info"."company_code" IS '企业代码';
COMMENT ON COLUMN "sys_user_info"."company_name" IS '企业名称';
COMMENT ON COLUMN "sys_user_info"."cust_code" IS '关区代码';
COMMENT ON COLUMN "sys_user_info"."cust_name" IS '关区名称';
COMMENT ON COLUMN "sys_user_info"."user_nickname" IS '用户别名';
COMMENT ON COLUMN "sys_user_info"."user_img" IS '头像';
COMMENT ON COLUMN "sys_user_info"."user_sex" IS '性别(0-保密/1-男/2-女)';
COMMENT ON COLUMN "sys_user_info"."login_count" IS '登录次数';
COMMENT ON COLUMN "sys_user_info"."login_time" IS '本次登录时间';
COMMENT ON COLUMN "sys_user_info"."login_last_time" IS '上次登录时间';
COMMENT ON COLUMN "sys_user_info"."client_ip" IS '客户端IP';
COMMENT ON COLUMN "sys_user_info"."client_province" IS '客户端IP所在省份';
COMMENT ON COLUMN "sys_user_info"."client_city" IS '客户端IP所在城市';
COMMENT ON COLUMN "sys_user_info"."client_browser" IS '客户端浏览器';
COMMENT ON COLUMN "sys_user_info"."client_os" IS '客户端操作系统';
COMMENT ON COLUMN "sys_user_info"."user_token" IS 'USER_TOKEN';
COMMENT ON COLUMN "sys_user_info"."bind_phone_no" IS '绑定手机号';
COMMENT ON COLUMN "sys_user_info"."web_side_type" IS '侧边栏类型(1-栏式1/2-栏式2)';
COMMENT ON COLUMN "sys_user_info"."web_head_type" IS '顶部模式(1-白/2-黑/3-主题色)';
COMMENT ON COLUMN "sys_user_info"."web_theme" IS '主题颜色';
COMMENT ON COLUMN "sys_user_info"."web_layout" IS '布局模式(1-侧边/2-顶部/3-混合)';
COMMENT ON COLUMN "sys_user_info"."web_side_is_icon" IS '侧边栏彩色图标';
COMMENT ON COLUMN "sys_user_info"."web_side_is_open" IS '侧栏排它展开';
COMMENT ON COLUMN "sys_user_info"."web_is_tab" IS '启用标签页';
COMMENT ON COLUMN "sys_user_info"."web_tab_type" IS '标签显示风格(1-默认/2-圆点/3-卡片)';
COMMENT ON COLUMN "sys_user_info"."mail_address" IS '用户邮箱地址';
COMMENT ON COLUMN "sys_user_info"."pwd_last_update_time" IS '密码最后修改时间';
COMMENT ON COLUMN "sys_user_info"."dev" IS '是否运维|0否1是';
COMMENT ON TABLE "sys_user_info" IS '用户信息';

CREATE TABLE "sys_user_login_log"
(
    "id"                int8 NOT NULL,
    "user_id"           varchar(50),
    "login_name"        varchar(30),
    "sys_code"          varchar(30),
    "client_ip"         varchar(30),
    "client_province"   varchar(30),
    "client_city"       varchar(30),
    "client_browser"    varchar(30),
    "client_os"         varchar(30),
    "login_time"        timestamp(6),
    "login_status"      int4,
    "login_description" varchar(255),
    "action_params"     text,
    CONSTRAINT "sys_user_login_log_pkey" PRIMARY KEY ("id")
);
COMMENT ON COLUMN "sys_user_login_log"."id" IS '自增列(业务无关)';
COMMENT ON COLUMN "sys_user_login_log"."user_id" IS '权限平台用户ID';
COMMENT ON COLUMN "sys_user_login_log"."login_name" IS '权限平台登录用户名';
COMMENT ON COLUMN "sys_user_login_log"."sys_code" IS '系统代码';
COMMENT ON COLUMN "sys_user_login_log"."client_ip" IS '客户端IP';
COMMENT ON COLUMN "sys_user_login_log"."client_province" IS '客户端IP所在省份';
COMMENT ON COLUMN "sys_user_login_log"."client_city" IS '客户端IP所在城市';
COMMENT ON COLUMN "sys_user_login_log"."client_browser" IS '客户端浏览器';
COMMENT ON COLUMN "sys_user_login_log"."client_os" IS '客户端操作系统';
COMMENT ON COLUMN "sys_user_login_log"."login_time" IS '登录时间';
COMMENT ON COLUMN "sys_user_login_log"."login_status" IS '登录状态(1-成功/0-失败)';
COMMENT ON COLUMN "sys_user_login_log"."login_description" IS '登录失败原因';
COMMENT ON COLUMN "sys_user_login_log"."action_params" IS '请求参数';
COMMENT ON TABLE "sys_user_login_log" IS '用户登录日志';

CREATE TABLE "sys_enterprise_info"
(
    "id"           int8         NOT NULL,
    "company_code" varchar(10)  NOT NULL,
    "company_name" varchar(255) NOT NULL,
    "ep_sccd"      varchar(18),
    "cust_code"    varchar(4),
    "cust_name"    varchar(20),
    "contacts"     varchar(100),
    "contacts_tel" varchar(100),
    "mail_address" varchar(255),
    "ep_address"   varchar(255),
    "ep_logo_url"  varchar(255),
    "ep_sys_name"  varchar(255),
    "remark"       varchar(255),
    "create_time"  timestamp(6) DEFAULT CURRENT_TIMESTAMP,
    "update_time"  timestamp(6) DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "sys_enterprise_info_pkey" PRIMARY KEY ("id")
);
COMMENT ON COLUMN "sys_enterprise_info"."id" IS '自增列(业务无关)';
COMMENT ON COLUMN "sys_enterprise_info"."company_code" IS '企业编码';
COMMENT ON COLUMN "sys_enterprise_info"."company_name" IS '企业名称';
COMMENT ON COLUMN "sys_enterprise_info"."ep_sccd" IS '企业18位社会信用代码';
COMMENT ON COLUMN "sys_enterprise_info"."cust_code" IS '关区代码';
COMMENT ON COLUMN "sys_enterprise_info"."cust_name" IS '关区名称';
COMMENT ON COLUMN "sys_enterprise_info"."contacts" IS '联系人';
COMMENT ON COLUMN "sys_enterprise_info"."contacts_tel" IS '联系电话';
COMMENT ON COLUMN "sys_enterprise_info"."mail_address" IS '邮件地址';
COMMENT ON COLUMN "sys_enterprise_info"."ep_address" IS '企业地址';
COMMENT ON COLUMN "sys_enterprise_info"."ep_logo_url" IS '企业专属logoURL';
COMMENT ON COLUMN "sys_enterprise_info"."ep_sys_name" IS '企业专属系统名称';
COMMENT ON COLUMN "sys_enterprise_info"."remark" IS '备注';
COMMENT ON COLUMN "sys_enterprise_info"."create_time" IS '创建时间';
COMMENT ON COLUMN "sys_enterprise_info"."update_time" IS '修改时间';
COMMENT ON TABLE "sys_enterprise_info" IS '企业信息表';

CREATE TABLE "sys_table_setting"
(
    "id"           int8 NOT NULL,
    "user_id"      varchar(36),
    "setting_type" varchar(1),
    "table_code"   varchar(255),
    "json_detail"  text,
    "create_time"  timestamp(6),
    CONSTRAINT "sys_table_setting_pkey" PRIMARY KEY ("id")
);
COMMENT ON COLUMN "sys_table_setting"."id" IS '主键';
COMMENT ON COLUMN "sys_table_setting"."user_id" IS '用户id';
COMMENT ON COLUMN "sys_table_setting"."setting_type" IS '设置类型|1筛选项2表单项';
COMMENT ON COLUMN "sys_table_setting"."table_code" IS '表名';
COMMENT ON COLUMN "sys_table_setting"."json_detail" IS 'json明细';
COMMENT ON COLUMN "sys_table_setting"."create_time" IS '创建时间';
COMMENT ON TABLE "sys_table_setting" IS '页面列表设置信息';

CREATE TABLE "sys_quick_my_collection"
(
    "id"          int8 NOT NULL,
    "user_id"     varchar(50),
    "login_name"  varchar(30),
    "true_name"   varchar(50),
    "menu_id"     varchar(50),
    "menu_name"   varchar(100),
    "menu_route"  varchar(100),
    "remark"      varchar(255),
    "create_user" varchar(50),
    "create_time" timestamp(6) DEFAULT CURRENT_TIMESTAMP,
    "update_user" varchar(50),
    "update_time" timestamp(6) DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "sys_quick_my_collection_pkey" PRIMARY KEY ("id")
);
COMMENT ON COLUMN "sys_quick_my_collection"."id" IS '自增主键';
COMMENT ON COLUMN "sys_quick_my_collection"."user_id" IS '权限平台用户ID';
COMMENT ON COLUMN "sys_quick_my_collection"."login_name" IS '用户名(权限平台登录用户名)';
COMMENT ON COLUMN "sys_quick_my_collection"."true_name" IS '真实姓名';
COMMENT ON COLUMN "sys_quick_my_collection"."menu_id" IS '菜单编号';
COMMENT ON COLUMN "sys_quick_my_collection"."menu_name" IS '菜单名称';
COMMENT ON COLUMN "sys_quick_my_collection"."menu_route" IS '菜单路由地址';
COMMENT ON COLUMN "sys_quick_my_collection"."remark" IS '备注(图标)';
COMMENT ON COLUMN "sys_quick_my_collection"."create_user" IS '创建人';
COMMENT ON COLUMN "sys_quick_my_collection"."create_time" IS '创建时间';
COMMENT ON COLUMN "sys_quick_my_collection"."update_user" IS '修改人';
COMMENT ON COLUMN "sys_quick_my_collection"."update_time" IS '修改时间';

CREATE TABLE "SYS_CACHE_HELP" (
  "ID"  NUMBER(20),
  "QUERY_KEY" VARCHAR2(100) DEFAULT NULL,
  "STATUS" VARCHAR2(2) DEFAULT NULL,
  "SQL_COMMAND" VARCHAR2(2000) DEFAULT NULL,
  "BASE_COMMENT" VARCHAR2(255) DEFAULT NULL,
  "MEMO" VARCHAR2(400) DEFAULT NULL,
  "MEMO_DETAIL" VARCHAR2(400) DEFAULT NULL,
  "SYS" VARCHAR2(30) DEFAULT NULL,
  "LANGUAGE_TYPE" VARCHAR2(10) DEFAULT NULL,
  "CREATE_TIME" date DEFAULT CURRENT_TIMESTAMP,
  "UPDATE_TIME" date DEFAULT CURRENT_TIMESTAMP,
  "CREATE_USER" VARCHAR2(50)DEFAULT NULL,
  "UPDATE_USER" VARCHAR2(50) DEFAULT NULL,
  "PL_VERSION" VARCHAR2(100) DEFAULT NULL,
  "DB_VERSION" VARCHAR2(100) DEFAULT NULL,
  PRIMARY KEY ("ID")
);
COMMENT ON TABLE SYS_CACHE_HELP is '缓存参数维护';
COMMENT ON COLUMN SYS_CACHE_HELP.ID is '自增主键';
COMMENT ON COLUMN SYS_CACHE_HELP.QUERY_KEY is '查询key';
COMMENT ON COLUMN SYS_CACHE_HELP.STATUS is '状态-----保留';
COMMENT ON COLUMN SYS_CACHE_HELP.SQL_COMMAND is 'SQL';
COMMENT ON COLUMN SYS_CACHE_HELP.BASE_COMMENT is '备注';
COMMENT ON COLUMN SYS_CACHE_HELP.MEMO is '备注';
COMMENT ON COLUMN SYS_CACHE_HELP.MEMO_DETAIL is '备注2';
COMMENT ON COLUMN SYS_CACHE_HELP.SYS is '使用系统';
COMMENT ON COLUMN SYS_CACHE_HELP.LANGUAGE_TYPE is '语言类型';
COMMENT ON COLUMN SYS_CACHE_HELP.CREATE_TIME is '创建时间';
COMMENT ON COLUMN SYS_CACHE_HELP.UPDATE_TIME is '修改时间';
COMMENT ON COLUMN SYS_CACHE_HELP.CREATE_USER is '创建人';
COMMENT ON COLUMN SYS_CACHE_HELP.UPDATE_USER is '修改人';
COMMENT ON COLUMN SYS_CACHE_HELP.PL_VERSION is '当前缓存版本';
COMMENT ON COLUMN SYS_CACHE_HELP.DB_VERSION is '当前DB库版本';