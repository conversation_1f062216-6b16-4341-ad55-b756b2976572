package com.eci.common;

// 用于存储产品编码和操作类型的组合
public  class ZsrPair<K, V> {
    private K key;
    private V value;

    public ZsrPair(K key, V value) {
        this.key = key;
        this.value = value;
    }

    public K getKey() {
        return key;
    }

    public V getValue() {
        return value;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ZsrPair<?, ?> pair = (ZsrPair<?, ?>) o;
        return java.util.Objects.equals(key, pair.key) && java.util.Objects.equals(value, pair.value);
    }

    @Override
    public int hashCode() {
        return java.util.Objects.hash(key, value);
    }
}