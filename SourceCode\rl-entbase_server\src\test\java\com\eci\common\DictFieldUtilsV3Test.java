package com.eci.common;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import static org.mockito.Mockito.*;
import static org.junit.jupiter.api.Assertions.*;

import java.util.Arrays;
import java.util.List;

/**
 * DictFieldUtilsV3 测试类
 * 验证从 DictFieldUtils 迁移到 DictFieldUtilsV3 的功能正确性
 */
public class DictFieldUtilsV3Test {

    @Mock
    private com.eci.common.cache.factory.CacheFactory cacheFactory;

    private DictFieldUtilsV3 dictFieldUtilsV3;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        dictFieldUtilsV3 = new DictFieldUtilsV3();
        // 注入模拟的 CacheFactory
        // dictFieldUtilsV3.cacheFactory = cacheFactory;
    }

    @Test
    public void testHandleDictFieldsWithSingleEntity() {
        // 创建测试实体
        TestEntity entity = new TestEntity();
        entity.setCode("TEST001");
        entity.setName("测试实体");

        // 测试处理单个实体
        assertDoesNotThrow(() -> {
            dictFieldUtilsV3.handleDictFields(entity);
        });
    }

    @Test
    public void testHandleDictFieldsWithEntityList() {
        // 创建测试实体列表
        TestEntity entity1 = new TestEntity();
        entity1.setCode("TEST001");
        entity1.setName("测试实体1");

        TestEntity entity2 = new TestEntity();
        entity2.setCode("TEST002");
        entity2.setName("测试实体2");

        List<TestEntity> entities = Arrays.asList(entity1, entity2);

        // 测试处理实体列表
        assertDoesNotThrow(() -> {
            dictFieldUtilsV3.handleDictFields(entities);
        });
    }

    @Test
    public void testHandleDictFieldsWithEmptyList() {
        // 测试空列表
        List<TestEntity> emptyList = Arrays.asList();
        
        assertDoesNotThrow(() -> {
            dictFieldUtilsV3.handleDictFields(emptyList);
        });
    }

    @Test
    public void testHandleDictFieldsWithNullEntity() {
        // 测试null实体
        TestEntity nullEntity = null;
        
        assertDoesNotThrow(() -> {
            dictFieldUtilsV3.handleDictFields(nullEntity);
        });
    }

    @Test
    public void testProcessCodeNameWithTypeConversionError() {
        // 测试类型转换错误的修复
        TestEntity entity = new TestEntity();
        entity.setIsQrjd("1");
        
        // 模拟包含JSONObject的缓存数据
        Map<String, CodeNameCommon> codeMap = new HashMap<>();
        codeMap.put("1", new CodeNameCommon("1", "是"));
        
        // 使用反射测试processCodeName方法
        try {
            Field field = TestEntity.class.getDeclaredField("isQrjd");
            DictField dictField = mock(DictField.class);
            when(dictField.suffix()).thenReturn("Name");
            
            // 使用反射调用私有方法
            java.lang.reflect.Method processCodeNameMethod = DictFieldUtilsV3.class
                    .getDeclaredMethod("processCodeName", Object.class, Field.class, Object.class, 
                                     Map.class, DictField.class);
            processCodeNameMethod.setAccessible(true);
            
            processCodeNameMethod.invoke(dictFieldUtilsV3, entity, field, "1", codeMap, dictField);
            
            // 验证结果
            assertEquals("是", entity.getIsQrjdName());
        } catch (Exception e) {
            fail("测试失败：" + e.getMessage());
        }
    }

    @Test
    public void testValidateAndConvertCodeMap() {
        // 测试数据验证和转换方法
        Map<String, CodeNameCommon> originalMap = new HashMap<>();
        originalMap.put("1", new CodeNameCommon("1", "是"));
        
        try {
            java.lang.reflect.Method validateMethod = DictFieldUtilsV3.class
                    .getDeclaredMethod("validateAndConvertCodeMap", Map.class);
            validateMethod.setAccessible(true);
            
            @SuppressWarnings("unchecked")
            Map<String, CodeNameCommon> result = (Map<String, CodeNameCommon>) 
                    validateMethod.invoke(dictFieldUtilsV3, originalMap);
            
            // 验证结果
            assertNotNull(result);
            assertEquals(1, result.size());
            assertTrue(result.containsKey("1"));
            assertEquals("是", result.get("1").getName());
        } catch (Exception e) {
            fail("测试失败：" + e.getMessage());
        }
    }

    /**
     * 测试实体类
     */
    public static class TestEntity {
        private String code;
        private String name;

        @DictField(queryKey = "TEST_DICT")
        private String status;

        @DictField(data = {
            "{\"code\":\"1\",\"name\":\"启用\"}",
            "{\"code\":\"0\",\"name\":\"禁用\"}"
        })
        private String enabled;

        @DictField(queryKey = "QRJD_DICT")
        private String isQrjd;
        private String isQrjdName;

        // Getters and Setters
        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public String getEnabled() {
            return enabled;
        }

        public void setEnabled(String enabled) {
            this.enabled = enabled;
        }

        public String getIsQrjd() {
            return isQrjd;
        }

        public void setIsQrjd(String isQrjd) {
            this.isQrjd = isQrjd;
        }

        public String getIsQrjdName() {
            return isQrjdName;
        }

        public void setIsQrjdName(String isQrjdName) {
            this.isQrjdName = isQrjdName;
        }
    }
}
