CREATE TABLE "SYS_OPER_LOG"(
 "ID" NUMBER(20),
 "TITLE" VARCHAR2(100) DEFAULT NULL ,
 "BUSINESS_TYPE" NUMBER(4) DEFAULT NULL,
 "OPER_URL" VARCHAR2(255)  DEFAULT NULL,
 "METHOD" VARCHAR2(255)  DEFAULT NULL,
 "REQUEST_METHOD" VARCHAR2(10)  DEFAULT NULL,
 "OPER_IP" VARCHAR2(15)  DEFAULT NULL,
 "OPER_LOCATION" VARCHAR2(100)  DEFAULT NULL,
 "OPER_STATUS" NUMBER(4) DEFAULT NULL,
 "ERROR_MSG" clob  DEFAULT NULL,
 "OPER_TIME" date DEFAULT NULL,
 "OPER_PARAM" clob DEFAULT NULL,
 "JSON_RESULT" clob DEFAULT NULL,
 "USER_ID" VARCHAR2(64)  DEFAULT NULL,
 "<PERSON>O<PERSON><PERSON>_NAME" VARCHAR2(64)  DEFAULT NULL,
 "OPER_OS" VARCHAR2(20)  DEFAULT NULL,
 "BROWSER" VARCHAR2(50)  DEFAULT NULL,
 "SERVER" VARCHAR2(200)  DEFAULT NULL,
 PRIMARY KEY ("ID")
);
COMMENT ON TABLE SYS_OPER_LOG is '操作日志';
COMMENT ON COLUMN SYS_OPER_LOG.ID is '主键ID';
COMMENT ON COLUMN SYS_OPER_LOG.TITLE is '操作模块';
COMMENT ON COLUMN SYS_OPER_LOG.BUSINESS_TYPE is '业务类型|0其它 1新增 2修改 3删除';
COMMENT ON COLUMN SYS_OPER_LOG.OPER_URL is '请求url';
COMMENT ON COLUMN SYS_OPER_LOG.METHOD is '请求后端方法';
COMMENT ON COLUMN SYS_OPER_LOG.REQUEST_METHOD is '请求方式';
COMMENT ON COLUMN SYS_OPER_LOG.OPER_IP is '操作地址';
COMMENT ON COLUMN SYS_OPER_LOG.OPER_LOCATION is '操作地点';
COMMENT ON COLUMN SYS_OPER_LOG.OPER_STATUS is '操作状态|0正常 1异常';
COMMENT ON COLUMN SYS_OPER_LOG.ERROR_MSG is '错误消息';
COMMENT ON COLUMN SYS_OPER_LOG.OPER_TIME is '操作时间';
COMMENT ON COLUMN SYS_OPER_LOG.OPER_PARAM is '请求参数';
COMMENT ON COLUMN SYS_OPER_LOG.JSON_RESULT is '返回响应体';
COMMENT ON COLUMN SYS_OPER_LOG.USER_ID is '用户id';
COMMENT ON COLUMN SYS_OPER_LOG.LOGIN_NAME is '用户名';
COMMENT ON COLUMN SYS_OPER_LOG.OPER_OS is '操作系统';
COMMENT ON COLUMN SYS_OPER_LOG.BROWSER is '浏览器';
COMMENT ON COLUMN SYS_OPER_LOG.SERVER is '本地服务器';

CREATE TABLE "SYS_DATA_HELP"(
 "ID" NUMBER(20),
 "QUERY_KEY" VARCHAR2(200) DEFAULT NULL,
 "QUERY_TYPE" VARCHAR2(1) DEFAULT NULL,
 "SQL_COMMAND" VARCHAR2(2000) DEFAULT NULL,
 "BASE_COMMENT" VARCHAR2(800) DEFAULT NULL,
 "SYS" VARCHAR2(30) DEFAULT NULL,
 "CREATE_TIME" date DEFAULT NULL,
 "UPDATE_TIME" date DEFAULT NULL,
 "USE_CACHE" VARCHAR2(10) DEFAULT 'N',
 "LANGUAGE_TYPE" VARCHAR2(10) DEFAULT NULL,
 "DATA_CHANGE_CODE" VARCHAR2(50) DEFAULT NULL,
 "DATA_CHANGE_NAME" VARCHAR2(50) DEFAULT NULL,
 "TG_SORT_FIELD" VARCHAR2(50) DEFAULT NULL,
 "CREATE_USER" VARCHAR2(50) DEFAULT NULL,
 "UPDATE_USER" VARCHAR2(50) DEFAULT NULL,
 "CODE_MEMO" VARCHAR2(50) DEFAULT NULL,
 "NAME_MEMO" VARCHAR2(50) DEFAULT NULL,
 "COMPARE_TYPE" VARCHAR2(20) DEFAULT NULL,
 "CHOOSE_SHOW" VARCHAR2(2000) DEFAULT NULL,
 "TG_QUERY_FIELD" VARCHAR2(100) DEFAULT NULL,
 "AUTO_UPPER" VARCHAR2(1) DEFAULT NULL,
 "QUERY_MODE" VARCHAR2(20) DEFAULT NULL,
 "STATUS" VARCHAR2(2) DEFAULT NULL,
 "ECI_LOCK" VARCHAR2(1) DEFAULT NULL,
 "CONVERT_SQL" VARCHAR2(2000) DEFAULT NULL,
 "PL_VERSION" VARCHAR2(200) DEFAULT NULL,
 "NEED_DOWNLOAD" VARCHAR2(1) DEFAULT NULL,
 "MEMO_DETAIL" VARCHAR2(800) DEFAULT NULL,
 "ADMIN_LOCK" VARCHAR2(1) DEFAULT NULL,
 "ASSEMBLY_NAME" VARCHAR2(400) DEFAULT NULL,
 "FILTER" VARCHAR2(1) DEFAULT NULL,
 "LAYOUT" VARCHAR2(200) DEFAULT NULL,
 "EDIT_URL" VARCHAR2(255) DEFAULT NULL,
 "CONFIG" VARCHAR2(255) DEFAULT NULL,
 "WIDTH" VARCHAR2(20) DEFAULT NULL,
 "HEIGHT" VARCHAR2(20) DEFAULT NULL,
 "QUERY_DATA" VARCHAR2(255) DEFAULT NULL,
 "TG_PAGE_SIZE" VARCHAR2(20) DEFAULT NULL,
 PRIMARY KEY ("ID")
);
COMMENT ON TABLE SYS_DATA_HELP is '基础参数维护';
COMMENT ON COLUMN SYS_DATA_HELP.ID is '自增主键';
COMMENT ON COLUMN SYS_DATA_HELP.QUERY_KEY is '查询key';
COMMENT ON COLUMN SYS_DATA_HELP.QUERY_TYPE is '查询类型1:下拉框 2:放大镜';
COMMENT ON COLUMN SYS_DATA_HELP.SQL_COMMAND is 'SQL';
COMMENT ON COLUMN SYS_DATA_HELP.BASE_COMMENT is '说明';
COMMENT ON COLUMN SYS_DATA_HELP.SYS is '使用系统';
COMMENT ON COLUMN SYS_DATA_HELP.CREATE_TIME is '创建时间';
COMMENT ON COLUMN SYS_DATA_HELP.UPDATE_TIME is '更新时间';
COMMENT ON COLUMN SYS_DATA_HELP.USE_CACHE is '缓存启用状态0不用1用';
COMMENT ON COLUMN SYS_DATA_HELP.LANGUAGE_TYPE is '语言类型';
COMMENT ON COLUMN SYS_DATA_HELP.DATA_CHANGE_CODE is '数据转换代码';
COMMENT ON COLUMN SYS_DATA_HELP.DATA_CHANGE_NAME is '数据转换名称';
COMMENT ON COLUMN SYS_DATA_HELP.TG_SORT_FIELD is '排序';
COMMENT ON COLUMN SYS_DATA_HELP.CREATE_USER is '创建人';
COMMENT ON COLUMN SYS_DATA_HELP.UPDATE_USER is '修改人';
COMMENT ON COLUMN SYS_DATA_HELP.CODE_MEMO is '代码描述';
COMMENT ON COLUMN SYS_DATA_HELP.NAME_MEMO is '名称描述';
COMMENT ON COLUMN SYS_DATA_HELP.COMPARE_TYPE is '查询方式 like eq';
COMMENT ON COLUMN SYS_DATA_HELP.CHOOSE_SHOW is '放大镜显示-----功能暂不实现';
COMMENT ON COLUMN SYS_DATA_HELP.TG_QUERY_FIELD is '查询列和conn配合使用-----功能暂不实现';
COMMENT ON COLUMN SYS_DATA_HELP.AUTO_UPPER is '自动转大写-----功能暂不实现';
COMMENT ON COLUMN SYS_DATA_HELP.QUERY_MODE is '查询模式-----功能暂不实现';
COMMENT ON COLUMN SYS_DATA_HELP.STATUS is '状态-----保留';
COMMENT ON COLUMN SYS_DATA_HELP.ECI_LOCK is '锁定状态';
COMMENT ON COLUMN SYS_DATA_HELP.CONVERT_SQL is '转换SQL';
COMMENT ON COLUMN SYS_DATA_HELP.PL_VERSION is '当前版本';
COMMENT ON COLUMN SYS_DATA_HELP.NEED_DOWNLOAD is '是否需要下载';
COMMENT ON COLUMN SYS_DATA_HELP.ADMIN_LOCK is '管理员锁定';
COMMENT ON COLUMN SYS_DATA_HELP.ASSEMBLY_NAME is '程序集扩展';
COMMENT ON COLUMN SYS_DATA_HELP.FILTER is '打开页面是否条件过滤';
COMMENT ON COLUMN SYS_DATA_HELP.LAYOUT is '布局';
COMMENT ON COLUMN SYS_DATA_HELP.EDIT_URL is '编辑地址';
COMMENT ON COLUMN SYS_DATA_HELP.CONFIG is '配置信息';
COMMENT ON COLUMN SYS_DATA_HELP.WIDTH is '宽度';
COMMENT ON COLUMN SYS_DATA_HELP.HEIGHT is '高度';
COMMENT ON COLUMN SYS_DATA_HELP.QUERY_DATA is '查询数据';
COMMENT ON COLUMN SYS_DATA_HELP.TG_PAGE_SIZE is '分页大小';

CREATE TABLE "SYS_USER_INFO" (
 "ID" NUMBER(20),
 "USER_ID" VARCHAR2(50) DEFAULT NULL ,
 "LOGIN_NAME" VARCHAR2(30) DEFAULT NULL ,
 "TRUE_NAME" VARCHAR2(30) DEFAULT NULL ,
 "COMPANY_CODE" VARCHAR2(20) DEFAULT NULL ,
 "COMPANY_NAME" VARCHAR2(255) DEFAULT NULL ,
 "CUST_CODE" VARCHAR2(4) DEFAULT NULL ,
 "CUST_NAME" VARCHAR2(20) DEFAULT NULL ,
 "USER_NICKNAME" VARCHAR2(30) DEFAULT NULL ,
 "USER_IMG" VARCHAR2(255) DEFAULT NULL ,
 "USER_SEX" VARCHAR2(1) DEFAULT '0' ,
 "LOGIN_COUNT" int DEFAULT '0' ,
 "LOGIN_TIME" date DEFAULT NULL ,
 "LOGIN_LAST_TIME" date DEFAULT NULL ,
 "CLIENT_IP" VARCHAR2(30) DEFAULT NULL ,
 "CLIENT_PROVINCE" VARCHAR2(30) DEFAULT NULL ,
 "CLIENT_CITY" VARCHAR2(30) DEFAULT NULL ,
 "CLIENT_BROWSER" VARCHAR2(30) DEFAULT NULL ,
 "CLIENT_OS" VARCHAR2(30) DEFAULT NULL ,
 "USER_TOKEN" VARCHAR2(255) DEFAULT NULL ,
 "BIND_PHONE_NO" VARCHAR2(20) DEFAULT NULL ,
 "WEB_SIDE_TYPE" VARCHAR2(1) DEFAULT '2' ,
 "WEB_HEAD_TYPE" VARCHAR2(1) DEFAULT '3' ,
 "WEB_THEME" VARCHAR2(10) DEFAULT '#409eff' ,
 "WEB_LAYOUT" VARCHAR2(1) DEFAULT '3' ,
 "WEB_SIDE_IS_ICON" VARCHAR2(1) DEFAULT '1' ,
 "WEB_SIDE_IS_OPEN" VARCHAR2(1) DEFAULT '1' ,
 "WEB_IS_TAB" VARCHAR2(1) DEFAULT '1' ,
 "WEB_TAB_TYPE" VARCHAR2(1) DEFAULT '1' ,
 "MAIL_ADDRESS" VARCHAR2(255) DEFAULT NULL ,
 "PWD_LAST_UPDATE_TIME" date DEFAULT CURRENT_TIMESTAMP ,
 "DEV" VARCHAR2(1) DEFAULT '0',
 PRIMARY KEY ("ID")
);
COMMENT ON TABLE SYS_USER_INFO is '用户信息';
COMMENT ON COLUMN SYS_USER_INFO.ID is '自增列';
COMMENT ON COLUMN SYS_USER_INFO.USER_ID is '权限平台用户ID';
COMMENT ON COLUMN SYS_USER_INFO.LOGIN_NAME is '权限平台登录用户名';
COMMENT ON COLUMN SYS_USER_INFO.TRUE_NAME is '权限平台真实姓名';
COMMENT ON COLUMN SYS_USER_INFO.COMPANY_CODE is '企业代码';
COMMENT ON COLUMN SYS_USER_INFO.COMPANY_NAME is '企业名称';
COMMENT ON COLUMN SYS_USER_INFO.CUST_CODE is '关区代码';
COMMENT ON COLUMN SYS_USER_INFO.CUST_NAME is '关区名称';
COMMENT ON COLUMN SYS_USER_INFO.USER_NICKNAME is '用户别名';
COMMENT ON COLUMN SYS_USER_INFO.USER_IMG is '头像';
COMMENT ON COLUMN SYS_USER_INFO.USER_SEX is '性别(0-保密/1-男/2-女)';
COMMENT ON COLUMN SYS_USER_INFO.LOGIN_COUNT is '登录次数';
COMMENT ON COLUMN SYS_USER_INFO.LOGIN_TIME is '本次登录时间';
COMMENT ON COLUMN SYS_USER_INFO.LOGIN_LAST_TIME is '上次登录时间';
COMMENT ON COLUMN SYS_USER_INFO.CLIENT_IP is '客户端IP';
COMMENT ON COLUMN SYS_USER_INFO.CLIENT_PROVINCE is '客户端IP所在省份';
COMMENT ON COLUMN SYS_USER_INFO.CLIENT_CITY is '客户端IP所在城市';
COMMENT ON COLUMN SYS_USER_INFO.CLIENT_BROWSER is '客户端浏览器';
COMMENT ON COLUMN SYS_USER_INFO.CLIENT_OS is '客户端操作系统';
COMMENT ON COLUMN SYS_USER_INFO.USER_TOKEN is 'USER_TOKEN';
COMMENT ON COLUMN SYS_USER_INFO.BIND_PHONE_NO is '绑定手机号';
COMMENT ON COLUMN SYS_USER_INFO.WEB_SIDE_TYPE is '侧边栏类型(1-栏式1/2-栏式2)';
COMMENT ON COLUMN SYS_USER_INFO.WEB_HEAD_TYPE is '顶部模式(1-白/2-黑/3-主题色)';
COMMENT ON COLUMN SYS_USER_INFO.WEB_THEME is '主题颜色';
COMMENT ON COLUMN SYS_USER_INFO.WEB_LAYOUT is '布局模式(1-侧边/2-顶部/3-混合)';
COMMENT ON COLUMN SYS_USER_INFO.WEB_SIDE_IS_ICON is '侧边栏彩色图标';
COMMENT ON COLUMN SYS_USER_INFO.WEB_SIDE_IS_OPEN is '侧栏排它展开';
COMMENT ON COLUMN SYS_USER_INFO.WEB_IS_TAB is '启用标签页';
COMMENT ON COLUMN SYS_USER_INFO.WEB_TAB_TYPE is '标签显示风格(1-默认/2-圆点/3-卡片)';
COMMENT ON COLUMN SYS_USER_INFO.MAIL_ADDRESS is '用户邮箱地址';
COMMENT ON COLUMN SYS_USER_INFO.PWD_LAST_UPDATE_TIME is '密码最后修改时间';
COMMENT ON COLUMN SYS_USER_INFO.DEV is '是否运维|0否1是';

CREATE TABLE "SYS_USER_LOGIN_LOG" (
 "ID" NUMBER(20),
 "USER_ID" VARCHAR2(50) DEFAULT NULL,
 "LOGIN_NAME" VARCHAR2(30) DEFAULT NULL,
 "SYS_CODE" VARCHAR2(30) DEFAULT NULL,
 "CLIENT_IP" VARCHAR2(30) DEFAULT NULL,
 "CLIENT_PROVINCE" VARCHAR2(30) DEFAULT NULL,
 "CLIENT_CITY" VARCHAR2(30) DEFAULT NULL,
 "CLIENT_BROWSER" VARCHAR2(30) DEFAULT NULL,
 "CLIENT_OS" VARCHAR2(30) DEFAULT NULL,
 "LOGIN_TIME" date DEFAULT NULL,
 "LOGIN_STATUS" NUMBER(2) DEFAULT NULL,
 "LOGIN_DESCRIPTION" VARCHAR2(255) DEFAULT NULL,
 "ACTION_PARAMS" clob  DEFAULT NULL,
 PRIMARY KEY ("ID")
);
COMMENT ON TABLE SYS_USER_LOGIN_LOG is '用户登录日志';
COMMENT ON COLUMN SYS_USER_LOGIN_LOG.ID is '自增列(业务无关)';
COMMENT ON COLUMN SYS_USER_LOGIN_LOG.USER_ID is '权限平台用户ID';
COMMENT ON COLUMN SYS_USER_LOGIN_LOG.LOGIN_NAME is '权限平台登录用户名';
COMMENT ON COLUMN SYS_USER_LOGIN_LOG.SYS_CODE is '系统代码';
COMMENT ON COLUMN SYS_USER_LOGIN_LOG.CLIENT_IP is '客户端IP';
COMMENT ON COLUMN SYS_USER_LOGIN_LOG.CLIENT_PROVINCE is '客户端IP所在省份';
COMMENT ON COLUMN SYS_USER_LOGIN_LOG.CLIENT_CITY is '客户端IP所在城市';
COMMENT ON COLUMN SYS_USER_LOGIN_LOG.CLIENT_BROWSER is '客户端浏览器';
COMMENT ON COLUMN SYS_USER_LOGIN_LOG.CLIENT_OS is '客户端操作系统';
COMMENT ON COLUMN SYS_USER_LOGIN_LOG.LOGIN_TIME is '登录时间';
COMMENT ON COLUMN SYS_USER_LOGIN_LOG.LOGIN_STATUS is '登录状态(1-成功/0-失败)';
COMMENT ON COLUMN SYS_USER_LOGIN_LOG.LOGIN_DESCRIPTION is '登录失败原因';

CREATE TABLE "SYS_ENTERPRISE_INFO" (
 "ID" NUMBER(20),
 "COMPANY_CODE" VARCHAR2(20) NOT NULL,
 "COMPANY_NAME" VARCHAR2(255) NOT NULL,
 "EP_SCCD" VARCHAR2(18) DEFAULT NULL,
 "CUST_CODE" VARCHAR2(4) DEFAULT NULL,
 "CUST_NAME" VARCHAR2(20) DEFAULT NULL,
 "CONTACTS" VARCHAR2(100) DEFAULT NULL,
 "CONTACTS_TEL" VARCHAR2(100) DEFAULT NULL,
 "MAIL_ADDRESS" VARCHAR2(255) DEFAULT NULL,
 "EP_ADDRESS" VARCHAR2(255) DEFAULT NULL,
 "EP_LOGO_URL" VARCHAR2(255) DEFAULT NULL,
 "EP_SYS_NAME" VARCHAR2(255) DEFAULT NULL,
 "REMARK" VARCHAR2(255) DEFAULT NULL,
 "CREATE_TIME" DATE DEFAULT CURRENT_TIMESTAMP,
 "UPDATE_TIME" DATE DEFAULT CURRENT_TIMESTAMP,
 PRIMARY KEY (ID)
);
COMMENT ON TABLE SYS_ENTERPRISE_INFO is '企业信息表';
COMMENT ON COLUMN SYS_ENTERPRISE_INFO.ID is '自增列(业务无关)';
COMMENT ON COLUMN SYS_ENTERPRISE_INFO.COMPANY_CODE is '企业编码';
COMMENT ON COLUMN SYS_ENTERPRISE_INFO.COMPANY_NAME is '企业名称';
COMMENT ON COLUMN SYS_ENTERPRISE_INFO.EP_SCCD is '企业18位社会信用代码';
COMMENT ON COLUMN SYS_ENTERPRISE_INFO.CUST_CODE is '关区代码';
COMMENT ON COLUMN SYS_ENTERPRISE_INFO.CUST_NAME is '关区名称';
COMMENT ON COLUMN SYS_ENTERPRISE_INFO.CONTACTS is '联系人';
COMMENT ON COLUMN SYS_ENTERPRISE_INFO.CONTACTS_TEL is '联系电话';
COMMENT ON COLUMN SYS_ENTERPRISE_INFO.MAIL_ADDRESS is '邮件地址';
COMMENT ON COLUMN SYS_ENTERPRISE_INFO.EP_ADDRESS is '企业地址';
COMMENT ON COLUMN SYS_ENTERPRISE_INFO.EP_LOGO_URL is '企业专属logoURL';
COMMENT ON COLUMN SYS_ENTERPRISE_INFO.EP_SYS_NAME is '企业专属系统名称';
COMMENT ON COLUMN SYS_ENTERPRISE_INFO.REMARK is '备注';
COMMENT ON COLUMN SYS_ENTERPRISE_INFO.CREATE_TIME is '创建时间';
COMMENT ON COLUMN SYS_ENTERPRISE_INFO.UPDATE_TIME is '修改时间';

CREATE TABLE "SYS_TABLE_SETTING" (
 "ID" NUMBER(20),
 "USER_ID" VARCHAR2(36) DEFAULT NULL,
 "SETTING_TYPE" VARCHAR2(1) DEFAULT NULL,
 "TABLE_CODE" VARCHAR2(255) DEFAULT NULL,
 "JSON_DETAIL" clob DEFAULT NULL,
 "CREATE_TIME" date DEFAULT NULL,
 PRIMARY KEY ("ID")
);
COMMENT ON TABLE SYS_TABLE_SETTING is '用户查询配置表';
COMMENT ON COLUMN SYS_TABLE_SETTING.ID is '自增列(业务无关)';
COMMENT ON COLUMN SYS_TABLE_SETTING.USER_ID is '用户id';
COMMENT ON COLUMN SYS_TABLE_SETTING.SETTING_TYPE is '设置类型|1筛选项2表单项';
COMMENT ON COLUMN SYS_TABLE_SETTING.TABLE_CODE is '表名';
COMMENT ON COLUMN SYS_TABLE_SETTING.JSON_DETAIL is 'json明细';
COMMENT ON COLUMN SYS_TABLE_SETTING.CREATE_TIME is '创建时间';

CREATE TABLE "SYS_QUICK_MY_COLLECTION" (
 "ID" NUMBER(20),
 "USER_ID" VARCHAR2(50) DEFAULT NULL,
 "LOGIN_NAME" VARCHAR2(30) DEFAULT NULL,
 "TRUE_NAME" VARCHAR2(50) DEFAULT NULL,
 "MENU_ID" VARCHAR2(50) DEFAULT NULL,
 "MENU_NAME" VARCHAR2(100) DEFAULT NULL,
 "MENU_ROUTE" VARCHAR2(100) DEFAULT NULL,
 "REMARK" VARCHAR2(255) DEFAULT NULL,
 "CREATE_USER" VARCHAR2(50) DEFAULT NULL,
 "CREATE_TIME" date DEFAULT CURRENT_TIMESTAMP,
 "UPDATE_USER" VARCHAR2(50) DEFAULT NULL,
 "UPDATE_TIME" date DEFAULT CURRENT_TIMESTAMP,
 PRIMARY KEY ("ID")
);
COMMENT ON TABLE SYS_QUICK_MY_COLLECTION is '用户收藏表';
COMMENT ON COLUMN SYS_QUICK_MY_COLLECTION.ID is '自增主键';
COMMENT ON COLUMN SYS_QUICK_MY_COLLECTION.USER_ID is '权限平台用户ID';
COMMENT ON COLUMN SYS_QUICK_MY_COLLECTION.LOGIN_NAME is '用户名(权限平台登录用户名)';
COMMENT ON COLUMN SYS_QUICK_MY_COLLECTION.TRUE_NAME is '真实姓名';
COMMENT ON COLUMN SYS_QUICK_MY_COLLECTION.MENU_ID is '菜单编号';
COMMENT ON COLUMN SYS_QUICK_MY_COLLECTION.MENU_NAME is '菜单名称';
COMMENT ON COLUMN SYS_QUICK_MY_COLLECTION.MENU_ROUTE is '菜单路由地址';
COMMENT ON COLUMN SYS_QUICK_MY_COLLECTION.REMARK is '备注(图标)';
COMMENT ON COLUMN SYS_QUICK_MY_COLLECTION.CREATE_USER is '创建人';
COMMENT ON COLUMN SYS_QUICK_MY_COLLECTION.CREATE_TIME is '创建时间';
COMMENT ON COLUMN SYS_QUICK_MY_COLLECTION.UPDATE_USER is '修改人';
COMMENT ON COLUMN SYS_QUICK_MY_COLLECTION.UPDATE_TIME is '修改时间';

CREATE TABLE "SYS_MONITOR_API" (
   "ID" NUMBER(20),
   "APP_NAME" VARCHAR2(50) DEFAULT NULL,
   "URI" VARCHAR2(100) DEFAULT NULL,
   "RESP_TIME" NUMBER(20),
   "RESP_STATUS" NUMBER(2),
   "CREATE_TIME" date DEFAULT CURRENT_TIMESTAMP,
   PRIMARY KEY ("ID")
);

CREATE TABLE "SYS_MONITOR_DB" (
  "ID" NUMBER(20),
  "PERCENT" NUMBER(10,2) DEFAULT NULL,
  "CREATE_TIME" date DEFAULT CURRENT_TIMESTAMP,
  "M_TYPE" VARCHAR2(1) DEFAULT NULL,
  PRIMARY KEY ("ID")
);

CREATE TABLE "SYS_MONITOR_SQL" (
    "ID" NUMBER(20),
    "URI" VARCHAR2(255) DEFAULT NULL,
    "SQL_STR" VARCHAR2(6000) DEFAULT NULL,
    "SQL_TIME"  NUMBER(20),
    "CREATE_TIME" date DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY ("ID")
);

CREATE TABLE "SYS_CACHE_HELP" (
  "ID"  NUMBER(20),
  "QUERY_KEY" VARCHAR2(100) DEFAULT NULL,
  "STATUS" VARCHAR2(2) DEFAULT NULL,
  "SQL_COMMAND" VARCHAR2(2000) DEFAULT NULL,
  "BASE_COMMENT" VARCHAR2(255) DEFAULT NULL,
  "MEMO" VARCHAR2(400) DEFAULT NULL,
  "MEMO_DETAIL" VARCHAR2(400) DEFAULT NULL,
  "SYS" VARCHAR2(30) DEFAULT NULL,
  "LANGUAGE_TYPE" VARCHAR2(10) DEFAULT NULL,
  "CREATE_TIME" date DEFAULT CURRENT_TIMESTAMP,
  "UPDATE_TIME" date DEFAULT CURRENT_TIMESTAMP,
  "CREATE_USER" VARCHAR2(50)DEFAULT NULL,
  "UPDATE_USER" VARCHAR2(50) DEFAULT NULL,
  "PL_VERSION" VARCHAR2(100) DEFAULT NULL,
  "DB_VERSION" VARCHAR2(100) DEFAULT NULL,
  PRIMARY KEY ("ID")
);
COMMENT ON TABLE SYS_CACHE_HELP is '缓存参数维护';
COMMENT ON COLUMN SYS_CACHE_HELP.ID is '自增主键';
COMMENT ON COLUMN SYS_CACHE_HELP.QUERY_KEY is '查询key';
COMMENT ON COLUMN SYS_CACHE_HELP.STATUS is '状态-----保留';
COMMENT ON COLUMN SYS_CACHE_HELP.SQL_COMMAND is 'SQL';
COMMENT ON COLUMN SYS_CACHE_HELP.BASE_COMMENT is '备注';
COMMENT ON COLUMN SYS_CACHE_HELP.MEMO is '备注';
COMMENT ON COLUMN SYS_CACHE_HELP.MEMO_DETAIL is '备注2';
COMMENT ON COLUMN SYS_CACHE_HELP.SYS is '使用系统';
COMMENT ON COLUMN SYS_CACHE_HELP.LANGUAGE_TYPE is '语言类型';
COMMENT ON COLUMN SYS_CACHE_HELP.CREATE_TIME is '创建时间';
COMMENT ON COLUMN SYS_CACHE_HELP.UPDATE_TIME is '修改时间';
COMMENT ON COLUMN SYS_CACHE_HELP.CREATE_USER is '创建人';
COMMENT ON COLUMN SYS_CACHE_HELP.UPDATE_USER is '修改人';
COMMENT ON COLUMN SYS_CACHE_HELP.PL_VERSION is '当前缓存版本';
COMMENT ON COLUMN SYS_CACHE_HELP.DB_VERSION is '当前DB库版本';