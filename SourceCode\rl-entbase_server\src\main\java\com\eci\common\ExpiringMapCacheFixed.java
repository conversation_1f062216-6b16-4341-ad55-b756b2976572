package com.eci.common;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Iterator;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 修复版本：带自动过期时间的本地缓存工具类
 * 修复了原版本中的内存泄露问题
 */
public class ExpiringMapCacheFixed<K, V> {
    
    private static final Logger logger = LoggerFactory.getLogger(ExpiringMapCacheFixed.class);
    
    private final Map<K, CacheEntry<V>> cacheMap = new ConcurrentHashMap<>();
    private final long defaultTtlMillis;
    private final ScheduledExecutorService cleanupExecutor;
    private final AtomicBoolean shutdown = new AtomicBoolean(false);

    public ExpiringMapCacheFixed(long defaultTtlMillis) {
        this.defaultTtlMillis = defaultTtlMillis;
        
        // 使用单线程定时器，设置为守护线程
        this.cleanupExecutor = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread t = new Thread(r, "ExpiringMapCache-Cleanup-" + System.identityHashCode(this));
            t.setDaemon(true);
            return t;
        });
        
        startCleanupTask();
        
        // 添加关闭钩子
        Runtime.getRuntime().addShutdownHook(new Thread(this::shutdown));
        
        logger.debug("ExpiringMapCacheFixed 初始化完成，TTL: {}ms", defaultTtlMillis);
    }

    // 添加缓存，默认过期时间
    public void put(K key, V value) {
        if (shutdown.get()) {
            logger.warn("缓存已关闭，忽略 put 操作: {}", key);
            return;
        }
        cacheMap.put(key, new CacheEntry<>(value, defaultTtlMillis));
    }

    // 添加缓存，自定义过期时间
    public void put(K key, V value, long ttlMillis) {
        if (shutdown.get()) {
            logger.warn("缓存已关闭，忽略 put 操作: {}", key);
            return;
        }
        cacheMap.put(key, new CacheEntry<>(value, ttlMillis));
    }

    // 获取缓存，如果已过期则返回 null
    public V get(K key) {
        if (shutdown.get()) {
            return null;
        }
        
        CacheEntry<V> entry = cacheMap.get(key);
        if (entry == null || entry.isExpired()) {
            if (entry != null) {
                cacheMap.remove(key); // 清理过期数据
            }
            return null;
        }
        return entry.getValue();
    }

    // 检查键是否存在且未过期
    public boolean containsKey(K key) {
        return get(key) != null;
    }

    // 删除缓存
    public void remove(K key) {
        cacheMap.remove(key);
    }

    // 清空所有缓存
    public void clear() {
        cacheMap.clear();
    }

    // 获取缓存大小
    public int size() {
        return cacheMap.size();
    }

    // 获取有效缓存大小（排除过期项）
    public int validSize() {
        if (shutdown.get()) {
            return 0;
        }
        
        int count = 0;
        for (CacheEntry<V> entry : cacheMap.values()) {
            if (!entry.isExpired()) {
                count++;
            }
        }
        return count;
    }

    // 手动清理过期项
    public int cleanupExpired() {
        if (shutdown.get()) {
            return 0;
        }
        
        int removedCount = 0;
        Iterator<Map.Entry<K, CacheEntry<V>>> iterator = cacheMap.entrySet().iterator();
        
        while (iterator.hasNext()) {
            Map.Entry<K, CacheEntry<V>> entry = iterator.next();
            if (entry.getValue().isExpired()) {
                iterator.remove();
                removedCount++;
            }
        }
        
        if (removedCount > 0) {
            logger.debug("清理过期缓存项: {} 个", removedCount);
        }
        
        return removedCount;
    }

    // 获取缓存统计信息
    public String getStats() {
        if (shutdown.get()) {
            return "缓存已关闭";
        }
        
        int totalSize = cacheMap.size();
        int validSize = validSize();
        int expiredSize = totalSize - validSize;
        
        return String.format("总大小: %d, 有效: %d, 过期: %d", totalSize, validSize, expiredSize);
    }

    // 定时清理任务：每分钟检查一次
    private void startCleanupTask() {
        cleanupExecutor.scheduleAtFixedRate(() -> {
            if (shutdown.get()) {
                return;
            }
            
            try {
                int removedCount = cleanupExpired();
                if (removedCount > 0) {
                    logger.debug("定时清理完成，移除 {} 个过期项", removedCount);
                }
            } catch (Exception e) {
                logger.warn("定时清理任务执行失败", e);
            }
        }, 60, 60, TimeUnit.SECONDS);
    }

    // 关闭缓存，释放资源
    public void shutdown() {
        if (shutdown.compareAndSet(false, true)) {
            try {
                cleanupExecutor.shutdown();
                if (!cleanupExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                    cleanupExecutor.shutdownNow();
                }
                clear();
                logger.debug("ExpiringMapCacheFixed 已关闭");
            } catch (InterruptedException e) {
                cleanupExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }

    // 检查是否已关闭
    public boolean isShutdown() {
        return shutdown.get();
    }

    // 缓存条目结构：包含值和过期时间
    private static class CacheEntry<V> {
        private final V value;
        private final long expireAt; // 过期时间戳（毫秒）

        public CacheEntry(V value, long ttlMillis) {
            this.value = value;
            this.expireAt = System.currentTimeMillis() + ttlMillis;
        }

        public V getValue() {
            return value;
        }

        public boolean isExpired() {
            return System.currentTimeMillis() > expireAt;
        }

        public long getExpireAt() {
            return expireAt;
        }

        public long getRemainingTtl() {
            long remaining = expireAt - System.currentTimeMillis();
            return Math.max(0, remaining);
        }
    }

    /**
     * 内存使用监控
     */
    public static class MemoryMonitor {
        private static final Logger logger = LoggerFactory.getLogger(MemoryMonitor.class);

        public static String getMemoryStats() {
            Runtime runtime = Runtime.getRuntime();
            long totalMemory = runtime.totalMemory();
            long freeMemory = runtime.freeMemory();
            long usedMemory = totalMemory - freeMemory;
            long maxMemory = runtime.maxMemory();

            return String.format(
                "内存使用: %d MB / %d MB (%.2f%%), 最大: %d MB",
                usedMemory / 1024 / 1024,
                totalMemory / 1024 / 1024,
                (double) usedMemory / totalMemory * 100,
                maxMemory / 1024 / 1024
            );
        }

        public static void logMemoryStats() {
            logger.info(getMemoryStats());
        }

        public static boolean isMemoryPressure() {
            Runtime runtime = Runtime.getRuntime();
            long totalMemory = runtime.totalMemory();
            long freeMemory = runtime.freeMemory();
            long usedMemory = totalMemory - freeMemory;
            
            // 如果内存使用超过 80%，认为有内存压力
            return (double) usedMemory / totalMemory > 0.8;
        }
    }
}
