package com.eci.common.validations;

import com.eci.common.Zsr;
import com.eci.exception.BaseException;

import java.lang.reflect.Field;
import java.util.List;

public class ZsrValidationUtil {
    public static void validation(Object entity) throws IllegalAccessException {
        Class<?> clazz = entity.getClass();

        // 获取类及其所有父类的字段
        List<Field> fields = Zsr.Fields.getAllFields(clazz);

        for (Field field : fields) {
            if (field.getType() == String.class && field.isAnnotationPresent(ZsrValidation.class)) {
                ZsrValidation stringLengthAnnotation = field.getAnnotation(ZsrValidation.class);
                int maxLength = stringLengthAnnotation.length();
                String name = stringLengthAnnotation.name();
                String displayName = stringLengthAnnotation.displayName();
                String errorMessage = stringLengthAnnotation.errorMessage();
                field.setAccessible(true); // 允许访问私有字段
                String fieldValue = (String) field.get(entity);
                boolean required = stringLengthAnnotation.required();

                // 必填校验
                if (required && (fieldValue == null || fieldValue.trim().isEmpty())) {
                    String exMessageFormat;
                    if (!Zsr.String.IsNullOrWhiteSpace(errorMessage)) {
                        exMessageFormat = errorMessage;
                    } else {
                        if (!Zsr.String.IsNullOrWhiteSpace(displayName)) {
                            exMessageFormat = String.format("%s 未填写", displayName);
                        } else {
                            if (Zsr.String.IsNullOrWhiteSpace(name)) {
                                exMessageFormat = String.format("%s 未填写", field.getName());
                            } else {
                                exMessageFormat = String.format("%s 未填写", name);
                            }
                        }
                    }

                    throw new BaseException(exMessageFormat);
                }
                // 长度校验
                if (fieldValue != null && fieldValue.length() > maxLength) {
                    String exMessageFormat;
                    if (Zsr.String.IsNullOrWhiteSpace(name)) {
                        exMessageFormat = String.format("%s 的长度超过限制 (%d)", field.getName(), maxLength);
                    } else {
                        exMessageFormat = String.format("%s 的长度超过限制 (%d)", name, maxLength);
                    }
                    throw new BaseException(
                            exMessageFormat
                    );
                }
            }
        }
    }


}
