DROP TABLE IF EXISTS SYS_OPER_LOG;
CREATE TABLE SYS_OPER_LOG  (
 ID bigint NOT NULL COMMENT '主键id',
 TITLE varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '操作模块',
 BUSINESS_TYPE int NULL DEFAULT NULL COMMENT '业务类型|0其它 1新增 2修改 3删除',
 OPER_URL varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '请求url',
 METHOD varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '请求后端方法',
 REQUEST_METHOD varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '请求方式',
 OPER_IP varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '操作地址',
 OPER_LOCATION varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '操作地点',
 OPER_STATUS int NULL DEFAULT NULL COMMENT '操作状态|0正常 1异常',
 ERROR_MSG text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '错误消息',
 OPER_TIME datetime(0) NULL DEFAULT NULL COMMENT '操作时间',
 OPER_PARAM text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '请求参数',
 JSON_RESULT text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '返回响应体',
 USER_ID varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '用户id',
 LOGIN_NAME varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '用户名',
 OPER_OS varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '操作系统',
 BROWSER varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '浏览器',
 SERVER varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '本地服务器',
 PRIMARY KEY (ID)
);

DROP TABLE IF EXISTS SYS_DATA_HELP;
CREATE TABLE SYS_DATA_HELP (
   ID bigint NOT NULL COMMENT '自增主键',
   QUERY_KEY varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '查询key',
   QUERY_TYPE varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '查询类型1:下拉框 2:放大镜',
   SQL_COMMAND varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'SQL',
   BASE_COMMENT varchar(800) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '说明',
   SYS varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '使用系统',
   CREATE_TIME datetime DEFAULT NULL COMMENT '创建时间',
   UPDATE_TIME datetime DEFAULT NULL COMMENT '更新时间',
   USE_CACHE char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT 'N' COMMENT '缓存启用状态0不用1用',
   LANGUAGE_TYPE varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '语言类型',
   DATA_CHANGE_CODE varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '数据转换代码',
   DATA_CHANGE_NAME varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '数据转换名称',
   TG_SORT_FIELD varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '排序',
   CREATE_USER varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '创建人',
   UPDATE_USER varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '修改人',
   CODE_MEMO varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '代码描述',
   NAME_MEMO varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '名称描述',
   COMPARE_TYPE varchar(20) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '查询方式 like eq',
   CHOOSE_SHOW varchar(2000) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '放大镜显示-----功能暂不实现',
   TG_QUERY_FIELD varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '查询列和conn配合使用-----功能暂不实现',
   AUTO_UPPER char(1) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '自动转大写-----功能暂不实现',
   QUERY_MODE varchar(20) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '查询模式-----功能暂不实现',
   STATUS varchar(2) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '状态-----保留',
   ECI_LOCK char(1) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '锁定状态',
   CONVERT_SQL varchar(2000) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '转换SQL',
   PL_VERSION varchar(200) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '当前版本',
   NEED_DOWNLOAD char(1) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '是否需要下载',
   MEMO_DETAIL varchar(800) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '详细备注',
   ADMIN_LOCK char(1) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '管理员锁定',
   ASSEMBLY_NAME varchar(400) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '程序集扩展',
   FILTER char(1) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '打开页面是否条件过滤',
   LAYOUT varchar(200) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '布局',
   EDIT_URL varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '编辑地址',
   CONFIG varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '配置信息',
   WIDTH varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '宽度',
   HEIGHT varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '高度',
   QUERY_DATA varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '查询数据',
   TG_PAGE_SIZE varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '分页大小',
   PRIMARY KEY (ID)
);

DROP TABLE IF EXISTS SYS_USER_INFO;
CREATE TABLE SYS_USER_INFO (
 ID bigint NOT NULL COMMENT '自增列',
 USER_ID varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '权限平台用户ID',
 LOGIN_NAME varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '权限平台登录用户名',
 TRUE_NAME varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '权限平台真实名',
 COMPANY_CODE varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '企业代码',
 COMPANY_NAME varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '企业名称',
 CUST_CODE varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '关区代码',
 CUST_NAME varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '关区名称',
 USER_NICKNAME varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '用户别名',
 USER_IMG varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '头像',
 USER_SEX varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '0' COMMENT '性别(0-保密/1-男/2-女)',
 LOGIN_COUNT int DEFAULT '0' COMMENT '登录次数',
 LOGIN_TIME datetime DEFAULT NULL COMMENT '本次登录时间',
 LOGIN_LAST_TIME datetime DEFAULT NULL COMMENT '上次登录时间',
 CLIENT_IP varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '客户端IP',
 CLIENT_PROVINCE varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '客户端IP所在省份',
 CLIENT_CITY varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '客户端IP所在城市',
 CLIENT_BROWSER varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '客户端浏览器',
 CLIENT_OS varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '客户端操作系统',
 USER_TOKEN varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'USER_TOKEN',
 BIND_PHONE_NO varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '绑定手机号',
 WEB_SIDE_TYPE varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '2' COMMENT '侧边栏类型(1-栏式1/2-栏式2)',
 WEB_HEAD_TYPE varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '3' COMMENT '顶部模式(1-白/2-黑/3-主题色)',
 WEB_THEME varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '#409eff' COMMENT '主题颜色',
 WEB_LAYOUT varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '3' COMMENT '布局模式(1-侧边/2-顶部/3-混合)',
 WEB_SIDE_IS_ICON varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '1' COMMENT '侧边栏彩色图标',
 WEB_SIDE_IS_OPEN varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '1' COMMENT '侧栏排它展开',
 WEB_IS_TAB varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '1' COMMENT '启用标签页',
 WEB_TAB_TYPE varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '1' COMMENT '标签显示风格(1-默认/2-圆点/3-卡片)',
 MAIL_ADDRESS varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '用户邮箱地址',
 PWD_LAST_UPDATE_TIME datetime DEFAULT CURRENT_TIMESTAMP COMMENT '密码最后修改时间',
 DEV varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '0' COMMENT '是否运维|0否1是',
 PRIMARY KEY (ID)
);

DROP TABLE IF EXISTS SYS_USER_LOGIN_LOG;
CREATE TABLE SYS_USER_LOGIN_LOG (
 ID bigint NOT NULL COMMENT '自增列(业务无关)',
 USER_ID varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '权限平台用户ID',
 LOGIN_NAME varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '权限平台登录用户名',
 SYS_CODE varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '系统代码',
 CLIENT_IP varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '客户端IP',
 CLIENT_PROVINCE varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '客户端IP所在省份',
 CLIENT_CITY varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '客户端IP所在城市',
 CLIENT_BROWSER varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '客户端浏览器',
 CLIENT_OS varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '客户端操作系统',
 LOGIN_TIME datetime DEFAULT NULL COMMENT '登录时间',
 LOGIN_STATUS int DEFAULT NULL COMMENT '登录状态(1-成功/0-失败)',
 LOGIN_DESCRIPTION varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '登录失败原因',
 ACTION_PARAMS text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT '请求参数',
 PRIMARY KEY (ID)
);

DROP TABLE IF EXISTS SYS_ENTERPRISE_INFO;
CREATE TABLE SYS_ENTERPRISE_INFO (
 ID bigint NOT NULL AUTO_INCREMENT COMMENT '自增列(业务无关)',
 COMPANY_CODE varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '企业编码',
 COMPANY_NAME varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '企业名称',
 EP_SCCD varchar(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '企业18位社会信用代码',
 CUST_CODE varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '关区代码',
 CUST_NAME varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '关区名称',
 CONTACTS varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '联系人',
 CONTACTS_TEL varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '联系电话',
 MAIL_ADDRESS varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '邮件地址',
 EP_ADDRESS varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '企业地址',
 EP_LOGO_URL varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '企业专属logoURL',
 EP_SYS_NAME varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '企业专属系统名称',
 REMARK varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
 CREATE_TIME datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 UPDATE_TIME datetime DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
 PRIMARY KEY (ID)
);

DROP TABLE IF EXISTS SYS_TABLE_SETTING;
CREATE TABLE SYS_TABLE_SETTING (
 ID bigint NOT NULL COMMENT '主键',
 USER_ID varchar(36) DEFAULT NULL COMMENT '用户id',
 SETTING_TYPE varchar(1) DEFAULT NULL COMMENT '设置类型|1筛选项2表单项',
 TABLE_CODE varchar(255) DEFAULT NULL COMMENT '表名',
 JSON_DETAIL text COMMENT 'json明细',
 CREATE_TIME datetime DEFAULT NULL COMMENT '创建时间',
 PRIMARY KEY (ID)
);

CREATE TABLE SYS_QUICK_MY_COLLECTION (
 ID bigint NOT NULL COMMENT '自增主键',
 USER_ID varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '权限平台用户ID',
 LOGIN_NAME varchar(30) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '用户名(权限平台登录用户名)',
 TRUE_NAME varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '真实姓名',
 MENU_ID varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '菜单编号',
 MENU_NAME varchar(100) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '菜单名称',
 MENU_ROUTE varchar(100) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '菜单路由地址',
 REMARK varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注(图标)',
 CREATE_USER varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '创建人',
 CREATE_TIME datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 UPDATE_USER varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '修改人',
 UPDATE_TIME datetime DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
 PRIMARY KEY (ID)
);

CREATE TABLE SYS_MONITOR_API (
 ID bigint NOT NULL COMMENT '主键',
 APP_NAME varchar(50) DEFAULT NULL COMMENT '系统名称',
 URI varchar(255) DEFAULT NULL COMMENT '接口地址',
 RESP_TIME int DEFAULT NULL COMMENT '响应时长(毫秒)',
 RESP_STATUS int DEFAULT NULL COMMENT '返回状态:0正常 1异常',
 CREATE_TIME datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 PRIMARY KEY (ID)
);

CREATE TABLE SYS_MONITOR_SQL (
 ID bigint NOT NULL COMMENT '主键',
 URI varchar(255) DEFAULT NULL COMMENT '接口地址',
 SQL_STR text COMMENT 'SQL语句',
 SQL_TIME int DEFAULT NULL COMMENT 'SQL时长(毫秒)',
 CREATE_TIME datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 PRIMARY KEY (ID)
);

DROP TABLE IF EXISTS SYS_CACHE_HELP;
CREATE TABLE SYS_CACHE_HELP (
  ID bigint NOT NULL COMMENT '自增主键',
  QUERY_KEY varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '查询key',
  STATUS varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '状态-----保留',
  SQL_COMMAND varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'SQL',
  BASE_COMMENT varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
  MEMO varchar(400) COLLATE utf8mb4_bin DEFAULT NULL,
  MEMO_DETAIL varchar(400) COLLATE utf8mb4_bin DEFAULT NULL,
  SYS varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '使用系统',
  LANGUAGE_TYPE varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '语言类型',
  CREATE_TIME datetime DEFAULT NULL COMMENT '创建时间',
  UPDATE_TIME datetime DEFAULT NULL COMMENT '更新时间',
  CREATE_USER varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '创建人',
  UPDATE_USER varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '修改人',
  PL_VERSION varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '当前缓存版本',
  DB_VERSION varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '当前DB库版本',
  PRIMARY KEY (ID)
);
