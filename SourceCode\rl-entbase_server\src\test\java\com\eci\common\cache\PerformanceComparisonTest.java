package com.eci.common.cache;

import com.eci.common.DictFieldUtils;
import com.eci.common.DictFieldUtilsV3;
import com.eci.common.ExpiringMapCache;
import com.eci.common.ExpiringMapCacheFixed;
import com.eci.common.cache.adapter.LocalCacheAdapter;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Timeout;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 性能对比测试
 * 比较新旧版本的性能差异
 */
public class PerformanceComparisonTest {

    private static final int OPERATIONS_COUNT = 10000;
    private static final int THREAD_COUNT = 4;

    @Test
    @Timeout(value = 60, unit = TimeUnit.SECONDS)
    void testCachePerformanceComparison() {
        System.out.println("=== 缓存性能对比测试 ===");
        
        // 测试原版 ExpiringMapCache
        long oldCacheTime = testExpiringMapCachePerformance();
        
        // 测试修复版 ExpiringMapCacheFixed
        long fixedCacheTime = testExpiringMapCacheFixedPerformance();
        
        // 测试新版 LocalCacheAdapter
        long newCacheTime = testLocalCacheAdapterPerformance();
        
        System.out.printf("原版缓存耗时: %d ms%n", oldCacheTime);
        System.out.printf("修复版缓存耗时: %d ms%n", fixedCacheTime);
        System.out.printf("新版缓存耗时: %d ms%n", newCacheTime);
        
        // 性能应该在合理范围内
        assertTrue(fixedCacheTime < oldCacheTime * 2, "修复版性能不应该显著下降");
        assertTrue(newCacheTime < oldCacheTime * 2, "新版性能不应该显著下降");
    }

    private long testExpiringMapCachePerformance() {
        ExpiringMapCache<String, String> cache = new ExpiringMapCache<>(300_000);
        
        long startTime = System.currentTimeMillis();
        
        // 执行大量操作
        for (int i = 0; i < OPERATIONS_COUNT; i++) {
            String key = "key_" + i;
            String value = "value_" + i;
            
            cache.put(key, value);
            cache.get(key);
            
            if (i % 1000 == 0) {
                cache.size();
            }
        }
        
        long endTime = System.currentTimeMillis();
        return endTime - startTime;
    }

    private long testExpiringMapCacheFixedPerformance() {
        ExpiringMapCacheFixed<String, String> cache = new ExpiringMapCacheFixed<>(300_000);
        
        try {
            long startTime = System.currentTimeMillis();
            
            // 执行大量操作
            for (int i = 0; i < OPERATIONS_COUNT; i++) {
                String key = "key_" + i;
                String value = "value_" + i;
                
                cache.put(key, value);
                cache.get(key);
                
                if (i % 1000 == 0) {
                    cache.size();
                }
            }
            
            long endTime = System.currentTimeMillis();
            return endTime - startTime;
            
        } finally {
            cache.shutdown();
        }
    }

    private long testLocalCacheAdapterPerformance() {
        LocalCacheAdapter<String, String> cache = new LocalCacheAdapter<>(
                OPERATIONS_COUNT, 5, TimeUnit.MINUTES, true);
        
        try {
            long startTime = System.currentTimeMillis();
            
            // 执行大量操作
            for (int i = 0; i < OPERATIONS_COUNT; i++) {
                String key = "key_" + i;
                String value = "value_" + i;
                
                cache.put(key, value);
                cache.get(key);
                
                if (i % 1000 == 0) {
                    cache.size();
                }
            }
            
            long endTime = System.currentTimeMillis();
            return endTime - startTime;
            
        } finally {
            cache.shutdown();
        }
    }

    @Test
    @Timeout(value = 60, unit = TimeUnit.SECONDS)
    void testConcurrentPerformanceComparison() throws InterruptedException {
        System.out.println("=== 并发性能对比测试 ===");
        
        // 测试修复版并发性能
        long fixedConcurrentTime = testConcurrentPerformance(true);
        
        // 测试新版并发性能
        long newConcurrentTime = testConcurrentPerformance(false);
        
        System.out.printf("修复版并发耗时: %d ms%n", fixedConcurrentTime);
        System.out.printf("新版并发耗时: %d ms%n", newConcurrentTime);
        
        // 并发性能应该在合理范围内
        assertTrue(Math.abs(newConcurrentTime - fixedConcurrentTime) < fixedConcurrentTime * 0.5,
                "新版并发性能应该与修复版相近");
    }

    private long testConcurrentPerformance(boolean useFixed) throws InterruptedException {
        Object cache;
        if (useFixed) {
            cache = new ExpiringMapCacheFixed<String, String>(300_000);
        } else {
            cache = new LocalCacheAdapter<String, String>(OPERATIONS_COUNT, 5, TimeUnit.MINUTES, true);
        }
        
        try {
            long startTime = System.currentTimeMillis();
            
            Thread[] threads = new Thread[THREAD_COUNT];
            
            for (int t = 0; t < THREAD_COUNT; t++) {
                final int threadId = t;
                threads[t] = new Thread(() -> {
                    for (int i = 0; i < OPERATIONS_COUNT / THREAD_COUNT; i++) {
                        String key = "thread_" + threadId + "_key_" + i;
                        String value = "thread_" + threadId + "_value_" + i;
                        
                        if (useFixed) {
                            ExpiringMapCacheFixed<String, String> fixedCache = 
                                (ExpiringMapCacheFixed<String, String>) cache;
                            fixedCache.put(key, value);
                            fixedCache.get(key);
                        } else {
                            LocalCacheAdapter<String, String> newCache = 
                                (LocalCacheAdapter<String, String>) cache;
                            newCache.put(key, value);
                            newCache.get(key);
                        }
                    }
                });
            }
            
            // 启动所有线程
            for (Thread thread : threads) {
                thread.start();
            }
            
            // 等待所有线程完成
            for (Thread thread : threads) {
                thread.join();
            }
            
            long endTime = System.currentTimeMillis();
            return endTime - startTime;
            
        } finally {
            if (useFixed) {
                ((ExpiringMapCacheFixed<String, String>) cache).shutdown();
            } else {
                ((LocalCacheAdapter<String, String>) cache).shutdown();
            }
        }
    }

    @Test
    void testMemoryUsageComparison() {
        System.out.println("=== 内存使用对比测试 ===");
        
        Runtime runtime = Runtime.getRuntime();
        
        // 测试原版内存使用
        runtime.gc();
        long beforeOld = runtime.totalMemory() - runtime.freeMemory();
        
        List<ExpiringMapCache<String, String>> oldCaches = new ArrayList<>();
        for (int i = 0; i < 100; i++) {
            ExpiringMapCache<String, String> cache = new ExpiringMapCache<>(10000);
            for (int j = 0; j < 100; j++) {
                cache.put("key_" + j, "value_" + j);
            }
            oldCaches.add(cache);
        }
        
        runtime.gc();
        long afterOld = runtime.totalMemory() - runtime.freeMemory();
        long oldMemoryUsage = afterOld - beforeOld;
        
        // 清理原版缓存
        oldCaches.clear();
        runtime.gc();
        
        // 测试新版内存使用
        long beforeNew = runtime.totalMemory() - runtime.freeMemory();
        
        List<LocalCacheAdapter<String, String>> newCaches = new ArrayList<>();
        for (int i = 0; i < 100; i++) {
            LocalCacheAdapter<String, String> cache = 
                new LocalCacheAdapter<>(100, 5, TimeUnit.MINUTES, true);
            for (int j = 0; j < 100; j++) {
                cache.put("key_" + j, "value_" + j);
            }
            newCaches.add(cache);
        }
        
        runtime.gc();
        long afterNew = runtime.totalMemory() - runtime.freeMemory();
        long newMemoryUsage = afterNew - beforeNew;
        
        // 清理新版缓存
        for (LocalCacheAdapter<String, String> cache : newCaches) {
            cache.shutdown();
        }
        newCaches.clear();
        
        System.out.printf("原版内存使用: %d KB%n", oldMemoryUsage / 1024);
        System.out.printf("新版内存使用: %d KB%n", newMemoryUsage / 1024);
        
        // 新版内存使用应该不会显著增加
        assertTrue(newMemoryUsage < oldMemoryUsage * 1.5, 
                "新版内存使用不应该显著增加");
    }

    @Test
    void testCacheHitRateComparison() {
        System.out.println("=== 缓存命中率对比测试 ===");
        
        // 测试新版缓存命中率
        LocalCacheAdapter<String, String> cache = 
            new LocalCacheAdapter<>(1000, 5, TimeUnit.MINUTES, true);
        
        try {
            // 添加数据
            for (int i = 0; i < 500; i++) {
                cache.put("key_" + i, "value_" + i);
            }
            
            // 执行查询操作
            int hits = 0;
            int total = 1000;
            
            for (int i = 0; i < total; i++) {
                String key = "key_" + (i % 600); // 部分命中，部分未命中
                if (cache.get(key) != null) {
                    hits++;
                }
            }
            
            double hitRate = (double) hits / total;
            System.out.printf("缓存命中率: %.2f%%%n", hitRate * 100);
            
            // 验证统计信息
            LocalCacheAdapter.CacheStats stats = cache.getStats();
            assertTrue(stats.getHitCount() > 0, "应该有缓存命中");
            assertTrue(stats.getMissCount() > 0, "应该有缓存未命中");
            assertTrue(stats.getHitRate() > 0, "命中率应该大于0");
            
        } finally {
            cache.shutdown();
        }
    }

    @Test
    void testCacheEvictionPerformance() {
        System.out.println("=== 缓存驱逐性能测试 ===");
        
        // 测试小容量缓存的驱逐性能
        LocalCacheAdapter<String, String> smallCache = 
            new LocalCacheAdapter<>(100, 5, TimeUnit.MINUTES, true);
        
        try {
            long startTime = System.currentTimeMillis();
            
            // 添加超过容量的数据，触发驱逐
            for (int i = 0; i < 1000; i++) {
                smallCache.put("key_" + i, "value_" + i);
            }
            
            long endTime = System.currentTimeMillis();
            long evictionTime = endTime - startTime;
            
            System.out.printf("驱逐操作耗时: %d ms%n", evictionTime);
            
            // 验证缓存大小被限制
            assertTrue(smallCache.size() <= 100, "缓存大小应该被限制");
            
            // 驱逐操作应该在合理时间内完成
            assertTrue(evictionTime < 5000, "驱逐操作应该在5秒内完成");
            
        } finally {
            smallCache.shutdown();
        }
    }

    @Test
    void testCleanupPerformance() {
        System.out.println("=== 清理性能测试 ===");
        
        ExpiringMapCacheFixed<String, String> cache = new ExpiringMapCacheFixed<>(1000);
        
        try {
            // 添加大量短期数据
            for (int i = 0; i < 10000; i++) {
                cache.put("key_" + i, "value_" + i, 100); // 100ms 过期
            }
            
            // 等待数据过期
            try {
                Thread.sleep(200);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
            
            // 测试清理性能
            long startTime = System.currentTimeMillis();
            int removedCount = cache.cleanupExpired();
            long endTime = System.currentTimeMillis();
            
            long cleanupTime = endTime - startTime;
            
            System.out.printf("清理 %d 个过期项耗时: %d ms%n", removedCount, cleanupTime);
            
            // 清理操作应该高效
            assertTrue(cleanupTime < 1000, "清理操作应该在1秒内完成");
            assertTrue(removedCount > 0, "应该有过期项被清理");
            
        } finally {
            cache.shutdown();
        }
    }
}
