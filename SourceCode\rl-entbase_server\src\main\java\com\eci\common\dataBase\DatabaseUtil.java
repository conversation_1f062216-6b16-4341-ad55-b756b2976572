package com.eci.common.dataBase;


import java.sql.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.concurrent.ConcurrentHashMap;

public class DatabaseUtil {
    private static final Map<String, DatabaseUtil> instances = new ConcurrentHashMap<>();
    private Connection connection;
    private static final int BATCH_SIZE = 1000;

    // 私有构造函数
    private DatabaseUtil() {
    }

    /**
     * 获取数据库工具类实例
     *
     * @param dbType 数据库类型：ORACLE, MYSQL, DM(达梦), VASTBASE(海量)
     * @return DatabaseUtil实例
     */
    public static synchronized DatabaseUtil getInstance(String dbType) {
        return instances.computeIfAbsent(dbType, k -> new DatabaseUtil());
    }

    /**
     * 初始化数据库连接
     *
     * @param url      数据库URL
     * @param username 用户名
     * @param password 密码
     *                 <remark> // 默认关闭自动提交</remark>
     */
    public void init(String url, String username, String password) throws SQLException {
        if (connection != null && !connection.isClosed()) {
            return;
        }

        try {
            // 根据URL判断数据库类型并加载对应驱动
            if (url.contains("oracle")) {
                Class.forName("oracle.jdbc.driver.OracleDriver");
            } else if (url.contains("mysql")) {
                Class.forName("com.mysql.cj.jdbc.Driver");
            } else if (url.contains("dm")) {
                Class.forName("dm.jdbc.driver.DmDriver");
            } else if (url.contains("vastbase")) {
                Class.forName("com.vastbase.jdbc.Driver");
            }

            Properties props = new Properties();
            props.setProperty("user", username);
            props.setProperty("password", password);

            // 设置通用连接属性
            props.setProperty("remarksReporting", "true");
            props.setProperty("useInformationSchema", "true");

            connection = DriverManager.getConnection(url, props);
            connection.setAutoCommit(false); // 默认关闭自动提交
        } catch (ClassNotFoundException e) {
            throw new SQLException("Database driver not found", e);
        }
    }


    /**
     * 执行任意SQL语句（支持INSERT、UPDATE、DELETE、DDL语句等）
     *
     * @param sql    SQL语句
     * @param params 参数数组
     * @return 受影响的行数，DDL语句返回0
     */
    public int execute(String sql, Object... params) throws SQLException {
        try (Statement stmt = connection.createStatement()) {
            // 如果没有参数，直接执行SQL
            if (params == null || params.length == 0) {
                boolean isQuery = stmt.execute(sql);
                return isQuery ? 0 : stmt.getUpdateCount();
            }
        }

        // 有参数的情况下使用PreparedStatement
        try (PreparedStatement pstmt = connection.prepareStatement(sql)) {
            for (int i = 0; i < params.length; i++) {
                pstmt.setObject(i + 1, params[i]);
            }
            boolean isQuery = pstmt.execute();
            return isQuery ? 0 : pstmt.getUpdateCount();
        }
    }


    /**
     * 执行查询操作
     *
     * @param sql    SQL语句
     * @param params 参数数组
     * @return 查询结果列表
     */
    public List<Map<String, Object>> query(String sql, Object... params) throws SQLException {
        List<Map<String, Object>> resultList = new ArrayList<>();

        try (PreparedStatement pstmt = connection.prepareStatement(sql)) {
            // 设置参数
            for (int i = 0; i < params.length; i++) {
                pstmt.setObject(i + 1, params[i]);
            }

            try (ResultSet rs = pstmt.executeQuery()) {
                ResultSetMetaData metaData = rs.getMetaData();
                int columnCount = metaData.getColumnCount();

                while (rs.next()) {
                    Map<String, Object> row = new ConcurrentHashMap<>();
                    for (int i = 1; i <= columnCount; i++) {
                        String columnName = metaData.getColumnLabel(i);
                        Object value = rs.getObject(i);
                        row.put(columnName, value);
                    }
                    resultList.add(row);
                }
            }
        }

        return resultList;
    }

    /**
     * 执行更新操作（增删改）
     *
     * @param sql    SQL语句
     * @param params 参数数组
     * @return 受影响的行数
     */
    public int update(String sql, Object... params) throws SQLException {
        try (PreparedStatement pstmt = connection.prepareStatement(sql)) {
            for (int i = 0; i < params.length; i++) {
                pstmt.setObject(i + 1, params[i]);
            }
            return pstmt.executeUpdate();
        }
    }

    /**
     * 批量执行更新操作
     *
     * @param sql        SQL语句
     * @param paramsList 参数列表
     * @return 受影响的行数数组
     */
    public int[] batchUpdate(String sql, List<Object[]> paramsList) throws SQLException {
        try (PreparedStatement pstmt = connection.prepareStatement(sql)) {
            int count = 0;
            for (Object[] params : paramsList) {
                for (int i = 0; i < params.length; i++) {
                    pstmt.setObject(i + 1, params[i]);
                }
                pstmt.addBatch();

                if (++count % BATCH_SIZE == 0) {
                    pstmt.executeBatch();
                    pstmt.clearBatch();
                }
            }

            if (count % BATCH_SIZE != 0) {
                return pstmt.executeBatch();
            }
        }
        return new int[0];
    }

    /**
     * 开始事务
     */
    public void beginTransaction() throws SQLException {
        connection.setAutoCommit(false);
    }

    /**
     * 提交事务
     */
    public void commit() throws SQLException {
        connection.commit();
    }

    /**
     * 回滚事务
     */
    public void rollback() throws SQLException {
        connection.rollback();
    }

    /**
     * 关闭连接
     */
    public void close() {
        try {
            if (connection != null && !connection.isClosed()) {
                connection.close();
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }


// 使用示例：
//    DatabaseUtil db = DatabaseUtil.getInstance("MYSQL");
//db.init("********************************", "username", "password");
//
//try {
//        // 执行DDL语句
//        db.execute("CREATE TABLE users (id INT PRIMARY KEY AUTO_INCREMENT, name VARCHAR(100))");
//
//        // 执行带参数的INSERT语句
//        int rows = db.execute("INSERT INTO users (name) VALUES (?)", "张三");
//        System.out.println("插入了 " + rows + " 行数据");
//
//        // 执行不带参数的更新语句
//        rows = db.execute("UPDATE users SET name = '李四' WHERE id = 1");
//        System.out.println("更新了 " + rows + " 行数据");
//
//        db.commit();
//    } catch (SQLException e) {
//        db.rollback();
//        e.printStackTrace();
//    } finally {
//        db.close();
//    }


}
