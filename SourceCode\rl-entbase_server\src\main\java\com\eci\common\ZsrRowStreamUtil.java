package com.eci.common;

import com.eci.wu.core.DataTable;

import java.util.List;
import java.util.function.Consumer;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 行数据流工具类
 *
 * @param <R>
 */
public class ZsrRowStreamUtil<R> {

    /**
     * DataTable wrapper to provide fluent stream operations with case insensitive support
     */
    public static class DataTableWrapper {
        private final List<CaseInsensitiveRowWrapper> rows;

        private DataTableWrapper(DataTable dataTable) {
            this.rows = dataTable.rows.stream().map(CaseInsensitiveRowWrapper::new)
                    .collect(Collectors.toList());
        }

        public DataTableWrapper forEach(Consumer<CaseInsensitiveRowWrapper> action) {
            rows.forEach(action);
            return this;
        }

        public Stream<CaseInsensitiveRowWrapper> stream() {
            return rows.stream();
        }
    }

    /**
     * Create a fluent wrapper for DataTable with case insensitive support
     */
    public static DataTableWrapper wrap(DataTable dataTable) {
        return new DataTableWrapper(dataTable);
    }


    /**
     * 将 DTO 对象列表转换为 CaseInsensitiveRowWrapper 对象列表
     *
     * @param dtoList DTO 对象列表
     * @param <T>     DTO 对象的类型
     * @return CaseInsensitiveRowWrapper 对象列表
     */
    public static <T> List<CaseInsensitiveRowWrapper> wrap(List<T> dtoList) {
        if (dtoList == null) {
            return java.util.Collections.emptyList(); // 或者可以返回 null，取决于您的需求
        }
        return dtoList.stream()
                .map(Zsr.DTOConverter::convertDTOToMap) // 使用 DTOConverter 转换为 Map
                .map(CaseInsensitiveRowWrapper::new)     // 使用 Map 创建 CaseInsensitiveRowWrapper
                .collect(Collectors.toList());
    }
}
