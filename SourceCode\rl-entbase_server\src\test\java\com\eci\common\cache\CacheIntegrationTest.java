package com.eci.common.cache;

import com.eci.common.cache.adapter.CacheAdapter;
import com.eci.common.cache.config.CacheConfigDetector;
import com.eci.common.cache.factory.CacheFactory;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.condition.EnabledIfEnvironmentVariable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;

import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 缓存系统集成测试
 * 需要真实的 Redis 环境来运行完整测试
 */
@SpringBootTest
@ActiveProfiles("test")
public class CacheIntegrationTest {

    @Autowired(required = false)
    private CacheFactory cacheFactory;

    @Autowired(required = false)
    private CacheConfigDetector configDetector;

    @Test
    void testCacheFactoryInitialization() {
        assertNotNull(cacheFactory, "CacheFactory 应该被正确注入");
        assertNotNull(configDetector, "CacheConfigDetector 应该被正确注入");
    }

    @Test
    void testLocalCacheStrategy() {
        // 如果没有 Redis 配置，应该使用本地缓存
        if (configDetector != null) {
            CacheConfigDetector.CacheStrategy strategy = configDetector.getCurrentStrategy();
            assertNotNull(strategy);
            
            // 创建缓存实例
            CacheAdapter<String, String> cache = cacheFactory.createStringCache("test_local");
            assertNotNull(cache);
            
            // 测试基本功能
            cache.put("test_key", "test_value");
            assertEquals("test_value", cache.get("test_key"));
            
            assertTrue(cache.isAvailable());
        }
    }

    @Test
    @EnabledIfEnvironmentVariable(named = "REDIS_AVAILABLE", matches = "true")
    @TestPropertySource(properties = {
        "spring.redis.host=localhost",
        "spring.redis.port=6379",
        "cache.strategy=redis"
    })
    void testRedisCacheStrategy() {
        // 这个测试只在 Redis 可用时运行
        if (configDetector != null) {
            // 强制重新检测配置
            configDetector.forceRedetect();
            
            CacheConfigDetector.CacheStrategy strategy = configDetector.getCurrentStrategy();
            
            if (strategy == CacheConfigDetector.CacheStrategy.REDIS) {
                assertTrue(configDetector.isRedisAvailable());
                
                // 创建 Redis 缓存实例
                CacheAdapter<String, String> cache = cacheFactory.createStringCache("test_redis");
                assertNotNull(cache);
                assertEquals(CacheAdapter.CacheType.REDIS, cache.getType());
                
                // 测试 Redis 特有功能
                cache.put("redis_key", "redis_value", 30, TimeUnit.SECONDS);
                assertEquals("redis_value", cache.get("redis_key"));
                
                // 测试过期时间
                long ttl = cache.getExpire("redis_key", TimeUnit.SECONDS);
                assertTrue(ttl > 0 && ttl <= 30);
                
                assertTrue(cache.isAvailable());
            }
        }
    }

    @Test
    void testCacheFallback() {
        if (cacheFactory != null) {
            // 创建缓存实例
            CacheAdapter<String, String> cache = cacheFactory.createStringCache("test_fallback");
            
            // 无论使用哪种策略，基本功能都应该工作
            cache.put("fallback_key", "fallback_value");
            assertEquals("fallback_value", cache.get("fallback_key"));
            
            // 测试批量操作
            cache.putAll(java.util.Map.of(
                "key1", "value1",
                "key2", "value2",
                "key3", "value3"
            ));
            
            assertEquals("value1", cache.get("key1"));
            assertEquals("value2", cache.get("key2"));
            assertEquals("value3", cache.get("key3"));
            
            // 测试删除操作
            assertTrue(cache.remove("key1"));
            assertNull(cache.get("key1"));
            
            // 测试清空操作
            cache.clear();
            assertNull(cache.get("key2"));
            assertNull(cache.get("key3"));
        }
    }

    @Test
    void testConfigurationSwitching() {
        if (configDetector != null && cacheFactory != null) {
            // 记录初始策略
            CacheConfigDetector.CacheStrategy initialStrategy = configDetector.getCurrentStrategy();
            
            // 创建缓存实例
            CacheAdapter<String, String> cache1 = cacheFactory.createStringCache("test_switch_1");
            cache1.put("switch_key", "switch_value");
            
            // 强制重新检测配置
            configDetector.forceRedetect();
            
            // 创建新的缓存实例
            CacheAdapter<String, String> cache2 = cacheFactory.createStringCache("test_switch_2");
            cache2.put("switch_key2", "switch_value2");
            
            // 验证两个缓存都能正常工作
            assertEquals("switch_value", cache1.get("switch_key"));
            assertEquals("switch_value2", cache2.get("switch_key2"));
        }
    }

    @Test
    void testCacheStatistics() {
        if (cacheFactory != null) {
            CacheAdapter<String, String> cache = cacheFactory.createStringCache("test_stats");
            
            // 执行一些操作
            cache.put("stats1", "value1");
            cache.put("stats2", "value2");
            cache.get("stats1"); // 命中
            cache.get("nonexistent"); // 未命中
            
            // 获取统计信息
            CacheAdapter.CacheStats stats = cache.getStats();
            assertNotNull(stats);
            
            // 验证统计数据
            assertTrue(stats.getHitCount() >= 0);
            assertTrue(stats.getMissCount() >= 0);
            assertTrue(stats.getSize() >= 0);
            
            // 获取工厂统计信息
            String factoryStats = cacheFactory.getCacheStats();
            assertNotNull(factoryStats);
            assertTrue(factoryStats.contains("缓存工厂统计信息"));
        }
    }

    @Test
    void testConcurrentAccess() throws InterruptedException {
        if (cacheFactory != null) {
            CacheAdapter<String, String> cache = cacheFactory.createStringCache("test_concurrent");
            
            int threadCount = 5;
            int operationsPerThread = 100;
            Thread[] threads = new Thread[threadCount];
            
            // 创建并发访问线程
            for (int i = 0; i < threadCount; i++) {
                final int threadId = i;
                threads[i] = new Thread(() -> {
                    for (int j = 0; j < operationsPerThread; j++) {
                        String key = "concurrent_" + threadId + "_" + j;
                        String value = "value_" + threadId + "_" + j;
                        
                        cache.put(key, value);
                        assertEquals(value, cache.get(key));
                    }
                });
            }
            
            // 启动所有线程
            for (Thread thread : threads) {
                thread.start();
            }
            
            // 等待所有线程完成
            for (Thread thread : threads) {
                thread.join();
            }
            
            // 验证缓存状态
            assertTrue(cache.size() > 0);
            assertTrue(cache.isAvailable());
        }
    }

    @Test
    void testCacheExpiration() throws InterruptedException {
        if (cacheFactory != null) {
            CacheAdapter<String, String> cache = cacheFactory.createStringCache("test_expiration");
            
            // 设置短过期时间的缓存项
            cache.put("expire_key", "expire_value", 1, TimeUnit.SECONDS);
            assertEquals("expire_value", cache.get("expire_key"));
            
            // 等待过期
            Thread.sleep(1500);
            
            // 验证已过期
            String value = cache.get("expire_key");
            // 注意：本地缓存可能不会立即清理过期项，所以这里只是验证功能
            // Redis 缓存应该返回 null
            if (cache.getType() == CacheAdapter.CacheType.REDIS) {
                assertNull(value, "Redis 缓存项应该已过期");
            }
        }
    }

    @Test
    void testCacheTypeConsistency() {
        if (cacheFactory != null && configDetector != null) {
            CacheAdapter<String, String> cache = cacheFactory.createStringCache("test_type");
            
            CacheConfigDetector.CacheStrategy strategy = configDetector.getCurrentStrategy();
            CacheAdapter.CacheType expectedType;
            
            switch (strategy) {
                case REDIS:
                    expectedType = CacheAdapter.CacheType.REDIS;
                    break;
                case LOCAL:
                default:
                    expectedType = CacheAdapter.CacheType.LOCAL;
                    break;
            }
            
            assertEquals(expectedType, cache.getType(), 
                    "缓存类型应该与配置策略一致");
        }
    }

    @Test
    void testErrorHandling() {
        if (cacheFactory != null) {
            CacheAdapter<String, String> cache = cacheFactory.createStringCache("test_error");
            
            // 测试 null 值处理
            cache.put(null, "value"); // 应该被忽略
            cache.put("key", null); // 应该被忽略
            
            assertNull(cache.get(null));
            assertNull(cache.get("key"));
            
            // 测试不存在的键
            assertNull(cache.get("nonexistent_key"));
            assertFalse(cache.containsKey("nonexistent_key"));
            
            // 测试删除不存在的键
            assertFalse(cache.remove("nonexistent_key"));
        }
    }

    @Test
    void testCacheFactoryManagement() {
        if (cacheFactory != null) {
            // 测试缓存实例管理
            CacheAdapter<String, String> cache1 = cacheFactory.createStringCache("management_test");
            CacheAdapter<String, String> cache2 = cacheFactory.createStringCache("management_test");
            
            // 相同名称应该返回相同实例
            assertSame(cache1, cache2, "相同名称的缓存应该返回相同实例");
            
            // 测试缓存存在检查
            assertTrue(cacheFactory.cacheExists("management_test", String.class, String.class));
            assertFalse(cacheFactory.cacheExists("nonexistent", String.class, String.class));
            
            // 测试获取所有缓存名称
            assertTrue(cacheFactory.getAllCacheNames().size() > 0);
        }
    }
}
